{
  // "extends": "next/core-web-vitals",
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react-hooks/recommended"
    // "prettier"
  ],
  "plugins": ["react-refresh", "@typescript-eslint"],
  "parser": "@typescript-eslint/parser",
  "ignorePatterns": [
    "*.json",
    "*.mjs",
    "*.config.js",
    "*.config.ts",
    "*.config.cjs",
    "*.env",
    "*.d.ts",
    "*.yaml",
    "node_modules",
    "public",
    ".next",
    ".github"
  ],
  "rules": {
    "react-refresh/only-export-components": [
      "warn",
      { "allowConstantExport": true }
    ],
    "indent": ["error", "tab"],
    "switch-colon-spacing": ["error", { "after": true, "before": false }],
    "keyword-spacing": ["error", { "before": true }],
    "object-curly-spacing": [
      "error",
      "always",
      { "arraysInObjects": false, "objectsInObjects": false }
    ],
    "key-spacing": ["error", { "beforeColon": false, "afterColon": true }],
    "arrow-spacing": ["error", { "before": true, "after": true }],
    "comma-spacing": ["error", { "before": false, "after": true }],
    "eol-last": ["error", "always"],
    "@typescript-eslint/no-explicit-any": "warn",
    "no-console": "warn",
    "no-trailing-spaces": "error"
  }
}
