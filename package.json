{"name": "eticketnepal-backend-web-v2", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "NODE_ENV=development tsnd --respawn --transpile-only -r tsconfig-paths/register src/index.ts", "lint": "eslint ./src --ext .ts --fix", "seed:create": "pnpx sequelize-cli seed:generate --name", "seed:reset": "pnpx sequelize-cli db:seed:undo:all", "seed:refresh": "pnpm seed:reset && pnpm seed", "seed": "pnpx sequelize-cli db:seed:all", "migrate:create": "pnpx sequelize-cli migration:create --name", "migrate": "pnpx sequelize-cli db:migrate", "migrate:rollback": "pnpx sequelize-cli db:migrate:undo", "rollback:migration": "pnpx sequelize-cli db:migrate:undo", "migrate:reset": "pnpx sequelize-cli db:migrate:undo:all", "migrate:refresh": "pnpm migrate:reset && pnpm migrate", "build": "NODE_ENV=production rimraf dist && tsc -p . && cp -r src/templates dist/src/templates", "start": "NODE_ENV=production node dist/src/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/qrcode": "^1.5.5", "aws-sdk": "^2.1692.0", "axios": "^1.8.2", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "envalid": "^8.0.0", "exceljs": "^4.4.0", "express": "^4.19.2", "express-validator": "^7.2.0", "handlebars": "^4.7.8", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "1.4.5-lts.1", "pg": "^8.12.0", "pg-hstore": "^2.3.4", "postmark": "^4.0.5", "puppeteer": "^24.3.1", "qrcode": "^1.5.4", "rimraf": "^6.0.1", "sequelize": "^6.37.3", "sequelize-typescript": "^2.1.6", "winston": "^3.14.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/bcrypt": "^5.0.2", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.7", "@types/klaw-sync": "^6.0.5", "@types/multer": "^1.4.12", "@types/node": "^22.5.0", "eslint": "^9.9.1", "globals": "^15.9.0", "klaw-sync": "^6.0.0", "module-alias": "^2.2.3", "sequelize-cli": "^6.6.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.4", "typescript-eslint": "^8.3.0"}, "_moduleAliases": {"@": "dist/src"}, "pnpm": {"onlyBuiltDependencies": ["aws-sdk", "bcrypt", "es5-ext", "puppeteer"]}}