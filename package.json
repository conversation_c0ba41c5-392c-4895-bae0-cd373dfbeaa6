{"name": "eticketnepal-client-web-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@ant-design/nextjs-registry": "^1.0.2", "@react-google-maps/api": "^2.20.6", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.2.7", "antd": "^5.20.1", "antd-mask-input": "^2.0.7", "axios": "^1.7.3", "bootstrap": "^5.3.3", "change-case": "^5.4.4", "compressorjs": "^1.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "next": "14.2.5", "next-auth": "^4.24.11", "next-nprogress-bar": "^2.3.13", "nprogress": "^0.2.0", "nuka-carousel": "^8.0.1", "placeholder-loading": "^0.6.0", "pluralize": "^8.0.0", "react": "^18", "react-dom": "^18", "react-google-places-autocomplete": "^4.1.0", "react-icons": "^5.5.0", "react-image-crop": "^11.0.7", "react-image-lightbox": "^5.1.4", "react-imask": "^7.6.1", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^9.1.2", "react-share": "^5.1.0", "reactstrap": "^9.2.2", "sass": "^1.77.8", "uuid": "^11.0.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.12", "@types/node": "^20", "@types/pluralize": "^0.0.33", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "eslint": "^8", "eslint-config-next": "14.2.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "pnpm": {"onlyBuiltDependencies": ["core-js-pure"]}}