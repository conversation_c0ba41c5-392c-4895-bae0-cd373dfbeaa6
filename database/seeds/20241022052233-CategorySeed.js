'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface) {
		const transaction = await queryInterface.sequelize.transaction();

		try {
			await queryInterface.bulkDelete('categories', null, { transaction });

			// Categories for Event App Type
			const categories = [
				{
					name: 'Corporate Events',
					slug: 'corporate-events',
					status: true,
					is_main: true,
					app_type: 1,
					icon_name: 'fas fa-briefcase',
					subcategories: [
						'Conferences',
						'Seminars',
						'Workshops',
						'Product Launches',
						'Trade Shows and Expos',
						'Business Meetings',
						'Networking Events',
						'Corporate Retreats'
					]
				},
				{
					name: 'Social Events',
					slug: 'social-events',
					status: true,
					is_main: true,
					app_type: 1,
					icon_name: 'fas fa-users',
					subcategories: [
						'Weddings',
						'Anniversaries',
						'Birthday Parties',
						'Baby Showers',
						'Family Reunions',
						'Graduations',
						'Holiday Parties'
					]
				},
				{
					name: 'Cultural and Community Events',
					slug: 'cultural-community-events',
					status: true,
					is_main: true,
					app_type: 1,
					icon_name: 'fas fa-globe',
					subcategories: [
						'Festivals',
						'Fairs',
						'Parades',
						'Cultural Celebrations',
						'Religious Ceremonies',
						'Fundraising and Charity Events',
						'Carnivals'
					]
				},
				{
					name: 'Entertainment Events',
					slug: 'entertainment-events',
					status: true,
					is_main: true,
					app_type: 1,
					icon_name: 'fas fa-film',
					subcategories: [
						'Concerts',
						'Theater Performances',
						'Movie Screenings',
						'Comedy Shows',
						'Award Shows',
						'Fashion Shows',
						'Talent Shows'
					]
				},
				{
					name: 'Sports Events',
					slug: 'sports-events',
					status: true,
					is_main: true,
					app_type: 1,
					icon_name: 'fas fa-futbol',
					subcategories: [
						'Tournaments',
						'Marathons',
						'Competitions',
						'Team Sports Matches',
						'eSports Competitions',
						'Exhibitions and Demonstrations'
					]
				},
				{
					name: 'Educational Events',
					slug: 'educational-events',
					status: true,
					is_main: true,
					app_type: 1,
					icon_name: 'fas fa-graduation-cap',
					subcategories: [
						'Workshops',
						'Webinars',
						'Training Sessions',
						'Lectures',
						'Career Fairs',
						'Symposiums',
						'Panel Discussions'
					]
				},
				{
					name: 'Political Events',
					slug: 'political-events',
					status: true,
					is_main: true,
					app_type: 1,
					icon_name: 'fas fa-landmark',
					subcategories: [
						'Political Rallies',
						'Campaign Events',
						'Public Debates',
						'Town Halls',
						'Policy Forums'
					]
				},
				{
					name: 'Virtual Events',
					slug: 'virtual-events',
					status: true,
					is_main: true,
					app_type: 1,
					icon_name: 'fas fa-video',
					subcategories: [
						'Online Webinars',
						'Virtual Conferences',
						'Online Workshops',
						'Live Streaming Events',
						'Virtual Networking Sessions'
					]
				},
				{
					name: 'Private Events',
					slug: 'private-events',
					status: true,
					is_main: true,
					app_type: 1,
					icon_name: 'fas fa-lock',
					subcategories: [
						'VIP Dinners',
						'Exclusive Launches',
						'Private Showcases',
						'Invite-only Gatherings'
					]
				}
			];

			// Insert main categories and their subcategories
			for (const category of categories) {
				const { subcategories, ...mainCategory } = category;
				const [result] = await queryInterface.bulkInsert('categories', [mainCategory], {
					returning: true,
					transaction
				});

				if (subcategories && subcategories.length > 0) {
					const subCategoriesData = subcategories.map(name => ({
						name,
						slug: name.toLowerCase().replace(/\s+/g, '-'),
						status: true,
						sub_id: result.id,
						app_type: 1
					}));

					await queryInterface.bulkInsert('categories', subCategoriesData, { transaction });
				}
			}

			// Commit transaction
			await transaction.commit();

		} catch (error) {
			await transaction.rollback();
			throw error;
		}
	},

	async down (queryInterface) {
		// await queryInterface.sequelize.query('TRUNCATE TABLE categories RESTART IDENTITY CASCADE;');
		await queryInterface.bulkDelete('categories', null, {
			truncate: true,
			restartIdentity: true,
			cascade: true,
		});
	}
};
