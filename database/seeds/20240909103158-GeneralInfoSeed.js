'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface) {
		await queryInterface.insert(null, 'general_infos', {
			name: 'eticketnepal',
			address_one: 'Kamaladi, Kathmandu, Nepal',
			mobile_one: '9810057317',
			phone_one: '012345678',
			site_url: 'https://www.eticketnepal.com',
			copyright: '© ' + new Date().getFullYear() + ' eticketnepal.com - All Rights Reserved.',
			yt_channel: 'https://www.youtube.com/eticketnepal',
			facebook: 'https://www.facebook.com/eticketnepal',
			meta_desc: 'Eticketnepal offers event tickets, reviews, trailers, concert tickets and events near Kathmandu. Online events ticket booking through various nepali payment gateway.',
			meta_keywords: 'EventResource Ticket Bookings, EventResource Tickets, Events in Nepal, Events Online Booking, Concert, Play, EventResource, Comedy Show Tickets, Cricket match Tickets, Reviews',
			email_one: '',
		}, {});
	},

	async down (queryInterface) {
		await queryInterface.bulkDelete('general_infos', null, {});
	}
};
