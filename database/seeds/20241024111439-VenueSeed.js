'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface) {
		const venues = [
			{
				name: 'Bhrikutimandap Exhibition Hall',
				slug: 'bhrikutimandap-exhibition-hall',
				status: true,
			},
			{
				name: 'The Everest Hotel',
				slug: 'the-everest-hotel',
				status: true,
			},
			{
				name: 'Hotel Yak and Yeti',
				slug: 'hotel-yak-and-yeti',
				status: true,
			},
			{
				name: 'Hotel Nirvana',
				slug: 'hotel-nirvana',
				status: true,
			},
		];

		await queryInterface.bulkInsert('venues', venues, {});
	},

	async down (queryInterface) {
		await queryInterface.bulkDelete('venues', null, {
			truncate: true,
			restartIdentity: true,
			cascade: true,
		});
	}
};
