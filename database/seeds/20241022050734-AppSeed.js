'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface) {
		await queryInterface.bulkInsert('apps', [
			{ name: 'Events' },
			{ name: 'Movies' },
			{ name: 'Travels' },
			{ name: 'Venues' },
		], {});
	},

	async down (queryInterface) {
		await queryInterface.bulkDelete('apps', null, {
			truncate: true,
			restartIdentity: true,
			cascade: true
		});
	}
};
