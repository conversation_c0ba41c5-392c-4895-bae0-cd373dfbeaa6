'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'general_infos', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				logo: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				name: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				app_info: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				address_one: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				address_two: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				phone_one: {
					type: Sequelize.STRING,
					allowNull: true,
					unique: true,
				},
				phone_two: {
					type: Sequelize.STRING,
					allowNull: true,
					unique: true,
				},
				mobile_one: {
					type: Sequelize.STRING,
					allowNull: true,
					unique: true,
				},
				mobile_two: {
					type: Sequelize.STRING,
					allowNull: true,
					unique: true,
				},
				site_url: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				email_one: {
					type: Sequelize.STRING,
					allowNull: true,
					unique: true,
				},
				email_two: {
					type: Sequelize.STRING,
					allowNull: true,
					unique: true,
				},
				copyright: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				yt_channel: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				facebook: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				twitter: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				linkedin: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				instagram: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				google: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				pinterest: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				meta_desc: {
					type: Sequelize.TEXT,
					allowNull: true,
				},
				meta_keywords: {
					type: Sequelize.TEXT,
					allowNull: true,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('general_infos');
	}
};
