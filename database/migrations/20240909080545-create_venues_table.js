'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'venues', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				name: {
					type: Sequelize.STRING,
					allowNull: false,
					unique: true,
				},
				slug: {
					type: Sequelize.STRING,
					allowNull: false,
					unique: true,
				},
				logo: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				image: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				city_id: {
					type: Sequelize.INTEGER,
					allowNull: true,
					references: {
						model: 'cities',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				facebook: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				youtube: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				twitter: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				instagram: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				lng: {
					type: Sequelize.DECIMAL(11, 8),
					allowNull: true,
				},
				lat: {
					type: Sequelize.DECIMAL(10, 8),
					allowNull: true,
				},
				map_link: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				contact_info: {
					type: Sequelize.JSON,
					allowNull: true,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				},
				about: {
					type: Sequelize.TEXT,
					allowNull: true,
				},
				attractions: {
					type: Sequelize.TEXT,
					allowNull: true,
				},
				meta_keywords: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				meta_desc: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				deleted_at: {
					type: Sequelize.DATE,
					allowNull: true,
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('venues');
	}
};
