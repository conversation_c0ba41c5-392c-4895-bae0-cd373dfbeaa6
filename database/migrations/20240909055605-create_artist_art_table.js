'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'artist_art', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				artist_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'artists',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				art_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'arts',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('artist_art');
	}
};
