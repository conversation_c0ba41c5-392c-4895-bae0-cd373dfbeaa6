'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'booking_inquiries',
			{
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				travel_id: {
					type: Sequelize.BIGINT,
					allowNull: false,
					references: {
						model: 'travels',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				user_id: {
					type: Sequelize.BIGINT,
					allowNull: true,
					references: {
						model: 'users',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				full_name: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				email: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				phone: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				message: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				seen: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				proceed: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('booking_inquiries');
	}
};
