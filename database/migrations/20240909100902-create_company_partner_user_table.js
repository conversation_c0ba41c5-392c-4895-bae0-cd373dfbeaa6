'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'company_partner_user', {
				id: {
					type: Sequelize.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				company_user_id: {
					type: Sequelize.BIGINT,
					allowNull: false,
					references: {
						model: 'company_user',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				partner_id: {
					type: Sequelize.BIGINT,
					allowNull: false,
					references: {
						model: 'partner_types',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('company_partner_user');
	}
};
