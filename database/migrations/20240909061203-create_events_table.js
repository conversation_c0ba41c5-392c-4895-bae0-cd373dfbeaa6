'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();
		try {
			await queryInterface.createTable(
				'events', {
					id: {
						type: Sequelize.INTEGER,
						autoIncrement: true,
						primaryKey: true,
					},
					company_id: {
						type: Sequelize.BIGINT,
						allowNull: true,
						references: {
							model: 'companies',
							key: 'id',
						},
						validate: {
							min: 1,
						},
						onDelete: 'CASCADE',
						onUpdate: 'CASCADE',
					},
					title: {
						type: Sequelize.STRING,
						allowNull: true,
						unique: true,
					},
					slug: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					duration: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					main_attraction: {
						type: Sequelize.TEXT,
						allowNull: true,
					},
					image: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					about: {
						type: Sequelize.TEXT,
						allowNull: true,
					},
					status: {
						type: Sequelize.BOOLEAN,
						allowNull: false,
						defaultValue: true,
					},
					feature_from: {
						type: Sequelize.DATE,
						allowNull: true,
					},
					feature_expiry: {
						type: Sequelize.DATE,
						allowNull: true,
					},
					meta_keywords: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					meta_desc: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					created_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
					updated_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
					published_at: {
						type: Sequelize.DATE,
						allowNull: true,
					},
					deleted_at: {
						type: Sequelize.DATE,
						allowNull: true,
					},
				},
				{ transaction }
			);
			await queryInterface.addIndex('events', ['slug', 'title'],
				{ transaction }
			);
			await transaction.commit();
		} catch (e) {
			await transaction.rollback();
			throw e;
		}
	},

	async down (queryInterface) {
		await queryInterface.dropTable('events');
	}
};
