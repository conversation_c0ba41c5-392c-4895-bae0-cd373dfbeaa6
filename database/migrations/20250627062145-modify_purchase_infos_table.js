'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.addColumn('purchase_infos', 'ticket_buyer_id', {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'ticket_buyers',
					key: 'id'
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			}, { transaction });

			await queryInterface.addColumn('purchase_infos', 'is_scan_success', {
				type: Sequelize.BOOLEAN,
				allowNull: false,
				defaultValue: false,
			}, { transaction });

			await queryInterface.addColumn('purchase_infos', 'paid_amount', {
				type: Sequelize.DECIMAL,
				allowNull: true,
			}, { transaction });

			await queryInterface.removeColumn('purchase_infos', 'ticket_count', { transaction });

			await queryInterface.removeColumn('purchase_infos', 'merchant_payload', { transaction });
		});
	},

	async down (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.removeColumn('purchase_infos', 'ticket_buyer_id', { transaction });

			await queryInterface.removeColumn('purchase_infos', 'is_scan_success', { transaction });

			await queryInterface.removeColumn('purchase_infos', 'paid_amount', { transaction });

			await queryInterface.addColumn('purchase_infos', 'ticket_count', {
				type: Sequelize.INTEGER,
				allowNull: true,
				validate: {
					min: 1,
				},
			}, { transaction });

			await queryInterface.addColumn('purchase_infos', 'merchant_payload', {
				type: Sequelize.JSON,
				allowNull: true,
			}, { transaction });
		});
	}
};
