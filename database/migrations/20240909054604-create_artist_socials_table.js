'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'artist_socials', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				artist_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'artists',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				social_vendor: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				link: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				}
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('artist_socials');
	}
};
