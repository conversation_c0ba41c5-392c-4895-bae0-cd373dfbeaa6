'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.addColumn('venues', 'coordinates', {
			type: Sequelize.GEOMETRY('POINT', 4326),
			allowNull: true,
		});
		await queryInterface.addColumn('venues', 'address', {
			type: Sequelize.JSON,
			allowNull: true,
		});
	},

	async down (queryInterface) {
		await queryInterface.removeColumn('venues', 'coordinates');
		await queryInterface.removeColumn('venues', 'address');
	}
};
