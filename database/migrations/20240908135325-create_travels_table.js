'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'travels', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				company_id: {
					type: Sequelize.BIGINT,
					allowNull: true,
					references: {
						model: 'companies',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				category_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'categories',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				title: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				slug: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				details: {
					type: Sequelize.TEXT,
					allowNull: false,
				},
				contact_info: {
					type: Sequelize.JSON,
					allowNull: true,
				},
				meta_keywords: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				meta_desc: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				currency: {
					type: Sequelize.CHAR(3),
					allowNull: false,
				},
				price: {
					type: Sequelize.DECIMAL(20, 2),
					allowNull: false,
				},
				scheme: {
					type: Sequelize.INTEGER,
					allowNull: true,
					validate: {
						min: 0,
					},
					comment: 'if there is any discount scheme provided in percentage'
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				image: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('travels');
	}
};
