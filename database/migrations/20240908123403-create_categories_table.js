'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();
		try {
			await queryInterface.createTable(
				'categories', {
					id: {
						type: Sequelize.INTEGER,
						autoIncrement: true,
						primaryKey: true,
					},
					sub_id: {
						type: Sequelize.INTEGER,
						allowNull: true,
						references: {
							model: 'categories',
							key: 'id',
						},
						validate: {
							min: 1,
						},
						onDelete: 'CASCADE',
						onUpdate: 'CASCADE',
					},
					app_type: {
						type: Sequelize.INTEGER,
						allowNull: true,
						references: {
							model: 'apps',
							key: 'id',
						},
						validate: {
							min: 1,
						},
						onDelete: 'SET NULL',
						onUpdate: 'CASCADE',
					},
					name: {
						type: Sequelize.STRING,
						allowNull: false,
					},
					slug: {
						type: Sequelize.STRING,
						allowNull: false,
					},
					info: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					image: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					icon_name: {
						type: Sequelize.STRING(50),
						allowNull: true,
					},
					meta_keywords: {
						type: Sequelize.TEXT,
						allowNull: true,
					},
					meta_description: {
						type: Sequelize.TEXT,
						allowNull: true,
					},
					status: {
						type: Sequelize.BOOLEAN,
						allowNull: false,
						defaultValue: true,
					},
					order: {
						type: Sequelize.INTEGER,
						allowNull: true,
					},
					marked_as_new: {
						type: Sequelize.SMALLINT,
						allowNull: true,
						comment: 'Place 1 is the category has to be marked as new'
					},
					navigation_list: {
						type: Sequelize.SMALLINT,
						allowNull: true,
						comment: 'Place 1 to the categories to list them on the menu'
					},
					is_main: {
						type: Sequelize.BOOLEAN,
						allowNull: true,
						comment: 'Place 1 if the category has to be marked as main'
					},
					created_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
					updated_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
				},
				{ transaction }
			);
			await queryInterface.addIndex('categories', ['sub_id', 'slug'],
				{ transaction }
			);
			await transaction.commit();
		} catch (e) {
			await transaction.rollback();
			throw e;
		}
	},

	async down (queryInterface) {
		await queryInterface.dropTable('categories');
	}
};
