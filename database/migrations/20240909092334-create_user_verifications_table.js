'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();
		try {
			await queryInterface.createTable(
				'user_verifications', {
					id: {
						type: Sequelize.BIGINT,
						autoIncrement: true,
						primaryKey: true,
					},
					verification_type: {
						type: Sequelize.SMALLINT,
						allowNull: false,
						comment: '1 is email 0 is phone'
					},
					user: {
						type: Sequelize.STRING,
						allowNull: false,
					},
					token: {
						type: Sequelize.STRING,
						allowNull: false,
					},
					expired: {
						type: Sequelize.BOOLEAN,
						allowNull: false,
						defaultValue: false,
					},
					created_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
					updated_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
				},
				{ transaction }
			);
			await queryInterface.addIndex('user_verifications', ['user'],
				{ transaction }
			);
			await transaction.commit();
		} catch (e) {
			await transaction.rollback();
			throw e;
		}
	},

	async down (queryInterface) {
		await queryInterface.dropTable('user_verifications');
	}
};
