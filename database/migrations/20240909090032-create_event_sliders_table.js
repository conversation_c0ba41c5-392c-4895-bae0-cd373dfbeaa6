'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'event_sliders', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				event_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'events',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				from: {
					type: Sequelize.DATE,
					allowNull: true,
				},
				to: {
					type: Sequelize.DATE,
					allowNull: true,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('event_sliders');
	}
};
