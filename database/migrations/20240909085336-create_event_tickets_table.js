'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'event_tickets', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				event_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'events',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				venue_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'venues',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				event_venue_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'event_venue',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				name: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				ticket_type: {
					type: Sequelize.ENUM('free', 'paid'),
					allowNull: false,
				},
				number_of_tickets: {
					type: Sequelize.INTEGER,
					allowNull: true,
					validate: {
						min: 0,
					},
				},
				price: {
					type: Sequelize.DECIMAL,
					allowNull: true,
					validate: {
						min: 0,
					},
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('event_tickets');
	}
};
