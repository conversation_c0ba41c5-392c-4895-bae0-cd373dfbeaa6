'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'admin_socials', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				admin_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'admins',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				facebook: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				twitter: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				linkedin: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				google: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				pinterest: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('admin_socials');
	}
};
