'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'purchase_infos', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				event_venue_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'event_venue',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				ticket_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'event_tickets',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				ticket_count: {
					type: Sequelize.INTEGER,
					allowNull: false,
					validate: {
						min: 1,
					},
				},
				date_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					validate: {
						min: 1,
					},
				},
				user_id: {
					type: Sequelize.INTEGER,
					allowNull: true,
					references: {
						model: 'users',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				date_time_id: {
					type: Sequelize.INTEGER,
					allowNull: true,
					references: {
						model: 'event_date_times',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('purchase_infos');
	}
};
