'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'event_faq',
			{
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				event_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'events',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				question: {
					type: Sequelize.STRING(1000),
					allowNull: false,
				},
				answer: {
					type: Sequelize.TEXT,
					allowNull: true,
				},
				suggest: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
					comment: 'if 1 this will be listed as the suggetion when creating faq'
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('event_faq');
	}
};
