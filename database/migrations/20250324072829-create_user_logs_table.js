'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		/**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
		await queryInterface.createTable(
			'user_logs', {
				id: {
					type: Sequelize.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				user_id: {
					allowNull: true,
					type: Sequelize.INTEGER,
					references: {
						model: 'users',
						key: 'id'
					},
					onDelete: 'cascade',
					onUpdate: 'cascade'
				},
				company_id: {
					allowNull: true,
					type: Sequelize.INTEGER,
				},
				event_type: {
					allowNull: true,
					type: Sequelize.STRING,
				},
        
				type: {
					allowNull: true,
					index: true,
					type: Sequelize.BOOLEAN,
					// ('1 is success 0 is fail');
				},
				details: {
					allowNull: true,
					index: true,
					type: Sequelize.TEXT,
					// Details about the action created
				},
				action: {
					allowNull: true,
					index: true,
					type: Sequelize.STRING,
				},
				payloads: {
					allowNull: true,
					type: Sequelize.JSON,
					// Details about the action created
				},
				ip_address: {
					allowNull: true,
					index: true,
					type: Sequelize.STRING(45),
				},
				user_agent: {
					allowNull: true,
					index: true,
					type: Sequelize.STRING,
				},
				read_access: {
					index: true,
					defaultValue: false,
					type: Sequelize.BOOLEAN,
					// 0 no read access, 1 read access permitted
				},
				referrer: {
					allowNull: true,
					type: Sequelize.TEXT,
				},
				method: {
					allowNull: true,
					index: true,
					type: Sequelize.STRING(10),
				},
				api_endpoint: {
					allowNull: true,
					type: Sequelize.TEXT,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		/**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
		await queryInterface.dropTable('user_logs');

	}
};
