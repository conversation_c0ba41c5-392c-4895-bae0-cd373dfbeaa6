'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'theatres', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true
				},
				city_id: {
					type: Sequelize.INTEGER,
					allowNull: true,
					references: {
						model: 'cities',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'SET NULL',
					onUpdate: 'CASCADE',
				},
				name: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				location: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				site: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				lon: {
					type: Sequelize.STRING(15),
					allowNull: true,
				},
				lat: {
					type: Sequelize.STRING(15),
					allowNull: true,
				},
				food_beverage: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
				},
				m_ticket: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false
				},
				meta_keywords: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				meta_desc: {
					type: Sequelize.STRING,
					allowNull: true,
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('theatres');
	}
};
