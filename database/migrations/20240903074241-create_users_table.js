'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();
		try {
			await queryInterface.createTable(
				'users', {
					id: {
						type: Sequelize.INTEGER,
						autoIncrement: true,
						primaryKey: true,
					},
					f_name: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					l_name: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					email: {
						type: Sequelize.STRING,
						allowNull: true,
						unique: true,
					},
					// Null email if the user is being logged from social accounts
					phone: {
						type: Sequelize.STRING,
						unique: true,
						allowNull: true,
					},
					image: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					ip_address: {
						type: Sequelize.INET,
						allowNull: true,
					},
					dob: {
						type: Sequelize.DATEONLY,
						allowNull: true,
					},
					gender: {
						type: Sequelize.ENUM('male', 'female', 'others'),
						allowNull: true,
					},
					password: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					// Null password if the user is being logged from social accounts
					is_partner: {
						type: Sequelize.BOOLEAN,
						allowNull: true,
						defaultValue: false,
					},
					email_verified_at: {
						type: Sequelize.DATE,
						allowNull: true,
					},
					phone_verified_at: {
						type: Sequelize.DATE,
						allowNull: true,
					},
					verification_code: {
						type: Sequelize.STRING(6),
						allowNull: true,
					},
					action_link_sent_at: {
						type: Sequelize.DATE,
						allowNull: true,
					},
					created_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
					updated_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
				},
				{ transaction }
			);
			await queryInterface.addIndex('users', ['is_partner'],
				{ transaction }
			);
			await transaction.commit();
		} catch (e) {
			await transaction.rollback();
			throw e;
		}
	},

	async down (queryInterface) {
		await queryInterface.dropTable('users');
	}
};
