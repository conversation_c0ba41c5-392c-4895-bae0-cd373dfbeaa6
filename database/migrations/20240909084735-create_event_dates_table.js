'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'event_dates', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				event_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'events',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				venue_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'venues',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				event_venue_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'event_venue',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				date: {
					type: Sequelize.DATE,
					allowNull: false,
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('event_dates');
	}
};
