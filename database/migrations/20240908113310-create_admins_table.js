'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'admins', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				uname: {
					type: Sequelize.STRING,
					allowNull: false,
					unique: true,
				},
				email: {
					type: Sequelize.STRING,
					allowNull: false,
					unique: true,
				},
				phone: {
					type: Sequelize.STRING(20),
					allowNull: true,
					unique: true,
				},
				image: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				address: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				gender: {
					type: Sequelize.ENUM('male', 'female', 'others'),
					allowNull: true,
				},
				password: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				designation: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				description: {
					type: Sequelize.TEXT,
					allowNull: true,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('admins');
	}
};
