'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface) {
		await queryInterface.removeColumn('venues', 'lng');
		await queryInterface.removeColumn('venues', 'lat');
	},

	async down (queryInterface, Sequelize) {
		await queryInterface.addColumn('venues', 'lng', {
			type: Sequelize.DECIMAL(11, 8),
			allowNull: true,
		});
		await queryInterface.addColumn('venues', 'lat', {
			type: Sequelize.DECIMAL(10, 8),
			allowNull: true,
		});
	}
};
