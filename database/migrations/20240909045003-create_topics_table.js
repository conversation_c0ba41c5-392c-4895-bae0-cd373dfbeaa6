'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'topics', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: Sequelize.STRING,
					allowNull: false,
					unique: true,
				},
				slug: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				},
				order: {
					type: Sequelize.INTEGER,
					allowNull: true,
					validate: {
						min: 0,
					}
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('topics');
	}
};
