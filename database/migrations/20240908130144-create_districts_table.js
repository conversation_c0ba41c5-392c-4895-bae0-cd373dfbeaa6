'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'districts', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				name: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				local_name: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				dial_code: {
					type: Sequelize.STRING(5),
					allowNull: true,
				},
				head_quarter: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				state_code: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'states',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				is_main: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				}
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('districts');
	}
};
