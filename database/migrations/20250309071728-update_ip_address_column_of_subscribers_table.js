'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.changeColumn('subscribers', 'ip_address', {
			type: Sequelize.INET,
			allowNull: true,
		});
	},

	async down (queryInterface, Sequelize) {
		await queryInterface.changeColumn('subscribers', 'ip_address', {
			type: Sequelize.INET,
			allowNull: false,
		});
	}
};
