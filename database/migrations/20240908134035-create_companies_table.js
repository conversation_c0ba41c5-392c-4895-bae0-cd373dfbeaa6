'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();
		try {
			await queryInterface.createTable(
				'companies', {
					id: {
						type: Sequelize.BIGINT,
						autoIncrement: true,
						primaryKey: true,
					},
					code: {
						type: Sequelize.STRING(32),
						allowNull: false,
						unique: true,
					},
					name: {
						type: Sequelize.STRING,
						allowNull: false,
					},
					domain: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					socials: {
						type: Sequelize.JSON,
						allowNull: true,
					},
					logo: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					slogan: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					phone: {
						type: Sequelize.STRING,
						allowNull: true,
						unique: true,
					},
					email: {
						type: Sequelize.STRING,
						allowNull: true,
						unique: true,
					},
					secondary_contacts: {
						type: Sequelize.JSON,
						allowNull: true,
					},
					address: {
						type: Sequelize.JSON,
						allowNull: true,
					},
					other_information: {
						type: Sequelize.JSON,
						allowNull: true,
					},
					about: {
						type: Sequelize.TEXT,
						allowNull: true,
					},
					status: {
						type: Sequelize.BOOLEAN,
						allowNull: false,
						defaultValue: true,
					},
					created_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
					updated_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
				},
				{ transaction }
			);
			await queryInterface.addIndex('companies', ['name', 'domain', 'phone', 'email'],
				{ transaction }
			);
			await transaction.commit();
		} catch (e) {
			await transaction.rollback();
			throw e;
		}
	},

	async down (queryInterface) {
		await queryInterface.dropTable('companies');
	}
};
