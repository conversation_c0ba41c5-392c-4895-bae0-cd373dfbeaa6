'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'movie_theatre_release_date',
			{
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				movie_theatre_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'movie_theatre',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				release_date: {
					type: Sequelize.DATE,
					allowNull: false,
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('movie_theatre_release_date');
	}
};
