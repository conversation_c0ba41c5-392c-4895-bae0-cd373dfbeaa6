'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'cities', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				city_name: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				local_name: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				district_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'districts',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				is_main: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				icon: {
					type: Sequelize.STRING(20),
					allowNull: true,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				}
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('cities');
	}
};
