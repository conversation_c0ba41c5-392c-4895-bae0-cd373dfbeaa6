'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.addColumn('purchase_infos', 'event_id', {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'events',
					key: 'id',
				},
				validate: {
					min: 1,
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			}, { transaction });

			await queryInterface.addColumn('purchase_infos', 'venue_id', {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'venues',
					key: 'id',
				},
				validate: {
					min: 1,
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			}, { transaction });
      
			await queryInterface.changeColumn('purchase_infos', 'date_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
				validate: {
					min: 1,
				},
			}, { transaction });

			await queryInterface.removeColumn('purchase_infos', 'event_venue_id', { transaction });
		});
	},

	async down (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.removeColumn('purchase_infos', 'event_id', { transaction });

			await queryInterface.removeColumn('purchase_infos', 'venue_id', { transaction });

			await queryInterface.changeColumn('purchase_infos', 'date_id', {
				type: Sequelize.INTEGER,
				allowNull: false,
				validate: {
					min: 1,
				},
			}, { transaction });

			await queryInterface.addColumn('purchase_infos', 'event_venue_id', {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'event_venue',
					key: 'id',
				},
				validate: {
					min: 1,
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			}, { transaction });
		});
	}
};
