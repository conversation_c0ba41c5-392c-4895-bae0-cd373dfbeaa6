'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'privileges', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				privilege_type: {
					type: Sequelize.SMALLINT,
					allowNull: false,
					defaultValue: 3,
					comment: '1 is Developer, 2 is Super Admin, 3 is Admin',
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('privileges');
	}
};
