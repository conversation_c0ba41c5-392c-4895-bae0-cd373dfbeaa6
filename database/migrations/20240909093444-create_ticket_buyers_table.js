'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();
		try {
			await queryInterface.createTable(
				'ticket_buyers', {
					id: {
						type: Sequelize.INTEGER,
						autoIncrement: true,
						primaryKey: true,
					},
					purchase_info_id: {
						type: Sequelize.INTEGER,
						allowNull: false,
						references: {
							model: 'purchase_infos',
							key: 'id',
						},
						validate: {
							min: 1,
						},
						onDelete: 'CASCADE',
						onUpdate: 'CASCADE',
					},
					user_id: {
						type: Sequelize.INTEGER,
						allowNull: true,
						references: {
							model: 'users',
							key: 'id',
						},
						validate: {
							min: 1,
						},
						onDelete: 'CASCADE',
						onUpdate: 'CASCADE',
					},
					pm_id: {
						type: Sequelize.INTEGER,
						allowNull: true,
						references: {
							model: 'payment_methods',
							key: 'id',
						},
						validate: {
							min: 1,
						},
						onDelete: 'CASCADE',
						onUpdate: 'CASCADE',
					},
					full_name: {
						type: Sequelize.STRING,
						allowNull: false,
					},
					phone_number: {
						type: Sequelize.STRING(20),
						allowNull: false,
					},
					email: {
						type: Sequelize.STRING,
						allowNull: false,
					},
					has_processed: {
						type: Sequelize.BOOLEAN,
						allowNull: false,
						defaultValue: false,
					},
					was_payment_successful: {
						type: Sequelize.BOOLEAN,
						allowNull: false,
						defaultValue: false,
					},
					paid_amount: {
						type: Sequelize.DECIMAL,
						allowNull: true,
					},
					gateway_response: {
						type: Sequelize.JSON,
						allowNull: true,
					},
					download_link: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					download_count: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					created_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
					updated_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
				},
				{ transaction }
			);
			await queryInterface.addIndex('ticket_buyers', ['download_link'],
				{ transaction }
			);
			await transaction.commit();
		} catch (e) {
			await transaction.rollback();
			throw e;
		}
	},

	async down (queryInterface) {
		await queryInterface.dropTable('ticket_buyers');
	}
};
