'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.addColumn('events', 'rejected_at', {
				type: Sequelize.DATE,
				allowNull: true,
			}, { transaction });

			await queryInterface.addColumn('events', 'cancelled_at', {
				type: Sequelize.DATE,
				allowNull: true,
			}, { transaction });

			await queryInterface.addColumn('events', 'reject_reason', {
				type: Sequelize.TEXT,
				allowNull: true,
			}, { transaction });
      
			await queryInterface.addColumn('events', 'cancel_reason', {
				type: Sequelize.TEXT,
				allowNull: true,
			}, { transaction });
		});
	},

	async down (queryInterface) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.removeColumn('events', 'rejected_at', { transaction });
			await queryInterface.removeColumn('events', 'cancelled_at', { transaction });
			await queryInterface.removeColumn('events', 'reject_reason', { transaction });
			await queryInterface.removeColumn('events', 'cancel_reason', { transaction });
		});
	}
};
