'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.addColumn('ticket_buyers', 'event_id', {
			type: Sequelize.INTEGER,
			allowNull: true,
			references: {
				model: 'events',
				key: 'id'
			},
			onUpdate: 'CASCADE',
			onDelete: 'CASCADE'
		});
	},

	async down (queryInterface) {
		await queryInterface.removeColumn('ticket_buyers', 'event_id');
	}
};
