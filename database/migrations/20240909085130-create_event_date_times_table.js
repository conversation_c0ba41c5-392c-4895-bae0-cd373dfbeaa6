'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'event_date_times', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				date_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'event_dates',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				time: {
					type: Sequelize.TIME,
					allowNull: false,
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('event_date_times');
	}
};
