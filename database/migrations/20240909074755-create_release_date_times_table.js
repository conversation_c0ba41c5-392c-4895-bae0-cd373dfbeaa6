'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'release_date_times',
			{
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				release_date_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'movie_theatre_release_date',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				time: {
					type: Sequelize.TIME,
					allowNull: false,
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('release_date_times');
	}
};
