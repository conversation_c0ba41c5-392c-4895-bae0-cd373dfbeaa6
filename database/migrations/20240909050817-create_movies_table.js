'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'movies', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				industry_id: {
					type: Sequelize.INTEGER,
					allowNull: true,
					references: {
						model: 'industries',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'SET NULL',
					onUpdate: 'CASCADE',
				},
				name: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				slug: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				trailer: {
					type: Sequelize.STRING(100),
					allowNull: false,
				},
				poster: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				director: {
					type: Sequelize.STRING(40),
					allowNull: true,
				},
				release_date: {
					type: Sequelize.DATE,
					allowNull: false,
				},
				duration: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				summary: {
					type: Sequelize.TEXT,
					allowNull: false,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				},
				meta_keywords: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				meta_desc: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('movies');
	}
};
