'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'event_credentials', {
				id: {
					type: Sequelize.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				event_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'events',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				uname: {
					type: Sequelize.STRING,
					allowNull: false,
					unique: true,
				},
				password: {
					type: Sequelize.STRING,
					allowNull: true,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('event_credentials');
	}
};
