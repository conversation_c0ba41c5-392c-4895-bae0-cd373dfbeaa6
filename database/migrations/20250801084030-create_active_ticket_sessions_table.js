'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable('active_ticket_sessions', {
			id: {
				type: Sequelize.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			event_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'events',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			user_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'users',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			ticket_id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'event_tickets',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			ticket_count: {
				type: Sequelize.INTEGER,
				allowNull: false,
			},
			start_time: {
				type: Sequelize.DATE,
				allowNull: false,
			},
			end_time: {
				type: Sequelize.DATE,
				allowNull: false,
			},
		});
	},

	async down (queryInterface) {
		await queryInterface.dropTable('active_ticket_sessions');
	}
};
