'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'company_user', {
				id: {
					type: Sequelize.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				company_id: {
					type: Sequelize.BIGINT,
					allowNull: false,
					references: {
						model: 'companies',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				user_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'users',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('company_user');
	}
};
