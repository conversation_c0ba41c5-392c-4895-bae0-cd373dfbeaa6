'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.addColumn('users', 'email_updated_at', {
				type: Sequelize.DATE,
				allowNull: true,
				transaction,
			});
			await queryInterface.addColumn('users', 'password_updated_at', {
				type: Sequelize.DATE,
				allowNull: true,
				transaction,
			});
		});
	},

	async down (queryInterface) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.removeColumn('users', 'email_updated_at', {
				transaction,
			});
			await queryInterface.removeColumn('users', 'password_updated_at', {
				transaction,
			});
		});
	}
};
