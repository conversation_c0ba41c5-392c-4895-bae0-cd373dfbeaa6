'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable('scan_logs', {
			id: {
				type: Sequelize.INTEGER,
				primaryKey: true,
				autoIncrement: true,
			},
			partner_id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'users',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			ticket_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'purchase_infos',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			event_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'events',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			status: {
				type: Sequelize.ENUM('success', 'fail'),
				allowNull: true,
			},
			title: {
				type: Sequelize.STRING,
				allowNull: true,
			},
			details: {
				type: Sequelize.STRING,
				allowNull: true,
			},
			created_at: {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.fn('NOW'),
			},
			updated_at: {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.fn('NOW'),
			},
		});
	},

	async down (queryInterface) {
		await queryInterface.dropTable('scan_logs');
	}
};
