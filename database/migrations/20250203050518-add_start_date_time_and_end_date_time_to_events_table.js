'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.addColumn('events', 'start_date_time', {
				type: Sequelize.DATE,
				allowNull: true,
				defaultValue: null,
				transaction,
			});
			await queryInterface.addColumn('events', 'end_date_time', {
				type: Sequelize.DATE,
				allowNull: true,
				defaultValue: null,
				transaction,
			});
		});
	},

	async down (queryInterface) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.removeColumn('events', 'start_date_time', {
				transaction,
			});
			await queryInterface.removeColumn('events', 'end_date_time', {
				transaction,
			});
		});
	}
};
