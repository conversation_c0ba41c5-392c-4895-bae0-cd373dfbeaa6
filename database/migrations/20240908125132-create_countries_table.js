'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();
		try {
			await queryInterface.createTable(
				'countries', {
					name: {
						type: Sequelize.STRING,
						allowNull: false,
					},
					iso_code: {
						type: Sequelize.CHAR(2),
						allowNull: false,
						primaryKey: true,
					},
					currency_code: {
						type: Sequelize.CHAR(3),
						allowNull: true,
					},
					dial_code: {
						type: Sequelize.CHAR(3),
						allowNull: true,
					},
          
				},
				{ transaction }
			);
			await queryInterface.addIndex('countries', ['dial_code'],
				{ transaction }
			);
			await transaction.commit();
		} catch (e) {
			await transaction.rollback();
			throw e;
		}
	},

	async down (queryInterface) {
		await queryInterface.dropTable('countries');
	}
};
