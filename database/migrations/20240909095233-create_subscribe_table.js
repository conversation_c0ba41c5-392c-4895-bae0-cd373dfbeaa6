'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'subscribers', {
				id: {
					type: Sequelize.BIGINT,
					autoIncrement: true,
					primaryKey: true,
				},
				email: {
					type: Sequelize.STRING,
					allowNull: false,
					unique: true,
				},
				ip_address: {
					type: Sequelize.INET,
					allowNull: false,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			});
	},

	async down (queryInterface) {
		await queryInterface.dropTable('subscribers');
	}
};
