'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'movie_category', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				category_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'categories',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				movie_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'movies',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('movie_category');
	}
};
