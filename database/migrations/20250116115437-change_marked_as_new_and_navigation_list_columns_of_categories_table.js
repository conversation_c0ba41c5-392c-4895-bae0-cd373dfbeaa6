'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			// Remove the old 'marked_as_new' column
			await queryInterface.removeColumn('categories', 'marked_as_new', { transaction });

			// Remove the old 'navigation_list' column
			await queryInterface.removeColumn('categories', 'navigation_list', { transaction });

			// Add the new 'marked_as_new' column
			await queryInterface.addColumn(
				'categories',
				'marked_as_new',
				{
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				{ transaction }
			);

			// Add the new 'navigation_list' column
			await queryInterface.addColumn(
				'categories',
				'navigation_list',
				{
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				{ transaction }
			);

			// For 'is_main': Set NULL values to FALSE and update the column
			await queryInterface.sequelize.query(
				'UPDATE categories SET is_main = FALSE WHERE is_main IS NULL',
				{ transaction }
			);

			await queryInterface.changeColumn(
				'categories',
				'is_main',
				{
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				{ transaction }
			);
		});
	},

	async down(queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			// Remove the new 'marked_as_new' column
			await queryInterface.removeColumn('categories', 'marked_as_new', { transaction });

			// Remove the new 'navigation_list' column
			await queryInterface.removeColumn('categories', 'navigation_list', { transaction });

			// Add back the old 'marked_as_new' column
			await queryInterface.addColumn(
				'categories',
				'marked_as_new',
				{
					type: Sequelize.SMALLINT,
					allowNull: true,
					comment: 'Place 1 if the category has to be marked as new',
				},
				{ transaction }
			);

			// Add back the old 'navigation_list' column
			await queryInterface.addColumn(
				'categories',
				'navigation_list',
				{
					type: Sequelize.SMALLINT,
					allowNull: true,
					comment: 'Place 1 to list the category in the menu',
				},
				{ transaction }
			);

			// Revert 'is_main' column
			await queryInterface.changeColumn(
				'categories',
				'is_main',
				{
					type: Sequelize.BOOLEAN,
					allowNull: true,
					comment: 'Place 1 if the category has to be marked as main',
				},
				{ transaction }
			);
		});
	},
};
