'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.addColumn('venues', 'phone_number', {
				type: Sequelize.STRING,
				allowNull: true,
			}, { transaction });

			await queryInterface.addColumn('venues', 'mobile_number', {
				type: Sequelize.STRING,
				allowNull: true,
			}, { transaction });

			await queryInterface.addColumn('venues', 'email', {
				type: Sequelize.STRING,
				allowNull: true,
			}, { transaction });

			await queryInterface.addColumn('venues', 'website', {
				type: Sequelize.STRING,
				allowNull: true,
			}, { transaction });

			await queryInterface.removeColumn('venues', 'contact_info', { transaction });

			await queryInterface.removeColumn('venues', 'map_link', { transaction });

			await queryInterface.removeColumn('venues', 'city_id', { transaction });
		});
	},

	async down (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.removeColumn('venues', 'website', { transaction });
			await queryInterface.removeColumn('venues', 'email', { transaction });
			await queryInterface.removeColumn('venues', 'mobile_number', { transaction });
			await queryInterface.removeColumn('venues', 'phone_number', { transaction });

			await queryInterface.addColumn('venues', 'contact_info', {
				type: Sequelize.JSON,
				allowNull: true,
			}, { transaction });

			await queryInterface.addColumn('venues', 'map_link', {
				type: Sequelize.STRING,
				allowNull: true,
			}, { transaction });

			await queryInterface.addColumn('venues', 'city_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'cities',
					key: 'id',
				},
				validate: {
					min: 1,
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			}, { transaction });
		});
	}
};
