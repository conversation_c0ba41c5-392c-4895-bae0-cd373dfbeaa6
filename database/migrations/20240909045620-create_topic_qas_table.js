'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'topic_qas', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				topic_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'topics',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				question: {
					type: Sequelize.STRING,
					allowNull: false,
					unique: true,
				},
				slug: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				answer: {
					type: Sequelize.TEXT,
					allowNull: true,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				},
				order: {
					type: Sequelize.INTEGER,
					allowNull: true,
					validate: {
						min: 0,
					}
				},
				view: {
					type: Sequelize.INTEGER,
					allowNull: true,
					validate: {
						min: 0,
					}
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('topic_qas');
	}
};
