'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.removeColumn('ticket_buyers', 'pm_id', { transaction });

			await queryInterface.removeColumn('ticket_buyers', 'purchase_info_id', { transaction });

			await queryInterface.removeColumn('ticket_buyers', 'gateway_response', { transaction });

			await queryInterface.renameColumn('ticket_buyers', 'paid_amount', 'total_amount', { transaction });

			await queryInterface.addColumn('ticket_buyers', 'ticket_count', {
				type: Sequelize.INTEGER,
				allowNull: true,
				validate: {
					min: 1,
				},
			}, { transaction });

			await queryInterface.addColumn('ticket_buyers', 'payment_method', {
				type: Sequelize.STRING,
				allowNull: true,
			}, { transaction });

			await queryInterface.addColumn('ticket_buyers', 'payment_method_response', {
				type: Sequelize.JSON,
				allowNull: true,
			}, { transaction });
		});
	},

	async down (queryInterface, Sequelize) {
		await queryInterface.sequelize.transaction(async (transaction) => {
			await queryInterface.addColumn('ticket_buyers', 'pm_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'payment_methods',
					key: 'id',
				},
				validate: {
					min: 1,
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			}, { transaction });

			await queryInterface.addColumn('ticket_buyers', 'purchase_info_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'purchase_infos',
					key: 'id',
				},
				validate: {
					min: 1,
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			}, { transaction });

			await queryInterface.addColumn('ticket_buyers', 'gateway_response', {
				type: Sequelize.JSON,
				allowNull: true,
			}, { transaction });

			await queryInterface.renameColumn('ticket_buyers', 'total_amount', 'paid_amount', { transaction });

			await queryInterface.removeColumn('ticket_buyers', 'ticket_count', { transaction });

			await queryInterface.removeColumn('ticket_buyers', 'payment_method', { transaction });

			await queryInterface.removeColumn('ticket_buyers', 'payment_method_response', { transaction });
		});
	}
};
