'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.addColumn('scan_logs', 'buyer_id', {
			type: Sequelize.INTEGER,
			allowNull: true,
			references: {
				model: 'ticket_buyers',
				key: 'id',
			},
			onDelete: 'CASCADE',
			onUpdate: 'CASCADE',
		});
	},

	async down (queryInterface) {
		await queryInterface.removeColumn('scan_logs', 'buyer_id');
	}
};
