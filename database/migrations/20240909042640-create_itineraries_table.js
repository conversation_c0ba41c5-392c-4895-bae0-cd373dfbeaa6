'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'itineraries', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				package_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'travels',
						key: 'id',
					},
					validate: {
						min: 1,
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				title: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				details: {
					type: Sequelize.TEXT,
					allowNull: false,
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
				updated_at: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.fn('NOW'),
				},
			},
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('itineraries');
	}
};
