'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();
		try {
			await queryInterface.createTable(
				'artists', {
					id: {
						type: Sequelize.INTEGER,
						autoIncrement: true,
						primaryKey: true,
					},
					name: {
						type: Sequelize.STRING,
						allowNull: false,
					},
					title: {
						type: Sequelize.STRING,
						allowNull: false,
					},
					stage_name: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					band_name: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					image: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					dob: {
						type: Sequelize.DATEONLY,
						allowNull: true,
					},
					home_town: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					about: {
						type: Sequelize.TEXT,
						allowNull: false,
					},
					status: {
						type: Sequelize.BOOLEAN,
						allowNull: false,
						defaultValue: true,
					},
					meta_keywords: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					meta_desc: {
						type: Sequelize.STRING,
						allowNull: true,
					},
					created_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
					updated_at: {
						type: Sequelize.DATE,
						allowNull: false,
						defaultValue: Sequelize.fn('NOW'),
					},
				},
				{ transaction }
			);
			await queryInterface.addIndex('artists', ['name', 'stage_name', 'band_name'],
				{ transaction }
			);
			await transaction.commit();
		} catch (e) {
			await transaction.rollback();
			throw e;
		}
	},

	async down (queryInterface) {
		await queryInterface.dropTable('artists');
	}
};
