'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'quotes', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				quote: {
					type: Sequelize.TEXT,
					allowNull: false,
				},
				by: {
					type: Sequelize.STRING,
					allowNull: true,
				}
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('quotes');
	}
};
