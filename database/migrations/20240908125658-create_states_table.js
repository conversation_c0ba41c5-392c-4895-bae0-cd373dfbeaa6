'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up (queryInterface, Sequelize) {
		await queryInterface.createTable(
			'states', {
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				name: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				local_name: {
					type: Sequelize.STRING,
					allowNull: false,
				},
				country_code: {
					type: Sequelize.CHAR(2),
					allowNull: false,
					references: {
						model: 'countries',
						key: 'iso_code',
					},
					onDelete: 'CASCADE',
					onUpdate: 'CASCADE',
				},
				is_main: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				icon: {
					type: Sequelize.STRING(20),
					allowNull: true,
				},
				status: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: true,
				}
			}
		);
	},

	async down (queryInterface) {
		await queryInterface.dropTable('states');
	}
};
