name: Deploy to Production
on:
  push:
    branches:
      - 'master'
jobs:
  build-project:
    name: Build Project
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Configure SSH
        uses: webfactory/ssh-agent@v0.5.1
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - uses: pnpm/action-setup@v3
        with:
          version: 8.8.0

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}

      - name: Setup and Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{matrix.node-version}}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install

      - name: Generate .env file
        run: |
          echo "${{ secrets.ENVIRONMENT_VARS }}" | base64 --decode > .env

      - name: Install dependencies
        run: pnpm build

      - name: Deploy via SSH
        run: |
          echo "A initialization"
          rsync -avz --delete-after --quiet --include=".*" --exclude=".github"  --exclude=".git"  --exclude=".vscode"  -e "ssh -o StrictHostKeyChecking=no" ./ root@**************:/var/www/production/client

      - name: Execute SSH command
        uses: appleboy/ssh-action@master
        with:
          host: **************
          username: root
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            # Enter your SSH command here
            pm2 reload eticketnepal-client
