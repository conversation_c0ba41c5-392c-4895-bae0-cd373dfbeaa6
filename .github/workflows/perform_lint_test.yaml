name: Upto standard sanity check (eslint)
on:
  pull_request:
    branches:
      - master

jobs:
  tests:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - uses: pnpm/action-setup@v2
        with:
          version: 8.8.0

      - name: Cache dependencies
        uses: actions/cache@v2
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/pnpm-lock.yaml') }}

      - name: Setup and Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{matrix.node-version}}
          cache: pnpm

      - name: Install dependencies
        run: pnpm install

      - name: Run ESLint
        run: pnpm run lint
      #   run: npm run lint --config ${matrix.eslint-config}.js
