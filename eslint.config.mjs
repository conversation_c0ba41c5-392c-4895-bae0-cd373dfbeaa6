import globals from 'globals';
import pluginJs from '@eslint/js';
import tseslint from 'typescript-eslint';

export default [
	{
		files: ['**/*.{js,mjs,cjs,ts}'],
		ignores: ['node_modules/**', 'dist/**', 'build/**', '*.d.ts', '*.json', '*.md', '*.yaml'],
	},
	{ languageOptions: { globals: { ...globals.browser, ...globals.node }}},
	pluginJs.configs.recommended,
	...tseslint.configs.recommended,
	{
		rules: {
			'eqeqeq': 'error',
			'no-unused-vars': 'error',
			'no-console': 'warn',
			'indent': ['error', 'tab'],
			'switch-colon-spacing': ['error', { 'after': true, 'before': false }],
			'keyword-spacing': ['error', { 'before': true }],
			'object-curly-spacing': [
				'error',
				'always',
				{ 'arraysInObjects': false, 'objectsInObjects': false }
			],
			'key-spacing': ['error', { 'beforeColon': false, 'afterColon': true }],
			'arrow-spacing': ['error', { 'before': true, 'after': true }],
			'comma-spacing': ['error', { 'before': false, 'after': true }],
			'eol-last': ['error', 'always'],
			'@typescript-eslint/no-explicit-any': 'warn',
			'no-trailing-spaces': [
				'error',
				{ 'skipBlankLines': true, 'ignoreComments': true }
			],
			'semi-style': ['error', 'last'],
			'semi': ['error', 'always'],
			'quotes': ['error', 'single'],
			'prefer-const': 'error',
			'no-multi-spaces': 'error',
			'space-in-parens': ['error', 'never'],
			'no-multiple-empty-lines': ['error', { 'max': 1, 'maxEOF': 0 }],
		}
	}
];
