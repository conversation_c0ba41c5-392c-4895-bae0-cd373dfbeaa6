import { AllowNull, BelongsTo, Column, DataType, Model, Table } from 'sequelize-typescript';
import User from './user.model';

export type UserLogType = {
    id?: number;
    user_id: number;
    company_id: number;
    event_type: string;
    type: boolean;
    details: string;
    action: string;
    payloads: JSON;
    ip_address: string;
    user_agent: string;
    read_access: boolean;
    referrer: string;
    method: string;
    api_endpoint: string;
    created_at: Date;
	user?: User;
}

@Table({
	tableName: 'user_logs',
	timestamps: false,
	paranoid: false,
	underscored: true,
	name: {
		singular: 'user_log',
		plural: 'user_logs',
	},
})
class UserLog extends Model implements UserLogType {
    @AllowNull(true)
    @Column(DataType.INTEGER)
	declare user_id: number;

    @AllowNull(true)
    @Column(DataType.INTEGER)
    declare company_id: number;

    @AllowNull(true)
    @Column(DataType.STRING)
    declare event_type: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    declare ip_address: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    declare user_agent: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    declare action: string;

    @AllowNull(true)
    @Column(DataType.BOOLEAN)
    declare type: boolean;
    
    @AllowNull(true)
    @Column(DataType.STRING)
    declare details: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    declare referrer: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    declare method: string;

    @AllowNull(true)
    @Column(DataType.STRING)
    declare api_endpoint: string;
    
    @AllowNull(true)
    @Column(DataType.JSON)
    declare payloads: JSON;
    
    @AllowNull(true)
    @Column(DataType.BOOLEAN)
    declare read_access: boolean;

    @Column(DataType.DATE)
    declare created_at: Date;

    @BelongsTo(() => User, { foreignKey: 'user_id', targetKey: 'id' })
    declare user: User;

}

export default UserLog;
