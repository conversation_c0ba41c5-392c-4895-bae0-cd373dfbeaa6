import { Table, Column, Model, DataType, PrimaryKey, AutoIncrement, AllowNull, Unique, CreatedAt, UpdatedAt, Default, BelongsToMany, Index, HasMany, HasOne } from 'sequelize-typescript';
import { Company, CompanyUser, EventLike, EventSubscription, TicketBuyer, UserCategoryPreference, UserPreference } from '.';

interface IUser {
	id: number;
	f_name?: string;
	l_name?: string;
	email?: string;
	phone?: string;
	image?: string;
	ip_address?: string;
	dob?: Date;
	gender?: 'male' | 'female' | 'others';
	password?: string;
	is_partner?: boolean;
	user_type?: string;
	email_verified_at?: Date;
	phone_verified_at?: Date;
	verification_code?: string;
	action_link_sent_at?: Date;
	last_logged_in_at?: Date;
	email_updated_at?: Date;
	password_updated_at?: Date;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'users',
	timestamps: true,
	paranoid: false,
})

class User extends Model implements IUser {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare f_name: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare l_name: string;

	@Unique
	@AllowNull(true)
	@Column(DataType.STRING)
	declare email: string;

	@Unique
	@AllowNull(true)
	@Column(DataType.STRING)
	declare phone: string;

	@AllowNull(true)
	@Column(DataType.STRING(100))
	declare image: string;

	@AllowNull(true)
	@Column(DataType.INET)
	declare ip_address: string;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare dob: Date;

	@AllowNull(true)
	@Column(DataType.ENUM('male', 'female', 'others'))
	declare gender: 'male' | 'female' | 'others';

	@AllowNull(true)
	@Column(DataType.STRING)
	declare password: string;

	@AllowNull(true)
	@Index
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare is_partner: boolean;

	@AllowNull(false)
	@Default('normal')
	@Column(DataType.ENUM('admin', 'normal'))
	declare user_type: string;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare email_verified_at: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare phone_verified_at: Date;

	@AllowNull(true)
	@Column(DataType.STRING(6))
	declare verification_code: string;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare action_link_sent_at: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare last_logged_in_at: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare email_updated_at: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare password_updated_at: Date;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsToMany(() => Company, {
		through: { model: () => CompanyUser },
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare companies: Company[];

	@HasMany(() => EventLike, {
		foreignKey: 'user_id',
	})
	declare liked_events: EventLike[];

	@HasMany(() => EventSubscription, {
		foreignKey: 'user_id',
	})
	declare subscribed_events: EventSubscription[];

	@HasOne(() => UserPreference, {
		foreignKey: 'user_id',
	})
	declare user_preference: UserPreference;

	@HasMany(() => UserCategoryPreference, {
		foreignKey: 'user_id',
	})
	declare user_category_preferences: UserCategoryPreference[];

	@HasMany(() => TicketBuyer, {
		foreignKey: 'user_id',
	})
	declare ticket_buys: TicketBuyer[];
}

User.prototype.toJSON = function () {
	const item = this.get();
	delete item.password;
	delete item.verification_code;
	delete item.action_link_sent_at;
	return item;
};

export default User;
