import { Table, Column, Model, DataType, PrimaryKey, AutoIncrement, AllowNull, ForeignKey } from 'sequelize-typescript';
import { Company, User } from '.';

interface ICompanyUser {
	id: number;
	company_id: number;
	user_id: number;
}

@Table({
	tableName: 'company_user',
	timestamps: false,
	paranoid: false,
})

class CompanyUser extends Model implements ICompanyUser {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.BIGINT)
	declare id: number;
	
	@AllowNull(false)
	@ForeignKey(() => Company)
	@Column(DataType.BIGINT)
	declare company_id: number;

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare user_id: number;
}

export default CompanyUser;
