import { AllowNull, AutoIncrement, BelongsToMany, Column, CreatedAt, DataType, Default, Model, PrimaryKey, Table, Unique, UpdatedAt } from 'sequelize-typescript';
import { Event, EventVenue } from '.';

interface IVenue {
	id: number;
	name: string;
	slug: string;
	logo?: string;
	image?: string;
	facebook?: string;
	youtube?: string;
	twitter?: string;
	instagram?: string;
	phone_number?: string;
	mobile_number?: string;
	email?: string;
	website?: string;
	status: boolean;
	about?: string;
	attractions?: string;
	coordinates?: { type: 'Point'; coordinates: [number, number] } | null;
	address?: JSON;
	meta_keywords?: string;
	meta_desc?: string;
	created_at: Date;
	updated_at: Date;
	deleted_at?: Date;
}

@Table({
	tableName: 'venues',
	timestamps: true,
	paranoid: false,
})

class Venue extends Model implements IVenue {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@Unique
	@Column(DataType.STRING)
	declare name: string;

	@Unique
	@AllowNull(false)
	@Column(DataType.STRING)
	declare slug: string;

	@AllowNull(true)
	@Column(DataType.STRING(100))
	declare logo: string;

	@AllowNull(true)
	@Column(DataType.STRING(100))
	declare image: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare facebook: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare youtube: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare twitter: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare instagram: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare phone_number: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare mobile_number: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare email: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare website: string;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare about: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare attractions: string;

	@AllowNull(true)
	@Column(DataType.GEOMETRY('POINT', 4326))
	declare coordinates: { type: 'Point'; coordinates: [number, number] } | null;

	@AllowNull(true)
	@Column(DataType.JSON)
	declare address: JSON;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare meta_keywords: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare meta_desc: string;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare deleted_at: Date;
	
	@BelongsToMany(() => Event, {
		through: { model: () => EventVenue },
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare events: Event[];
}

export default Venue;
