import { Table, Column, Model, DataType, PrimaryKey, AutoIncrement, AllowNull, Unique, CreatedAt, UpdatedAt } from 'sequelize-typescript';

interface IGeneralInfo {
	id: number;
	logo?: string;
	name?: string;
	app_info?: string;
	address_one?: string;
	address_two?: string;
	phone_one?: string;
	phone_two?: string;
	mobile_one?: string;
	mobile_two?: string;
	site_url?: string;
	email_one?: string;
	email_two?: string;
	copyright?: string;
	yt_channel?: string;
	facebook?: string;
	twitter?: string;
	linkedin?: string;
	instagram?: string;
	google?: string;
	pinterest?: string;
	meta_desc?: string;
	meta_keywords?: string;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'general_infos',
	timestamps: true,
	paranoid: false,
})

class GeneralInfo extends Model implements IGeneralInfo {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare logo: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare name: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare app_info: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare address_one: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare address_two: string;

	@AllowNull(true)
	@Unique(true)
	@Column(DataType.STRING)
	declare phone_one: string;

	@AllowNull(true)
	@Unique(true)
	@Column(DataType.STRING)
	declare phone_two: string;

	@AllowNull(true)
	@Unique(true)
	@Column(DataType.STRING)
	declare mobile_one: string;

	@AllowNull(true)
	@Unique(true)
	@Column(DataType.STRING)
	declare mobile_two: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare site_url: string;

	@AllowNull(true)
	@Unique(true)
	@Column(DataType.STRING)
	declare email_one: string;

	@AllowNull(true)
	@Unique(true)
	@Column(DataType.STRING)
	declare email_two: string;

	@Column(DataType.TEXT)
	declare copyright: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare yt_channel: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare facebook: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare twitter: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare linkedin: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare instagram: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare google: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare pinterest: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare meta_desc: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare meta_keywords: string;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;
}

export default GeneralInfo;
