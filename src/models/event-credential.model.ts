import { AllowNull, AutoIncrement, BelongsTo, Column, CreatedAt, DataType, ForeignKey, Model, PrimaryKey, Table, Unique, UpdatedAt } from 'sequelize-typescript';
import { Event } from '.';

interface IEventCredential {
	id: bigint;
	event_id: number;
	uname: string;
	password: string;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'event_credentials',
	timestamps: true,
	paranoid: false,
})

class EventCredential extends Model implements IEventCredential {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.BIGINT)
	declare id: bigint;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@Unique
	@Column(DataType.STRING)
	declare uname: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare password: string;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare event: Event;
}

export default EventCredential;
