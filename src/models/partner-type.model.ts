import { Table, Column, Model, DataType, PrimaryKey, AutoIncrement, AllowNull, CreatedAt, UpdatedAt, Default } from 'sequelize-typescript';

interface IPartnerType {
	id: number;
	code: string;
	type_name: string;
	status: boolean;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'partner_types',
	timestamps: true,
	paranoid: false,
})

class PartnerType extends Model implements IPartnerType {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.BIGINT)
	declare id: number;

	@AllowNull(false)
	@Column(DataType.STRING(32))
	declare code: string;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare type_name: string;

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;
}

export default PartnerType;
