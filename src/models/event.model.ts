import { Table, Column, Model, DataType, PrimaryKey, AutoIncrement, AllowNull, Unique, CreatedAt, UpdatedAt, Default, ForeignKey, BelongsTo, Index, BelongsToMany, HasMany } from 'sequelize-typescript';
import { Artist, Category, Company, EventActivity, EventArtist, EventCategory, EventDate, EventFaq, EventGallery, EventLike, EventSlider, EventSubscription, EventTerm, EventTicket, EventVenue, Venue } from '.';

export interface IEvent {
	id: number;
	company_id?: number;
	title?: string;
	slug?: string;
	duration?: string;
	main_attraction?: string;
	image?: string;
	about?: string;
	status: boolean;
	feature_from?: Date;
	feature_expiry?: Date;
	meta_keywords?: string;
	meta_desc?: string
	created_at: Date;
	published_at?: Date;
	updated_at: Date;
	deleted_at?: Date;
	start_date_time?: Date;
	end_date_time?: Date;
	approved_at: Date;
	rejected_at?: Date;
	cancelled_at?: Date;
	reject_reason?: string;
	cancel_reason?: string;
	is_delayed?: boolean;
	external_ticket_link?: string;
}

@Table({
	tableName: 'events',
	timestamps: true,
	paranoid: false,
})

class Event extends Model implements IEvent {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(true)
	@ForeignKey(() => Company)
	@Column(DataType.BIGINT)
	declare company_id: number;

	@AllowNull(true)
	@Index
	@Unique
	@Column(DataType.STRING)
	declare title: string;

	@AllowNull(true)
	@Index
	@Column(DataType.STRING)
	declare slug: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare duration: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare main_attraction: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare image: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare about: string;

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare feature_from: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare feature_expiry: Date;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare meta_keywords: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare meta_desc: string;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare published_at: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare deleted_at: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare start_date_time: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare end_date_time: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare approved_at: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare rejected_at: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare cancelled_at: Date;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare reject_reason: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare cancel_reason: string;

	@AllowNull(true)
	@Column(DataType.BOOLEAN)
	declare is_delayed: boolean;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare external_ticket_link: string;

	@BelongsTo(() => Company, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare company: Company;

	@BelongsToMany(() => Artist, {
		through: { model: () => EventArtist },
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare artists: Artist[];

	@BelongsToMany(() => Venue, {
		through: { model: () => EventVenue },
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare venues: Venue[];

	@BelongsToMany(() => Category, {
		through: { model: () => EventCategory },
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare categories: Category[];

	@HasMany(() => EventTicket, {
		foreignKey: 'event_id',
	})
	declare tickets: EventTicket[];

	@HasMany(() => EventTerm, {
		foreignKey: 'event_id',
	})
	declare terms: EventTerm[];

	@HasMany(() => EventFaq, {
		foreignKey: 'event_id',
	})
	declare faqs: EventFaq[];

	@HasMany(() => EventDate, {
		foreignKey: 'event_id',
	})
	declare dates: EventDate[];

	@HasMany(() => EventSlider, {
		foreignKey: 'event_id',
	})
	declare sliders: EventSlider[];

	@HasMany(() => EventGallery, {
		foreignKey: 'event_id',
	})
	declare galleries: EventGallery[];

	@HasMany(() => EventActivity, {
		foreignKey: 'event_id',
	})
	declare activities: EventActivity[];

	@HasMany(() => EventLike, {
		foreignKey: 'event_id',
	})
	declare likes: EventLike[];

	@HasMany(() => EventSubscription, {
		foreignKey: 'event_id',
	})
	declare subscriptions: EventSubscription[];
}

export default Event;
