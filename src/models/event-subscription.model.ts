import { AllowNull, AutoIncrement, BelongsTo, Column, CreatedAt, DataType, ForeignKey, Model, PrimaryKey, Table, UpdatedAt } from 'sequelize-typescript';
import { Event, User } from '.';

interface IEventSubscription {
	id: number;
	event_id: number;
	user_id: number;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'event_subscriptions',
	timestamps: true,
	paranoid: false,
})

class EventSubscription extends Model implements IEventSubscription {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare user_id: number;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare event: Event;

	@BelongsTo(() => User, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare user: User;
}

EventSubscription.prototype.toJSON = function () {
	const item = this.get();
	delete item.created_at;
	delete item.updated_at;
	return item;
};

export default EventSubscription;
