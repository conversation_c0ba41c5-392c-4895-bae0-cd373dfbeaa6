import { AllowNull, AutoIncrement, Column, DataType, De<PERSON>ult, Model, PrimaryKey, Table } from 'sequelize-typescript';

interface IApp {
	id: number;
	name: string;
	status: boolean;
}

@Table({
	tableName: 'apps',
	timestamps: false,
	paranoid: false,
})

class App extends Model implements IApp {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare name: string;

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare status: boolean;
}

export default App;
