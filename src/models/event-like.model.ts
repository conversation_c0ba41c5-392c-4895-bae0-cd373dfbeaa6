import { AllowNull, AutoIncrement, BelongsTo, Column, CreatedAt, DataType, ForeignKey, Model, PrimaryKey, Table, UpdatedAt } from 'sequelize-typescript';
import { Event, User } from '.';

interface IEventLike {
	id: number;
	event_id: number;
	user_id: number;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'event_likes',
	timestamps: true,
	paranoid: false,
})

class EventLike extends Model implements IEventLike {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare user_id: number;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare event: Event;

	@BelongsTo(() => User, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare user: User;
}

EventLike.prototype.toJSON = function () {
	const item = this.get();
	delete item.created_at;
	delete item.updated_at;
	return item;
};

export default EventLike;
