import { AllowNull, AutoIncrement, BelongsToMany, Column, CreatedAt, DataType, Default, Index, Model, PrimaryKey, Table, UpdatedAt } from 'sequelize-typescript';
import { EventArtist, Event } from '.';

interface IArtist {
	id: number;
	name: string;
	title: string;
	stage_name?: string;
	band_name?: string;
	image?: string;
	dob?: Date;
	home_town?: string;
	about: string;
	status: boolean;
	meta_keywords?: string;
	meta_desc?: string;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'artists',
	timestamps: true,
	paranoid: false,
})

class Artist extends Model implements IArtist {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@Index
	@AllowNull(false)
	@Column(DataType.STRING)
	declare name: string;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare title: string;

	@Index
	@AllowNull(true)
	@Column(DataType.STRING)
	declare stage_name: string;

	@Index
	@AllowNull(true)
	@Column(DataType.STRING)
	declare band_name: string;

	@AllowNull(true)
	@Column(DataType.STRING(100))
	declare image: string;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare dob: Date;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare home_town: string;

	@AllowNull(false)
	@Column(DataType.TEXT)
	declare about: string;

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare meta_keywords: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare meta_desc: string;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsToMany(() => Event, {
		through: { model: () => EventArtist },
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare events: Event[];
}

export default Artist;
