import { AllowNull, AutoIncrement, BelongsTo, BelongsToMany, Column, CreatedAt, DataType, Default, ForeignKey, HasMany, Index, Model, PrimaryKey, Table, UpdatedAt } from 'sequelize-typescript';
import { App, EventCategory, Event } from '.';

interface ICategory {
	id: number;
	sub_id?: number;
	app_type?: number;
	name: string;
	slug: string;
	info?: string;
	image?: string;
	icon_name?: string;
	meta_keywords?: string;
	meta_description?: string;
	status: boolean;
	order?: number;
	marked_as_new: boolean;
	navigation_list: boolean;
	is_main: boolean;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'categories',
	timestamps: true,
	paranoid: false,
})

class Category extends Model implements ICategory {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@Index
	@AllowNull(true)
	@ForeignKey(() => Category)
	@Column(DataType.INTEGER)
	declare sub_id: number;

	@AllowNull(true)
	@ForeignKey(() => App)
	@Column(DataType.INTEGER)
	declare app_type: number;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare name: string;

	@Index
	@AllowNull(false)
	@Column(DataType.STRING)
	declare slug: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare info: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare image: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare icon_name: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare meta_keywords: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare meta_description: string;

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare order: number;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare marked_as_new: boolean;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare navigation_list: boolean;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare is_main: boolean;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => App, {
		onDelete: 'SET NULL',
		onUpdate: 'CASCADE'
	})
	declare app: App;

	@BelongsToMany(() => Event, {
		through: { model: () => EventCategory },
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare events: Event[];

	@HasMany(() => Category, {
		foreignKey: 'sub_id',
		sourceKey: 'id',
	})
	declare sub_categories?: Category[];
}

export default Category;
