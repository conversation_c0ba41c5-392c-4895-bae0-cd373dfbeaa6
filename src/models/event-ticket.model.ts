import { AllowNull, AutoIncrement, BelongsTo, Column, DataType, ForeignKey, Model, PrimaryKey, Table } from 'sequelize-typescript';
import { EventVenue, Event, Venue } from '.';

interface IEventTicket {
	id: number;
	event_id: number;
	venue_id: number;
	event_venue_id: number;
	name?: string,
	ticket_type: 'free' | 'paid';
	number_of_tickets?: number;
	price?: number;
}

@Table({
	tableName: 'event_tickets',
	timestamps: false,
	paranoid: false,
})

class EventTicket extends Model implements IEventTicket {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@ForeignKey(() => Venue)
	@Column(DataType.INTEGER)
	declare venue_id: number;

	@AllowNull(false)
	@ForeignKey(() => EventVenue)
	@Column(DataType.INTEGER)
	declare event_venue_id: number;

	@AllowNull(false)
	@Column(DataType.ENUM('free', 'paid'))
	declare ticket_type: 'free' | 'paid';

	@AllowNull(true)
	@Column(DataType.STRING)
	declare name: string;

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare number_of_tickets: number;

	@AllowNull(true)
	@Column(DataType.DECIMAL)
	declare price: number;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare event: Event;
}

export default EventTicket;
