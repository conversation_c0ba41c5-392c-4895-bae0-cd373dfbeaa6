import {
	AllowNull,
	AutoIncrement,
	Column,
	CreatedAt,
	DataType,
	ForeignKey,
	Model,
	Table,
	UpdatedAt,
	PrimaryKey,
	BelongsTo
} from 'sequelize-typescript';
import { PurchaseInfo, Event, User, TicketBuyer } from '.';

interface IScanLog {
	id: number;
	partner_id: number;
	ticket_id: number | null;
	event_id: number | null;
	buyer_id: number | null;
	status: string | null;
	title: string | null;
	details: string | null;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'scan_logs',
	timestamps: true,
	paranoid: false,
})

class ScanLog extends Model implements IScanLog {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare partner_id: number;

	@AllowNull(true)
	@ForeignKey(() => PurchaseInfo)
	@Column(DataType.INTEGER)
	declare ticket_id: number | null;

	@AllowNull(true)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number | null;

	@AllowNull(true)
	@ForeignKey(() => TicketBuyer)
	@Column(DataType.INTEGER)
	declare buyer_id: number | null;

	@AllowNull(true)
	@Column(DataType.ENUM('success', 'fail'))
	declare status: string | null;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare title: string | null;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare details: string | null;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => TicketBuyer, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare ticket_buyer: TicketBuyer;

	@BelongsTo(() => PurchaseInfo, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare purchase_info: PurchaseInfo;
}

export default ScanLog;
