import { AllowNull, AutoIncrement, Column, CreatedAt, DataType, <PERSON><PERSON>ult, <PERSON><PERSON>any, <PERSON>, Model, PrimaryKey, Table, Unique, UpdatedAt } from 'sequelize-typescript';
import TopicQas from './topic-qas.model';

interface ITopic {
	id: number;
	title: string;
	slug: string;
	status: boolean;
	order: number | null;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'topics',
	timestamps: true,
	paranoid: false,
})

class Topic extends Model implements ITopic {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@Unique
	@Column(DataType.STRING)
	declare title: string;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare slug: string;

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@AllowNull(true)
	@Min(0)
	@Column(DataType.INTEGER)
	declare order: number | null;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;
	
	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@HasMany(() => TopicQas, {
		foreignKey: 'topic_id',
	})
	declare topicQas: TopicQas[];
}

export default Topic;
