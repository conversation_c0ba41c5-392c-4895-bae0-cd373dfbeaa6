import {
	Table,
	Column,
	Model,
	DataType,
	PrimaryKey,
	AutoIncrement,
	AllowNull,
	CreatedAt,
	UpdatedAt,
	ForeignKey,
	BelongsTo
} from 'sequelize-typescript';
import { User, Venue } from '.';

interface IVenueBookingInquiry {
	id: number;
	venue_id: number;
	user_id: number;
	name: string;
	email: string;
	phone: string;
	description: string;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'venue_booking_inquiries',
	timestamps: true,
	paranoid: false,
})

class VenueBookingInquiry extends Model implements IVenueBookingInquiry {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Venue)
	@Column(DataType.INTEGER)
	declare venue_id: number;

	@AllowNull(true)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare user_id: number;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare name: string;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare email: string;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare phone: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare description: string;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => Venue, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare venue: Venue;

	@BelongsTo(() => User, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare user: User;
}

export default VenueBookingInquiry;
