import { AllowNull, AutoIncrement, Column, DataType, Foreign<PERSON>ey, Model, PrimaryKey, Table } from 'sequelize-typescript';
import { Event, Category } from '.';

interface IEventCategory {
	id: number;
	category_id: number;
	event_id: number;
}

@Table({
	tableName: 'event_category',
	timestamps: false,
	paranoid: false,
})

class EventCategory extends Model implements IEventCategory {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Category)
	@Column(DataType.INTEGER)
	declare category_id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;
}

export default EventCategory;
