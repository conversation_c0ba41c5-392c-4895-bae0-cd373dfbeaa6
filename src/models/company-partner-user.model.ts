import { Table, Column, Model, DataType, PrimaryKey, AutoIncrement, AllowNull, ForeignKey, CreatedAt, UpdatedAt } from 'sequelize-typescript';
import { CompanyUser, PartnerType } from '.';

interface ICompanyPartnerUser {
	id: number;
	company_user_id: number;
	partner_id: number;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'company_partner_user',
	timestamps: true,
	paranoid: false,
})

class CompanyPartnerUser extends Model implements ICompanyPartnerUser {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.BIGINT)
	declare id: number;
	
	@AllowNull(false)
	@ForeignKey(() => CompanyUser)
	@Column(DataType.BIGINT)
	declare company_user_id: number;

	@AllowNull(false)
	@ForeignKey(() => PartnerType)
	@Column(DataType.BIGINT)
	declare partner_id: number;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;
}

export default CompanyPartnerUser;
