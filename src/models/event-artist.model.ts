import { AllowNull, AutoIncrement, Column, DataType, Foreign<PERSON>ey, Model, PrimaryKey, Table } from 'sequelize-typescript';
import { Artist, Event } from '.';

interface IEventArtist {
	id: number;
	event_id: number;
	artist_id: number;
}

@Table({
	tableName: 'event_artist',
	timestamps: false,
	paranoid: false,
})

class EventArtist extends Model implements IEventArtist {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@ForeignKey(() => Artist)
	@Column(DataType.INTEGER)
	declare artist_id: number;
}

export default EventArtist;
