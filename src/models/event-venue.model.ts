import { AllowNull, AutoIncrement, BelongsTo, Column, DataType, ForeignKey, Model, PrimaryKey, Table } from 'sequelize-typescript';
import { Venue, Event } from '.';

interface IEventVenue {
	id: number;
	event_id: number;
	venue_id: number;
}

@Table({
	tableName: 'event_venue',
	timestamps: false,
	paranoid: false,
})

class EventVenue extends Model implements IEventVenue {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@ForeignKey(() => Venue)
	@Column(DataType.INTEGER)
	declare venue_id: number;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare event: Event;
}

export default EventVenue;
