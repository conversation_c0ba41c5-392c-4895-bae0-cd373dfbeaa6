import { AllowNull, AutoIncrement, Column, DataType, ForeignKey, Model, PrimaryKey, Table } from 'sequelize-typescript';
import { Event, EventTicket, User } from '.';

interface IActiveTicketSession {
	id: number;
	event_id: number;
	user_id: number;
	ticket_id: number;
	ticket_count: number;
	start_time: Date;
	end_time: Date;
}

@Table({
	tableName: 'active_ticket_sessions',
	timestamps: false,
	paranoid: false,
})

class ActiveTicketSession extends Model implements IActiveTicketSession {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare user_id: number;

	@AllowNull(false)
	@ForeignKey(() => EventTicket)
	@Column(DataType.INTEGER)
	declare ticket_id: number;

	@AllowNull(false)
	@Column(DataType.INTEGER)
	declare ticket_count: number;

	@AllowNull(false)
	@Column(DataType.DATE)
	declare start_time: Date;

	@AllowNull(false)
	@Column(DataType.DATE)
	declare end_time: Date;
}

export default ActiveTicketSession;
