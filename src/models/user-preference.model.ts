import { AllowNull, AutoIncrement, BelongsTo, Column, CreatedAt, DataType, <PERSON><PERSON>ult, Foreign<PERSON>ey, Model, PrimaryKey, Table, UpdatedAt } from 'sequelize-typescript';
import User from './user.model';

interface IUserPreference {
	id: number;
	user_id: number;
	notification_email: boolean;
	notification_whatsapp: boolean;
	notification_device: boolean;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'user_preferences',
	timestamps: true,
	paranoid: false,
})

class UserPreference extends Model implements IUserPreference {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare user_id: number;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare notification_email: boolean;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare notification_whatsapp: boolean;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare notification_device: boolean;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;
	
	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => User, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare user: User;
}

export default UserPreference;
