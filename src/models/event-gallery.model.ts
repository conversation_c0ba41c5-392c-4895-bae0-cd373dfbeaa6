import { AllowNull, AutoIncrement, BelongsTo, Column, DataType, ForeignKey, Model, PrimaryKey, Table } from 'sequelize-typescript';
import { Event } from '.';

interface IEventGallery {
	id: number;
	event_id: number;
	name: string;
	status: boolean;
}

@Table({
	tableName: 'event_galleries',
	timestamps: false,
	paranoid: false,
})

class EventGallery extends Model implements IEventGallery {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@Column(DataType.STRING(70))
	declare name: string;

	@AllowNull(false)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare event: Event;
}

export default EventGallery;
