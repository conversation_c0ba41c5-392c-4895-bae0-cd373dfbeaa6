import { AllowNull, AutoIncrement, BelongsTo, Column, DataType, Foreign<PERSON>ey, Model, PrimaryKey, Table } from 'sequelize-typescript';
import { EventVenue, Event, Venue } from '.';

interface IEventDate {
	id: number;
	event_id: number;
	venue_id: number;
	event_venue_id: number;
	date: Date;
}

@Table({
	tableName: 'event_dates',
	timestamps: false,
	paranoid: false,
})

class EventDate extends Model implements IEventDate {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@ForeignKey(() => Venue)
	@Column(DataType.INTEGER)
	declare venue_id: number;

	@AllowNull(false)
	@ForeignKey(() => EventVenue)
	@Column(DataType.INTEGER)
	declare event_venue_id: number;

	@AllowNull(false)
	@Column(DataType.DATE)
	declare date: Date;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare event: Event;
}

export default EventDate;
