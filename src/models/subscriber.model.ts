import { Table, Column, Model, DataType, PrimaryKey, AutoIncrement, AllowNull, CreatedAt, UpdatedAt, Default } from 'sequelize-typescript';

interface ISubscriber {
	id: number;
	email: string;
	ip_address?: string;
	status: boolean;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'subscribers',
	timestamps: true,
	paranoid: false,
})

class Subscriber extends Model implements ISubscriber {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.BIGINT)
	declare id: number;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare email: string;

	@AllowNull(true)
	@Column(DataType.INET)
	declare ip_address: string;

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;
}

export default Subscriber;
