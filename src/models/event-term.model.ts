import { AllowNull, AutoIncrement, BelongsTo, Column, CreatedAt, DataType, Default, Foreign<PERSON>ey, Model, PrimaryKey, Table, UpdatedAt } from 'sequelize-typescript';
import { Event } from '.';

interface IEventTerm {
	id: number;
	event_id: number;
	terms: string;
	status: boolean;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'event_terms',
	timestamps: true,
	paranoid: false,
})

class EventTerm extends Model implements IEventTerm {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@Column(DataType.TEXT)
	declare terms: string;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare event: Event;
}

EventTerm.prototype.toJSON = function () {
	const item = this.get();
	delete item.created_at;
	delete item.updated_at;
	return item;
};

export default EventTerm;
