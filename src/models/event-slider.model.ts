import { AllowNull, AutoIncrement, BelongsTo, Column, DataType, <PERSON><PERSON>ult, Foreign<PERSON>ey, Model, PrimaryKey, Table } from 'sequelize-typescript';
import { Event } from '.';

interface IEventSlider {
	id: number;
	event_id: number;
	from?: Date;
	to?: Date;
	status: boolean;
}

@Table({
	tableName: 'event_sliders',
	timestamps: false,
	paranoid: false,
})

class EventSlider extends Model implements IEventSlider {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare from: Date;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare to: Date;

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare event: Event;
}

export default EventSlider;
