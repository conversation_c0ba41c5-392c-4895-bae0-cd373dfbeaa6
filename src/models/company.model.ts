import { Table, Column, Model, DataType, PrimaryKey, AutoIncrement, AllowNull, Unique, CreatedAt, UpdatedAt, Default, BelongsToMany, HasMany, Index } from 'sequelize-typescript';
import { CompanyUser, User, Event } from '.';

interface ICompany {
	id: number;
	code: string;
	name: string;
	domain?: string;
	socials?: JSON;
	logo?: string;
	slogan?: string;
	email?: string;
	phone?: string;
	secondary_contacts?: JSON;
	address?: JSON;
	other_information?: JSON;
	about?: string;
	status: boolean;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'companies',
	timestamps: true,
	paranoid: false,
})

class Company extends Model implements ICompany {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.BIGINT)
	declare id: number;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare code: string;

	@AllowNull(false)
	@Index
	@Column(DataType.STRING)
	declare name: string;

	@AllowNull(true)
	@Index
	@Column(DataType.STRING)
	declare domain: string;

	@AllowNull(true)
	@Column(DataType.JSON)
	declare socials: JSON;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare logo: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare slogan: string;

	@Unique
	@Index
	@AllowNull(true)
	@Column(DataType.STRING)
	declare email: string;

	@Unique
	@Index
	@AllowNull(true)
	@Column(DataType.STRING)
	declare phone: string;

	@AllowNull(true)
	@Column(DataType.JSON)
	declare secondary_contacts: JSON;

	@AllowNull(true)
	@Column(DataType.JSON)
	declare address: JSON;

	@AllowNull(true)
	@Column(DataType.JSON)
	declare other_information: JSON;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare about: string;

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsToMany(() => User, {
		through: { model: () => CompanyUser },
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare users: User[];

	@HasMany(() => Event, {
		foreignKey: 'company_id',
	})
	declare events: Event[];
}

export default Company;
