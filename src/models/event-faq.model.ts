import { AllowNull, AutoIncrement, BelongsTo, Column, CreatedAt, DataType, <PERSON>fault, ForeignKey, Model, PrimaryKey, Table, UpdatedAt } from 'sequelize-typescript';
import { Event } from '.';

interface IEventFaq {
	id: number;
	event_id: number;
	question: string;
	answer?: string;
	suggest: boolean;
	status: boolean;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'event_faq',
	timestamps: true,
	paranoid: false,
})

class EventFaq extends Model implements IEventFaq {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@Column(DataType.STRING(1000))
	declare question: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare answer: string;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare suggest: boolean;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare event: Event;
}

EventFaq.prototype.toJSON = function () {
	const item = this.get();
	delete item.created_at;
	delete item.updated_at;
	return item;
};

export default EventFaq;
