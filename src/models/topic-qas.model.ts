import { AllowNull, AutoIncrement, BelongsTo, Column, CreatedAt, DataType, Default, ForeignKey, <PERSON>, Model, PrimaryKey, Table, Unique, UpdatedAt } from 'sequelize-typescript';
import Topic from './topic.model';

interface ITopicQas {
	id: number;
	topic_id: number;
	question: string;
	slug: string;
	answer: string;
	status: boolean;
	order: number | null;
	view: number | null;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'topic_qas',
	timestamps: true,
	paranoid: false,
})

class TopicQas extends Model implements ITopicQas {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Topic)
	@Column(DataType.INTEGER)
	declare topic_id: number;

	@AllowNull(false)
	@Unique
	@Column(DataType.STRING)
	declare question: string;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare slug: string;

	@AllowNull(false)
	@Column(DataType.TEXT)
	declare answer: string;

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@AllowNull(true)
	@Min(0)
	@Column(DataType.INTEGER)
	declare order: number | null;

	@AllowNull(true)
	@Min(0)
	@Column(DataType.INTEGER)
	declare view: number | null;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;
	
	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => Topic, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare topic: Topic;
}

export default TopicQas;
