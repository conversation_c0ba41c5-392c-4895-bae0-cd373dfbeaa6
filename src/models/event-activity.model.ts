import { AllowNull, AutoIncrement, BelongsTo, Column, DataType, Foreign<PERSON>ey, Model, PrimaryKey, Table } from 'sequelize-typescript';
import { Event } from '.';

interface IEventActivity {
	id: number;
	event_id: number;
	title: string;
	image?: string;
	details: string;
	status: boolean;
}

@Table({
	tableName: 'event_activities',
	timestamps: false,
	paranoid: false,
})

class EventActivity extends Model implements IEventActivity {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare title: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare image: string;

	@AllowNull(false)
	@Column(DataType.TEXT)
	declare details: string;

	@AllowNull(false)
	@Column(DataType.BOOLEAN)
	declare status: boolean;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare event: Event;
}

export default EventActivity;
