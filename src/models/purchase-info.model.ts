import { Table, Column, Model, DataType, PrimaryKey, AutoIncrement, AllowNull, CreatedAt, UpdatedAt, ForeignKey, BelongsTo, Default } from 'sequelize-typescript';
import { Event, EventTicket, TicketBuyer, User, Venue } from '.';

interface IPurchaseInfo {
	id: number;
	event_id: number;
	venue_id: number;
	ticket_id: number;
	ticket_buyer_id: number;
	date_id: number;
	user_id: number;
	date_time_id: number;
	is_scan_success: boolean;
	paid_amount: number;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'purchase_infos',
	timestamps: true,
	paranoid: false,
})

class PurchaseInfo extends Model implements IPurchaseInfo {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.BIGINT)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => Event)
	@Column(DataType.INTEGER)
	declare event_id: number;

	@AllowNull(false)
	@ForeignKey(() => Venue)
	@Column(DataType.INTEGER)
	declare venue_id: number;

	@AllowNull(false)
	@ForeignKey(() => EventTicket)
	@Column(DataType.INTEGER)
	declare ticket_id: number;

	@AllowNull(false)
	@ForeignKey(() => TicketBuyer)
	@Column(DataType.INTEGER)
	declare ticket_buyer_id: number;

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare date_id: number;

	@AllowNull(true)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare user_id: number;

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare date_time_id: number;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare is_scan_success: boolean;

	@AllowNull(true)
	@Column(DataType.DECIMAL)
	declare paid_amount: number;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => Event, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare event: Event;

	@BelongsTo(() => Venue, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare venue: Venue;

	@BelongsTo(() => EventTicket, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare event_ticket: EventTicket;

	@BelongsTo(() => User, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare user: User;

	@BelongsTo(() => TicketBuyer, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare ticket_buyer: TicketBuyer;
}

export default PurchaseInfo;
