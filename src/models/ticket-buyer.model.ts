import {
	Table,
	Column,
	Model,
	DataType,
	PrimaryKey,
	AutoIncrement,
	AllowNull,
	CreatedAt,
	UpdatedAt,
	ForeignKey,
	BelongsTo,
	Default,
	HasMany
} from 'sequelize-typescript';
import { PurchaseInfo, User, Event } from '.';

interface ITicketBuyer {
  id: number;
  user_id: number | null;
  event_id: number | null;
  full_name: string;
  phone_number: string;
  email: string;
  has_processed: boolean;
  was_payment_successful: boolean;
  total_amount: number | null;
  ticket_count: number | null;
  payment_method: string | null;
  payment_method_response: JSON | null;
  download_link: string | null;
  download_count: string | null;
  created_at: Date;
  updated_at: Date;
}

@Table({
	tableName: 'ticket_buyers',
	timestamps: true,
	paranoid: false,
})

class TicketBuyer extends Model implements ITicketBuyer {
  @PrimaryKey
  @AutoIncrement
  @Column(DataType.INTEGER)
	declare id: number;

  @AllowNull(true)
  @ForeignKey(() => User)
  @Column(DataType.INTEGER)
  declare user_id: number | null;

  @AllowNull(true)
  @ForeignKey(() => Event)
  @Column(DataType.INTEGER)
  declare event_id: number | null;

  @AllowNull(false)
  @Column(DataType.STRING)
  declare full_name: string;

  @AllowNull(false)
  @Column(DataType.STRING(20))
  declare phone_number: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  declare email: string;

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  declare has_processed: boolean;

  @AllowNull(false)
  @Default(false)
  @Column(DataType.BOOLEAN)
  declare was_payment_successful: boolean;

  @AllowNull(true)
  @Column(DataType.DECIMAL)
  declare total_amount: number | null;

  @AllowNull(true)
  @Column(DataType.INTEGER)
  declare ticket_count: number | null;

  @AllowNull(true)
  @Column(DataType.STRING)
  declare payment_method: string | null;

  @AllowNull(true)
  @Column(DataType.JSON)
  declare payment_method_response: JSON | null;

  @AllowNull(true)
  @Column(DataType.STRING)
  declare download_link: string | null;

  @AllowNull(true)
  @Column(DataType.STRING)
  declare download_count: string | null;

  @CreatedAt
  @AllowNull(false)
  @Column(DataType.DATE)
  declare created_at: Date;

  @UpdatedAt
  @AllowNull(false)
  @Column(DataType.DATE)
  declare updated_at: Date;
  
	@BelongsTo(() => User, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
  declare user: User;

	@HasMany(() => PurchaseInfo, {
		foreignKey: 'ticket_buyer_id',
	})
	declare purchase_infos: PurchaseInfo[];
}

export default TicketBuyer;
