import { AllowNull, AutoIncrement, BelongsTo, Column, CreatedAt, DataType, ForeignKey, Model, PrimaryKey, Table, UpdatedAt } from 'sequelize-typescript';
import User from './user.model';
import Category from './category.model';

interface IUserCategoryPreference {
	id: number;
	user_id: number;
	category_id: number;
	created_at: Date;
	updated_at: Date;
}

@Table({
	tableName: 'users_category_preferences',
	timestamps: true,
	paranoid: false,
})

class UserCategoryPreference extends Model implements IUserCategoryPreference {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare user_id: number;

	@AllowNull(false)
	@ForeignKey(() => Category)
	@Column(DataType.INTEGER)
	declare category_id: number;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare created_at: Date;
	
	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updated_at: Date;

	@BelongsTo(() => User, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare user: User;

	@BelongsTo(() => Category, {
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE'
	})
	declare category: Category;
}

export default UserCategoryPreference;
