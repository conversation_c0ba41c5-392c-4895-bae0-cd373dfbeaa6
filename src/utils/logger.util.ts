import { UserLog } from '@/models';

type UserLoggerType = {
    user_id?: number | string | null,
    company_id?: number | string | null,
    event_type: 'AUTH' | 'CATEGORY' | 'COMPANY' | 'ARTIST' | 'EVENT' | 'NEWSLETTER' | 'TICKET' | 'USER' | 'VENUE' | 'PAYMENT' | 'TOPIC' | 'GENERAL-INFO',
    type: boolean, // boolean (success or failed)
    details?: string, // Made optional
    action: 'AUTH-LOGIN' | 'AUTH-GOOGLE-LOGIN' | 'AUTH-FB-LOGIN' | 'AUTH-EMAIL-UPDATE' | 'AUTH-PASSWORD-CHANGE' | 'AUTH-PASSWORD-RESET-LINK' |'AUTH-RESET-PASSWORD'
            | 'AUTH-REGISTER-PARTNER' | 'AUTH-REGISTER-USER' | 'AUTH-RESEND-VERIFICATION-LINK' | 'AUTH-VERIFY-EMAIL' | 'AUTH-SEND-VERIFICATION-TOKEN'
            | 'CATEGORY-CREATE' | 'CATEGORY-UPDATE' | 'CATEGORY-DELETE' | 'COMPANY-UPDATE' | 'ARTIST-CREATE' | 'ARTIST-UPDATE' |'ARTIST-DELETE'
			| 'EVENT-CREATE' | 'EVENT-UPDATE' | 'EVENT-DELETE' | 'EVENT-FEATURED-UPDATE' | 'EVENT-SLIDER-UPDATE' | 'EVENT-LIKE-UPDATE' | 'EVENT-SUBSCRIPTION-UPDATE' |'NEWSLETTER-CREATE' | 'USER-UPDATE'
			| 'TICKET-GENERATED' | 'VENUE-CREATE' | 'VENUE-UPDATE' | 'VENUE-DELETE' | 'PAYMENT-KHALTI-INITIATE' | 'PAYMENT-BY-KHALTI' | 'PAYMENT-BY-ESEWA' | 'TOPIC-CREATE' | 'TOPIC-UPDATE' | 'TOPIC-DELETE'
			| 'TOPIC-QA-CREATE' | 'TOPIC-QA-UPDATE' | 'TOPIC-QA-DELETE' | 'GENERAL-INFO-SAVE' | 'USER-PREFERENCES-UPDATE'
    payloads?: { [key: string]: unknown } | null,
    ip_address: string,
    user_agent: string,
    read_access?: boolean,
    referrer?: string | null,
    method: string,
    api_endpoint: string
};

export const userLog = async ({
	user_id = null,
	company_id = null,
	event_type,
	type,
	details = '', // Default to empty string if not provided
	action,
	payloads,
	ip_address,
	user_agent,
	read_access = true, // Default to true if not provided
	referrer = null,
	method,
	api_endpoint
}: UserLoggerType) => {
	try {
		const createLog = await UserLog.create({
			user_id,
			company_id,
			event_type,
			type,
			details,
			action,
			payloads: payloads || null,
			ip_address,
			user_agent,
			read_access,
			referrer,
			method,
			api_endpoint
		});

		return !!createLog; // Returns `true` if created, `false` otherwise
	} catch {
		// console.error('Error logging user event:', error);
		return false;
	}
};
