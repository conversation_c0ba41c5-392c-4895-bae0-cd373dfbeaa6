import { Request } from 'express';

const getPage = (req: Request) => parseInt(req.query.page as string) || 1;
const getPerPage = (req: Request) => parseInt(req.query.perPage as string) || 20;

/**
 * The `paginated` function calculates the limit and offset values for pagination based on the request parameters.
 * @param {Request} req - The `req` parameter is of type `Request`. It represents the HTTP request object
 * that contains information about the incoming request, such as headers, query parameters, and body.
 * @param {number | null} [perPage=null] - The `perPage` parameter is the number of items to be displayed per
 * page in a paginated list. It determines the maximum number of items that will be returned in a single page.
 * If `perPage` is set to `null`, it means that the number of items per page is not specified and
 * @returns An object is being returned with the properties "limit" and "offset".
 */
export const paginated = (req: Request) => {
	const page = getPage(req);
	const limit = getPerPage(req);
	return {
		limit,
		offset: (page - 1) * limit,
	};
};

/**
 * The function paginatedData takes in data and a request object, and returns a paginated version of the data along
 * with metadata about the pagination.
 * @param {object | any} data - The `data` parameter is the object or array containing the paginated data. It should
 * have two properties: `count` and `rows`. `count` represents the total number of items in the data, and `rows`
 * represents the actual items.
 * @param {Request} req - The `req` parameter is an object representing the HTTP request. It typically contains
 * information such as the request method, headers, query parameters, and body. In this code snippet, it is used to
 * determine the current page and the number of items per page for pagination purposes.
 * @returns The function `paginatedData` returns an object with two properties: `data` and `meta`. The `data` property
 * contains the items from the `rows` property of the input `data` object. The `meta` property contains information
 * about the pagination, including the current page, number of items per page, total number of items, last page number,
 * next page number (if available), and previous page number (if available).
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const paginatedData = (data: object | any, req: Request) => {
	const { count: total, rows: items } = data;
	const page = getPage(req);
	const perPage = getPerPage(req);
	const totalItems = Array.isArray(total) ? total.reduce((acc, curr) => acc + curr.length, 0): total;
	return {
		rows: items || data,
		meta: {
			currentPage: page,
			perPage,
			total: totalItems,
			lastPage: Math.ceil(totalItems / perPage),
			nextPage: Math.ceil(totalItems / perPage) > page ? page + 1 : null,
			prevPage: page > 1 ? page - 1 : null,
		},
	};
};
