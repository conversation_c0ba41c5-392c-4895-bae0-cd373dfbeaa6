import { cleanEnv, str, port, bool, num } from 'envalid';
import 'dotenv/config';

export default cleanEnv(process.env, {
	NODE_ENV: str({
		choices: ['development', 'staging', 'production', 'test', 'local']
	}),
	APP_NAME: str({ default: 'Example App' }),
	HTTP_PORT: port({ default: 4000 }),
	CLIENT_ENDPOINT: str({ default: 'https://eticketnepal.com' }),
	DEBUG: bool({ default: false }),
	SUPPORT_EMAIL: str({ default: '<EMAIL>' }),
	LINK_VERIFICATION_EXPIRY: num({ default: 7200000 }), // 2 Hours
	RESEND_LINK_COOLDOWN: num({ default: 60000 }), // 1 Minute
	JWT_SALT: str(),
	POSTMARK_API_KEY: str(),
	
	DB_HOST: str(),
	DB_PORT: port(),
	DB_USER: str(),
	DB_PASSWORD: str(),
	DB_NAME: str(),

	// S3
	AWS_ACCESS_KEY_ID: str(),
	AWS_SECRET_KEY: str(),
	AWS_S3_ENDPOINT: str(),
	AWS_S3_BUCKET: str(),
	AWS_SES_REGION: str(),
});
