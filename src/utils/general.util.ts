import { PER_PAGE } from "@/configs/app-constants.config";
import { ActionQueryPayload, BreadcrumbItem } from "@/types/index.types";
import { EventModel } from "@/types/models/event.model";
import { UserModel } from "@/types/models/user.model";
import { capitalize } from 'lodash';

/**
 *
 * @param phone
 * @param type
 * @returns boolean
 */
export const isPhone = (phone: string, type: "mobile" | "landline") => {
	if (type === "mobile") {
		// Mobile: starts with 96/97/98 and has 10 digits total
		return /^(96|97|98)\d{8}$/.test(phone);
	}

	if (type === "landline") {
		// Landline: starts with 0 and has 8 or 9 digits total
		return /^0\d{7,8}$/.test(phone);
	}

	return false;
};


/**
 *
 * @param email
 * @returns boolean
 */
export const isEmail = (email: string) => {
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email);
}

/**
 *
 * @param payload
 * @returns queryParams
 */
export const constructQuery = <T>(payload?: ActionQueryPayload & T) => {
	const queryParams: { [key: string]: number | string | boolean } = {
		page: payload?.page || 1,
		perPage: payload?.perPage || PER_PAGE,
	};

	if (payload?.type) queryParams.type = payload.type;
	if (payload?.category) queryParams.category = payload.category;
	if (payload?.search) queryParams.search = payload.search;
	if (payload?.price) queryParams.price = payload.price;
	if (payload?.date) queryParams.date = payload.date;
	if (payload?.getMainCategories) queryParams.getMainCategories = payload.getMainCategories;
	if (payload?.getCount) queryParams.getCount = payload.getCount;
	if (payload?.getNavCategories) queryParams.getNavCategories = payload.getNavCategories;
	if (payload?.getQas) queryParams.getQas = payload.getQas;
	if (payload?.latitude) queryParams.latitude = payload.latitude;
	if (payload?.longitude) queryParams.longitude = payload.longitude;

	return queryParams;
};

/**
 *
 * @param string
 * @param limit
 * @returns string
 */
export const stringLimit = (string: string, limit: number) => {
	if (!string) return '';

	if (string.length <= limit) return string;

	return string.slice(0, limit) + '.....';
};


/**
 *
 * @param sluggerData
 * @param type
 * @returns BreadcrumbItem[]
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const slugger = (sluggerData: any, type: "travel" | "event" | "venue" = "travel"): BreadcrumbItem[] => {
	const slugList: BreadcrumbItem[] = [{ name: "Home", link: "/" }];

	switch (type) {
	case "travel":
		if (sluggerData.category) {
			slugList.push({ name: "Travels", link: "/travels" });
			slugList.push({ name: sluggerData.category.name, link: sluggerData.category.slug });
		}
		if (sluggerData.title && sluggerData.slug) {
			slugList.push({ name: sluggerData.title, link: sluggerData.slug });
		}
		break;

	case "event":
		slugList.push({ name: "Events", link: "/events" });
		if (sluggerData.title && sluggerData.slug) {
			slugList.push({ name: sluggerData.title, link: sluggerData.slug });
		}
		break;

	case "venue":
		slugList.push({ name: "Venues", link: "/venues" });
		if (sluggerData.name && sluggerData.slug) {
			slugList.push({ name: sluggerData.name, link: sluggerData.slug });
		}
		break;
	}

	return slugList;
};

/**
 *
 * @param text
 * @returns string
 */
export function convertNewLinesToBr(text: string): string {
	return text.replace(/\n/g, '<br><br>');
}

/**
 *
 * @param url
 * @returns boolean
 */
export const isValidUrl = (domain: string): boolean => {
	const regex = /^(https?:\/\/)?([\w-]+\.)+[\w-]{2,}(\/[^\s]*)?$/i;
	return regex.test(domain);
};


/**
 *
 * @param value
 * @returns string
 */
export const formatNameInput = (value: string): string => {
	const cleaned = value.replace(/[^a-zA-Z\s]/g, '');

	return capitalize(cleaned);
};

/**
 *
 * @param str
 * @returns slug
 */
export const generateSlug = (str: string): string => {
	return str
		.replace(/\(.*?\)/g, '') // Remove any content inside parentheses
		.replace(/'/g, '') // Remove apostrophes
		.trim() // Remove leading and trailing whitespace
		.replace(/[^a-zA-Z0-9\s]/g, '-') // Replace non-word characters with hyphens
		.replace(/\s+/g, '-') // Replace spaces with hyphens
		.toLowerCase() // Convert to lowercase
		.replace(/^-+|-+$/g, '') // Trim leading/trailing hyphens
		.replace(/-+/g, '-'); // Replace consecutive hyphens with a single hyphen
};

/**
 *
 * @param event
 * @returns string
 */
export const getEventPrice = (event: EventModel): string | null => {
	if (!event.tickets || event.tickets.length === 0) return null;

	const hasFreeTicket = event.tickets.some(ticket => ticket.ticket_type === 'free');
	if (hasFreeTicket) return 'Free Event';

	const minPrice = Math.min(...event.tickets.map(ticket => ticket.price ?? Infinity));
	return minPrice !== Infinity ? `NPR ${minPrice}` : null;
};

/**
 *
 * @param imagePath
 * @returns url
 */
export const getFullImageUrl = (imagePath: string): string => {
	return `${process.env.NEXT_PUBLIC_IMAGE_BASE_URL}/${imagePath}`;
};

/**
 *
 * @param user
 * @returns full name
 */
export function getFullName(user: UserModel) {
	if (!user || !user.f_name) return '';
	if (!user.l_name) return user.f_name.trim();
	return (user.f_name + ' ' + user.l_name).trim();
}
