import { User } from '@/models';
import crypto from 'crypto';
import envUtil from './env.util';
import { type Request } from 'express';
import jwt from 'jsonwebtoken';

/**
 * 
 * @param length 
 * @returns random string of given length
 */
export const generateRandomString = (length: number) => {
	const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
	let randomString = '';
	
	for (let i = 0; i < length; i++) {
		const randomIndex = crypto.randomInt(characters.length);
		randomString += characters.charAt(randomIndex);
	}
	return randomString;
};

/**
 * 
 * @param min 
 * @param max 
 * @returns random number between min and max
 */
export const generateRandomNumber = (min: number, max: number) => {
	min = Math.ceil(min);
	max = Math.floor(max);
	return Math.floor(Math.random() * (max - min)) + min;
};

/**
 * 
 * @param str 
 * @returns slug
 */
export const generateSlug = (str: string): string => {
	return str
		.replace(/\(.*?\)/g, '') // Remove any content inside parentheses
		.replace(/'/g, '') // Remove apostrophes
		.trim() // Remove leading and trailing whitespace
		.replace(/[^a-zA-Z0-9\s]/g, '-') // Replace non-word characters with hyphens
		.replace(/\s+/g, '-') // Replace spaces with hyphens
		.toLowerCase() // Convert to lowercase
		.replace(/^-+|-+$/g, '') // Trim leading/trailing hyphens
		.replace(/-+/g, '-'); // Replace consecutive hyphens with a single hyphen
};

/**
 * Check if an object has only empty values
 * @param obj 
 * @returns boolean
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const hasOnlyEmptyValues = (obj: Record<string, any>): boolean => {
	return Object.values(obj).every(value =>
		value === '' ||
			value === null ||
			value === undefined ||
			(Array.isArray(value) && value.length === 0)
	);
};

/**
 * 
 * @param req 
 * @returns decoded user
 */
export const getDecodedUser = (req: Request): Partial<User> | null => {
	const cookieHeader = req.headers.cookie;

	if (!cookieHeader) return null;

	const authToken = cookieHeader
		.split('; ')
		.find((cookie) => cookie.startsWith('_eticket_at_='))
		?.split('=')[1];

	if (!authToken) return null;

	try {
		return jwt.verify(authToken, envUtil.JWT_SALT) as Partial<User>;
	} catch (error) {
		console.error(error);
		return null;
	}
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const getClientIP = (req: any): string => {
	if (!req?.headers) return ''; // Ensure `req` and `req.headers` exist

	const forwarded = req.headers['x-forwarded-for'];

	if (forwarded && typeof forwarded === 'string' && forwarded.trim().length > 0) {
		return forwarded.split(',')[0]!.trim(); // Get the first IP if multiple exist
	}

	return req?.socket?.remoteAddress ?? ''; // Use optional chaining and nullish coalescing
};
