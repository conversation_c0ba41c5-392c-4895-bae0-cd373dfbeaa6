import { type Application } from 'express';
import klawSync from 'klaw-sync';
import path from 'path';

export const useController = (app: Application): void => {
	const controllerPaths = klawSync(path.join(__dirname, '../controllers'), { nodir: true });
	let controllersCount = 0;
	controllerPaths.forEach((file) => {
		// eslint-disable-next-line @typescript-eslint/no-require-imports
		const controller = require(file.path);
		if (controller.default) {
			app.use('/api/', controller.default);
			controllersCount++;
		}
	});
	// eslint-disable-next-line no-console
	console.log(`Loaded ${controllersCount} controllers.`);
};
