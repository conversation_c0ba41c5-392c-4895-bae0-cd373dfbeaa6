/* eslint-disable @typescript-eslint/no-explicit-any */
import { type Request, type Response, NextFunction } from 'express';

export const errorWrapper = (handler: any): any => {
	return async (req: Request, res: Response, next: NextFunction) => {
		try {
			await handler(req, res, next);
		} catch (error: any) {
			if (error && Array.isArray(error)) {
				return res.invalid(error);
			}
			console.error('An error occurred', error);
			return res.failure('Internal Server Error', 500);
		}
	};
};
