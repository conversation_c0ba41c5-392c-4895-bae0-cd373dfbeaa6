import Cookies from 'js-cookie';
import CryptoJS from 'crypto-js';

// Type definitions
type CookieValue = string | object;
type EncryptedValue = string;

// Environment variable validation
const SECRET_KEY: string = process.env.NEXT_PUBLIC_COOKIE_SECRET_KEY || '';
if (!SECRET_KEY) {
	throw new Error('NEXT_PUBLIC_COOKIE_SECRET_KEY environment variable is not set');
}

/**
 * Encrypts data using AES encryption
 * @param data - Data to encrypt
 * @returns Encrypted string
 * @throws Error if encryption fails
 */
export const encryptData = (data: CookieValue): EncryptedValue => {
	try {
		const stringData = typeof data === 'object' ? JSON.stringify(data) : String(data);
		return CryptoJS.AES.encrypt(stringData, SECRET_KEY).toString();
	} catch (error) {
		throw new Error(`Encryption failed: ${(error as Error).message}`);
	}
};

/**
 * Decrypts encrypted data
 * @param encryptedData - Data to decrypt
 * @returns Decrypted string or parsed object
 * @throws Error if decryption fails
 */
export const decryptData = (encryptedData: EncryptedValue): CookieValue => {
	try {
		const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
		const decryptedString = bytes.toString(CryptoJS.enc.Utf8);

		if (!decryptedString) {
			throw new Error('Decryption resulted in an empty string');
		}

		try {
			// Attempt to parse as JSON in case it's an object
			return JSON.parse(decryptedString);
		} catch {
			// If parsing fails, return as string
			return decryptedString;
		}
	} catch (error) {
		throw new Error(`Decryption failed: ${(error as Error).message}`);
	}
};

/**
 * Sets an encrypted cookie
 * @param name - Cookie name
 * @param value - Cookie value (string or object)
 * @param options - Cookie options (expiry, path, etc.)
 * @throws Error if cookie setting fails
 */
export const setEncryptedCookie = (
	name: string,
	value: CookieValue,
	options: Cookies.CookieAttributes = {}
): void => {
	try {
		const encrypted = encryptData(value);

		Cookies.set(name, encrypted, options);
	} catch (error) {
		throw new Error(`Failed to set cookie "${name}": ${(error as Error).message}`);
	}
};

/**
 * Gets and decrypts a cookie value
 * @param name - Cookie name
 * @returns Decrypted cookie value or null if not found
 */
export const getEncryptedCookie = (name: string): CookieValue | null => {
	try {
		const encryptedValue = Cookies.get(name);
		if (!encryptedValue) return null;

		return decryptData(encryptedValue);
	} catch (error) {
		console.error(`Failed to get cookie "${name}":`, error);
		return null;
	}
};

/**
 * Sets a normal cookie
 * @param name - Cookie name
 * @param value - Cookie value (string or object)
 * @param options - Cookie options (expiry, path, etc.)
 * @throws Error if cookie setting fails
 */
export const setCookie = (
	name: string,
	value: string | object,
	options: Cookies.CookieAttributes = {}
): void => {
	try {
		const cookieValue = typeof value === 'string' ? value : JSON.stringify(value);
		Cookies.set(name, cookieValue, options);
	} catch (error) {
		throw new Error(`Failed to set cookie "${name}": ${(error as Error).message}`);
	}
};

/**
 * Gets a normal cookie value
 * @param name - Cookie name
 * @returns Cookie value as string or object, or null if not found
 */
export const getCookie = (name: string): string | object | null => {
	try {
		const cookieValue = Cookies.get(name);
		if (!cookieValue) return null;

		try {
			// Attempt to parse as JSON; if it fails, return the string value
			return JSON.parse(cookieValue);
		} catch {
			return cookieValue;
		}
	} catch (error) {
		console.error(`Failed to get cookie "${name}":`, error);
		return null;
	}
};

/**
 * Removes a specific cookie
 * @param name - Cookie name
 * @param options - Cookie removal options
 */
export const removeCookie = (name: string): void => {
	try {
		const isProduction = process.env.NODE_ENV === 'production';
		const options: Cookies.CookieAttributes = {
			secure: isProduction,
			sameSite: isProduction ? 'None' : 'Lax',
			domain: isProduction ? '.eticketnepal.com' : undefined,
			path: '/',
		};
		Cookies.remove(name, options);
	} catch (error) {
		console.error(`Failed to remove cookie "${name}":`, error);
	}
};

/**
 * Clears all cookies
 * @param options - Cookie removal options
 */
export const clearAllCookies = (): void => {
	try {
		const isProduction = process.env.NODE_ENV === 'production';
		const options: Cookies.CookieAttributes = {
			secure: isProduction,
			sameSite: isProduction ? 'None' : 'Lax',
			domain: isProduction ? '.eticketnepal.com' : undefined,
			path: '/',
		};

		const allCookies = Cookies.get();
		Object.keys(allCookies).forEach(cookieName => {
			Cookies.remove(cookieName, options);
		});
	} catch (error) {
		console.error('Failed to clear all cookies:', error);
	}
};

/**
 * Checks if the user is authenticated
 * @returns True if user is authenticated, false otherwise
 */
export const isAuthenticatedUser = (): boolean => {
	const accessToken = getCookie('_eticket_at_');
	const refreshToken = getCookie('_eticket_rt_');
	const userInfo = getCookie('_eticket_user_');

	return Boolean(accessToken && refreshToken && userInfo);
}
