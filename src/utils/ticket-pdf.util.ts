import path from 'path';
import fs from 'fs/promises';
import handlebars from 'handlebars';
import puppeteer from 'puppeteer';
import qr from 'qrcode';

interface TicketPdfData {
	ticketNo: string;
	eventName: string;
	eventDate: string;
	eventLocation: string;
	userName: string;
	ticketType: string;
	price: number | string;
	issueDate: string;
	eventId: string;
}

export async function generateTicketPdf(ticketData: TicketPdfData): Promise<Buffer> {
	// Generate QR code
	const ticketQr = await qr.toDataURL(
		JSON.stringify({
			ticketNo: ticketData.ticketNo,
			eventId: ticketData.eventId,
			eventName: ticketData.eventName,
			price: ticketData.price,
		}),
		{ version: 7, type: 'image/png', width: 300 }
	);

	// Read and compile the Handlebars template
	const templatePath = path.resolve(__dirname, '../templates/ticket.html');
	const templateContent = await fs.readFile(templatePath, 'utf8');
	const template = handlebars.compile(templateContent);
	const html = template({ ...ticketData, ticketQr });

	// Generate PDF using Puppeteer
	const browser = await puppeteer.launch({
		headless: true,
		args: ['--no-sandbox', '--disable-setuid-sandbox'],
	});
	const page = await browser.newPage();
	await page.setContent(html, { waitUntil: 'networkidle0' });

	const uint8Array = await page.pdf({
		format: 'A4',
		printBackground: true,
	});

	await browser.close();
	return Buffer.from(uint8Array);
}
