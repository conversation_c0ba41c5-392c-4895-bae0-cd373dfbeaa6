import axios from 'axios';
import { message } from 'antd';
import { clearAllCookies } from './cookie.util';

axios.defaults.baseURL = process.env.NEXT_PUBLIC_APP_BASE_API;
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.withCredentials = true;

axios.interceptors.response.use(
	function (response) {
		if (response.data && response.data.message) {
			message.success({
				content: response.data.message,
				style: {
					fontSize: 15,
					color: '#1d2026',
					opacity: 1,
				}
			});
		}
		return response;
	},
	function (error) {
		if (error.response && error.response.data) {
			switch (error.response.status) {
			case 401:
				if (error.response.data.message) {
					message.error({
						content: error.response.data.message,
						style: {
							fontSize: 15,
							color: '#FF0000',
							opacity: 1,
						}
					});
				}
				clearAllCookies();
				window.location.href = '/user/login';
				break;
			default:
				if (error.response.data.message) {
					message.error({
						content: error.response.data.message,
						style: {
							fontSize: 15,
							color: '#FF0000',
							opacity: 1,
						}
					});
				}
				break;
			}
		}
		return Promise.reject(error);
	}
);

export default axios;
