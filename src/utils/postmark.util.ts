import { ServerClient, Message, Attachment } from 'postmark';
import { emailTemplate } from './template.util';
import envUtil from './env.util';

const client = new ServerClient(envUtil.POSTMARK_API_KEY);

export const sendEmail = async (payload: Message) => {
	return await client.sendEmail(payload);
};

export const registrationVerificationEmail = (email: string, registrationLink: string): Message => {
	return {
		From: envUtil.SUPPORT_EMAIL,
		To: email,
		Subject: `Welcome to ${envUtil.APP_NAME}`,
		HtmlBody: emailTemplate(`
            <img src="https://eticketnepal.com/images/logo-full.png" alt="eticketnepal"/>
            <h1>
              Welcome to ${envUtil.APP_NAME}!
            </h1>
            <p>Hello,</p>
            <p>We're excited to have you join the ${envUtil.APP_NAME} community, where you can explore and book tickets for the most exciting events near you.</p>

            <p><strong>To get started, simply click on the button below to verify your email and start discovering amazing events and experiences.</strong></p>
            <br/>
            <p>
              <a href="${registrationLink}" style="display: inline-block; padding: 10px 15px; background-color: #c71782; color: #ffffff; text-decoration: none; font-size: 16px; border-radius: 10px;">Verify Your Email</a>
            </p>

            <p>If the button above doesn't work, you can copy this link into your browser:</p>
              ${registrationLink}
            </p>
            <br/>
            <p>Once verified, you'll have access to the best concerts, sports events, festivals, and shows in your area.</p>

            <p style="color:#999">Don't miss out on your next great experience! We're here to help you discover the best events happening near you.</p>

            <p>Best Regards, <br/>
            <strong>${envUtil.APP_NAME} Team</strong>
            </p>
        `),
		TextBody: `Welcome to ${envUtil.APP_NAME}! To verify your email and start discovering amazing events, please visit: ${registrationLink}`,
		MessageStream: 'outbound'
	};
};

export const emailChangeEmail = (email: string, oldEmail: string, verificationCode: string): Message => {
	return {
		From: envUtil.SUPPORT_EMAIL,
		To: email,
		Subject: 'Email Change Verification Code',
		HtmlBody: emailTemplate(`
            <img src="https://eticketnepal.com/images/logo-full.png" alt="eticketnepal"/>
            <h1>
              Greetings from ${envUtil.APP_NAME}
            </h1>
            <p>Hello,</p>
            <p>We received a request to change your email address for your <strong>${oldEmail}</strong> account.</p>

            <p><strong>Your verification code is:</strong></p>
            <div style="background-color: #f4f4f4; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
              <span style="font-size: 24px; font-weight: bold; letter-spacing: 5px;">${verificationCode}</span>
            </div>

            <p>Enter this code in the application to confirm your new email address. This code will expire in 2 hours.</p>

            <p style="color: #666;">If you didn't request this email change, please ignore this email or contact our support team immediately if you believe this is suspicious activity.</p>

            <p>Best Regards, <br/>
            <strong>${envUtil.APP_NAME} Team</strong>
            </p>
        `),
		TextBody: `Hello, we received a request to change you email address for your (${oldEmail}) account. Your Verification Code is: ${verificationCode}`,
		MessageStream: 'outbound'
	};
};

export const forgotPasswordEmail = (email: string, resetLink: string): Message => {
	return {
		From: envUtil.SUPPORT_EMAIL,
		To: email,
		Subject: 'Password Reset Email',
		HtmlBody: emailTemplate(`
            <img src="https://eticketnepal.com/images/logo-full.png" alt="eticketnepal"/>
            <h1>
              Welcome to ${envUtil.APP_NAME}!
            </h1>
            <p>Hello,</p>
            <p>We received a request to reset your the password for your <strong>${email}</strong> account.</p>

            <p><strong>To get started, simply click on the button below to navigate to password reset page and reset your password.</strong></p>
            <br/>
            <p>
              <a href="${resetLink}" style="display: inline-block; padding: 10px 15px; background-color: #c71782; color: #ffffff; text-decoration: none; font-size: 16px; border-radius: 10px;">Reset Your Password</a>
            </p>

            <p>If the button above doesn't work, you can copy this link into your browser:</p>
              ${resetLink}
            </p>
            <p>The link will expire in 2 hours.</p>

            <p style="color: #666;">If you didn't request this email change, please ignore this email or contact our support team immediately if you believe this is suspicious activity.</p>

            <p>Best Regards, <br/>
            <strong>${envUtil.APP_NAME} Team</strong>
            </p>
        `),
		TextBody: `Hello, we received a request to reset the password for your (${email}) account. Please visit: ${resetLink} to reset your password.`,
		MessageStream: 'outbound'
	};
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const ticketEmail = (email: string, ticketData: any, attachments: Attachment[]): Message => {
	return {
		From: envUtil.SUPPORT_EMAIL,
		To: email,
		Subject: `Your Ticket Confirmation for ${ticketData.eventName}`,
		HtmlBody: emailTemplate(/* html */`
      <div style="line-height: 1.6; max-width: 700px; margin: auto; padding: 20px;">
        <div style="text-align: center;">
          <img src="https://eticketnepal.com/images/logo-full.png" alt="eticketnepal" style="max-width: 200px; margin-bottom: 20px;" />
        </div>

        <h1 style="color: #222; font-size: 24px;">Thank You for Your Purchase!</h1>
        
        <p>Hello, ${ticketData.userName}</p>
        
        <p>Your ticket for <strong>${ticketData.eventName}</strong> has been successfully booked.</p>
        
        <div style="background-color: #f9f9f9; padding: 15px; border-radius: 8px; margin-top: 15px;">
          <h3 style="margin-top: 0;">Ticket Details</h3>
          <ul style="list-style: none; padding: 0;">
            <li><strong>Event:</strong> ${ticketData.eventName}</li>
            <li><strong>Date:</strong> ${ticketData.eventDate}</li>
            <li><strong>Venue:</strong> ${ticketData.eventLocation}</li>
            <li><strong>Number of Tickets:</strong> ${ticketData.ticketCount}</li>
            <li><strong>Total Amount:</strong> ${ticketData.totalAmount}</li>
          </ul>
        </div>

        <p style="margin-top: 20px;">Your ticket is attached to this email.</p>

        <p>If you have any questions, feel free to reach out to our support team anytime.</p>

        <div style="text-align: center; margin-top: 30px;">
          <a href="${envUtil.CLIENT_ENDPOINT}/user/dashboard/booked-events" style="background-color: #c71782; color: white; padding: 12px 20px; border-radius: 5px; text-decoration: none; font-weight: bold;">
            View Your Booked Events
          </a>
        </div>

        <p style="margin-top: 30px;">Best Regards,<br/>
        <strong>${envUtil.APP_NAME} Team</strong></p>
      </div>
    `),
		TextBody: `Hello, your ticket for ${ticketData.eventName} has been successfully booked. Please check the attached PDF for details.`,
		Attachments: attachments,
		MessageStream: 'outbound'
	};
};

export const newsletterSubscriptionEmail = (email: string): Message => {
	return {
		From: envUtil.SUPPORT_EMAIL,
		To: email,
		Subject: 'Welcome to Our Newsletter!',
		HtmlBody: emailTemplate(`
            <img src="https://eticketnepal.com/images/logo-full.png" alt="eticketnepal"/>
            <h1>Welcome to ${envUtil.APP_NAME} Newsletter!</h1>
            <p>Hello,</p>
            <p>Thank you for subscribing to our newsletter! 🎉</p>
            <p>You'll now receive the latest updates, exclusive offers, and news straight to your inbox.</p>
            <p>We’re excited to have you on board!</p>
            <br/>
            <p style="color: #666;">If you didn’t subscribe or received this email by mistake, you can safely ignore it.</p>
            <p>Best Regards, <br/>
            <strong>${envUtil.APP_NAME} Team</strong>
            </p>
        `),
		TextBody: `Hello, thank you for subscribing to ${envUtil.APP_NAME} newsletter! You'll receive the latest updates and exclusive offers in your inbox.`,
		MessageStream: 'outbound'
	};
};

export const rejectEventEmail = (email: string, reason: string, eventName: string): Message => {
	return {
		From: envUtil.SUPPORT_EMAIL,
		To: email,
		Subject: `Event Submission Rejected: ${eventName}`,
		HtmlBody: emailTemplate(`
      <img src="https://eticketnepal.com/images/logo-full.png" alt="eticketnepal" />
      <h1>Event Rejected</h1>
      <p>Hello,</p>
      <p>We regret to inform you that your event submission for <strong>${eventName}</strong> has been rejected.</p>
      <p><strong>Reason for rejection:</strong></p>
      <blockquote style="background-color: #f8f8f8; padding: 15px; border: 3px solid #c71782; border-radius: 10px;">
        ${reason}
      </blockquote>

      <p>If you believe this was a mistake or need further clarification, feel free to contact our support team.</p>

      <p>Thank you for using ${envUtil.APP_NAME}.</p>

      <p>Best Regards, <br/>
      <strong>${envUtil.APP_NAME} Team</strong>
      </p>
    `),
		TextBody: `Hello, your event submission for "${eventName}" was rejected. Reason: ${reason}. Please contact support if you need clarification.`,
		MessageStream: 'outbound'
	};
};

// * For partners
export const cancelEventEmail = (email: string, reason: string, eventName: string): Message => {
	return {
		From: envUtil.SUPPORT_EMAIL,
		To: email,
		Subject: `Event Cancelled: ${eventName}`,
		HtmlBody: emailTemplate(`
      <img src="https://eticketnepal.com/images/logo-full.png" alt="eticketnepal" />
      <h1>Event Cancelled</h1>
      <p>Hello,</p>
      <p>We regret to inform you that your event <strong>${eventName}</strong> has been cancelled.</p>
      <p><strong>Reason for cancellation:</strong></p>
      <blockquote style="background-color: #f8f8f8; padding: 15px; border: 3px solid #c71782; border-radius: 10px;">
        ${reason}
      </blockquote>

      <p>If you have any questions or need further assistance, please contact our support team.</p>

      <p>Thank you for using ${envUtil.APP_NAME}.</p>

      <p>Best Regards, <br/>
      <strong>${envUtil.APP_NAME} Team</strong>
      </p>
    `),
		TextBody: `Hello, your event "${eventName}" has been cancelled. Reason: ${reason}. Please contact support if you need assistance.`,
		MessageStream: 'outbound'
	};
};

// * For ticket buyers
export const notifyUserEventCancelledEmail = (
	email: string,
	userName: string,
	eventName: string,
	reason: string
): Message => {
	return {
		From: envUtil.SUPPORT_EMAIL,
		To: email,
		Subject: `Event Cancelled: ${eventName}`,
		HtmlBody: emailTemplate(`
      <img src="https://eticketnepal.com/images/logo-full.png" alt="eticketnepal" />
      <h1>Event Cancelled</h1>
      <p>Hi ${userName},</p>
      <p>We regret to inform you that the event <strong>${eventName}</strong> you purchased tickets for has been cancelled.</p>
      <p><strong>Reason for cancellation:</strong></p>
      <blockquote style="background-color: #f8f8f8; padding: 15px; border: 3px solid #c71782; border-radius: 10px;">
        ${reason}
      </blockquote>

      <p>We apologize for the inconvenience. If you have already purchased tickets, our support team will contact you shortly regarding the refund process.</p>

      <p>If you have any questions, feel free to reach out to our support team.</p>

      <p>Thank you for choosing ${envUtil.APP_NAME}.</p>

      <p>Best Regards, <br/>
      <strong>${envUtil.APP_NAME} Team</strong>
      </p>
    `),
		TextBody: `Hi ${userName}, the event "${eventName}" you purchased tickets for has been cancelled. Reason: ${reason}. We will contact you regarding the refund. For assistance, please contact our support team.`,
		MessageStream: 'outbound'
	};
};

// * For ticket buyers
export const notifyUserEventDelayedEmail = ({
	email,
	userName,
	eventName,
	oldStartDateTime,
	oldEndDateTime,
	newStartDateTime,
	newEndDateTime
} : {
  email: string,
	userName: string,
	eventName: string,
	oldStartDateTime: string,
	oldEndDateTime: string,
	newStartDateTime: string,
	newEndDateTime: string
}): Message => {
	return {
		From: envUtil.SUPPORT_EMAIL,
		To: email,
		Subject: `Event Delayed: ${eventName}`,
		HtmlBody: emailTemplate(/* html */`
      <img src="https://eticketnepal.com/images/logo-full.png" alt="eticketnepal" />
      <h1>Event Delayed</h1>
      <p>Hi ${userName},</p>
      <p>We would like to inform you that the event <strong>${eventName}</strong> you purchased tickets for has been delayed.</p>

      <p><strong>Previous Schedule:</strong><br/>
      <strong>Start:</strong> ${oldStartDateTime}<br/>
      <strong>End:</strong> ${oldEndDateTime}</p>

      <p><strong>New Schedule:</strong><br/>
      <strong>Start:</strong> ${newStartDateTime}<br/>
      <strong>End:</strong> ${newEndDateTime}</p>

      <p>Your tickets will remain valid for the rescheduled event. If you are unable to attend or have any questions, please reach out to our support team.</p>

      <p>We apologize for any inconvenience and appreciate your understanding.</p>

      <p>Thank you for choosing ${envUtil.APP_NAME}.</p>

      <p>Best Regards, <br/>
      <strong>${envUtil.APP_NAME} Team</strong>
      </p>
    `),
		TextBody: `Hi ${userName}, the event "${eventName}" you purchased tickets for has been delayed. Your tickets are still valid. For assistance, please contact our support team.`,
		MessageStream: 'outbound'
	};
};
