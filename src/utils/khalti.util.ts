// const axios = require('axios');

// Function to verify Khalti Payment
// async function verifyKhaltiPayment(pidx) {
// 	const headersList = {
// 		'Authorization': `Key ${process.env.KHALTI_SECRET_KEY}`,
// 		'Content-Type': 'application/json',
// 	};

// 	const bodyContent = JSON.stringify({ pidx });

// 	const reqOptions = {
// 		url: `${process.env.KHALTI_GATEWAY_URL}/api/v2/epayment/lookup/`,
// 		method: 'POST',
// 		headers: headersList,
// 		data: bodyContent,
// 	};

// 	try {
		
// 		const response = await axios.request(reqOptions);
// 		return response.data;
// 	} catch (error) {
// 		console.error('Error verifying Khalti payment:', error);
// 		throw error;
// 	}
// }
interface KhaltiPaymentResponse {
	payment_url: string;
	pidx: string;
	// add any additional fields if present in the response
}

export const verifyKhaltiPayment = async (pidx: string) => {
	const url = `${process.env.KHALTI_GATEWAY_URL}epayment/lookup/`;
	const headers = {
	  'Authorization': `Key ${process.env.KHALTI_SECRET_KEY}`,
	  'Content-Type': 'application/json'
	};
  
	try {
	  const response = await fetch(url, {
			method: 'POST',
			headers,
			body: JSON.stringify({ pidx })
	  });

	  if (!response.ok) {
			const errorData = await response.text();
			throw new Error(`Error verifying Khalti payment: ${errorData}`);
	  }
  
	  const data = await response.json();
	  return data;
	} catch (error) {
	  console.error(error);
	  throw error;
	}
};

// Function to initialize Khalti Payment
export const initializeKhaltiPayment = async (
	details: Record<string, unknown>
): Promise<KhaltiPaymentResponse> => {
	if (!process.env.KHALTI_SECRET_KEY || !process.env.KHALTI_GATEWAY_URL) {
	  throw new Error('Khalti API credentials are missing');
	}
  
	const headers = {
	  Authorization: `Key ${process.env.KHALTI_SECRET_KEY}`,
	  'Content-Type': 'application/json',
	};
  
	try {
	  const response = await fetch(
			`${process.env.KHALTI_GATEWAY_URL}epayment/initiate/`,
			{
				method: 'POST',
				headers,
				body: JSON.stringify(details),
			}
	  );

	  if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
	  }
  
	  const data: KhaltiPaymentResponse = await response.json();
	  if (!data.payment_url || !data.pidx) {
			throw new Error('Invalid response from Khalti API');
	  }
  
	  return data;
	} catch (error) {
	  console.error('Error initializing Khalti payment:', error);
	  throw error;
	}
};
