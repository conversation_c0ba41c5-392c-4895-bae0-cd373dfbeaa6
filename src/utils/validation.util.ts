import { Errors } from "@/types/index.types";
import { UserModel } from "@/types/models/user.model";
import { PartnerRegistrationType } from "@/types/forms/partner-registration.type";
import { UserSignupType } from "@/types/forms/user-signup.type";
import { isEmail, isPhone, isValidUrl } from "./general.util";
import { ActivityDataType, AddEventType, FaqDataType, VenueDataType } from "@/types/forms/add-event.type";
import { AddCategoryType } from "@/types/forms/add-category.type";
import { AddArtistType } from "@/types/forms/add-artist.type";
import { AddVenueType } from "@/types/forms/add-venue.type";
import { CompanyModel } from "@/types/models/company.model";
import { UpdateGeneralInfoType } from "@/types/forms/update-general-info.type";

export const partnerRegistrationValidation = (values: PartnerRegistrationType) => {
	// const errors: Errors<PartnerRegistrationType> = {};
	const errors: Errors<Omit<PartnerRegistrationType, 'company'> & {
		company: Pick<CompanyModel, 'name' | 'phone' | 'email'> & { address?: string };
	}> = {};

	if (!values.partnerCategory) errors.partnerCategory = 'Please select a category';

	if (!values.company.name) errors.company = { ...errors.company, name: 'Please enter your company name' };

	if (!values.company.phone && !values.company.email) errors.company = { ...errors.company, phone: 'Please enter your phone number', email: 'Please enter your email address' };

	if (values.company.phone && !isPhone(values.company.phone, 'mobile')) errors.company = { ...errors.company, phone: 'Please enter a valid phone number' };

	if (values.company.email && !isEmail(values.company.email)) errors.company = { ...errors.company, email: 'Please enter a valid email address' };

	if (!values.company.address) errors.company = { ...errors.company, address: 'Please enter your company address' };

	if (!values.auth.email) errors.auth = { ...errors.auth, email: 'Please enter your email address' };
	else if (!isEmail(values.auth.email)) errors.auth = { ...errors.auth, email: 'Please enter a valid email address' };

	if (!values.auth.password) errors.auth = { ...errors.auth, password: 'Please enter your password' };
	else if (values.auth.password.length < 6) errors.auth = { ...errors.auth, password: 'Password must be at least 6 characters' };

	if (!values.auth.password_confirmation) errors.auth = { ...errors.auth, password_confirmation: 'Please confirm your password' };

	if (values.auth.password && values.auth.password_confirmation) {
		if (values.auth.password !== values.auth.password_confirmation) errors.auth = { ...errors.auth, password_confirmation: 'Passwords do not match' };
	}

	return errors;
}

export const userSignupValidation = (values: UserSignupType) => {
	const errors: Errors<UserSignupType> = {};

	if (!values.credential) errors.credential = 'Please enter your email or phone number';
	// else {
	// 	// Check if the credential is an email or phone number
	// 	if (isEmail(values.credential)) {
	// 		// It's an email, no additional validation needed
	// 	} else if (isPhone(values.credential)) {
	// 		// It's a phone number, no additional validation needed
	// 	} else {
	// 		errors.credential = 'Please enter a valid email or 10-digit phone number';
	// 	}
	// }
	else if (values.credential && !isEmail(values.credential)) errors.credential = 'Please enter a valid email address';

	if (!values.password) errors.password = 'Please enter your password';

	if (values.password && values.password.length < 6) errors.password = 'Password must be at least 6 characters';

	if (!values.password_confirmation) errors.password_confirmation = 'Please confirm your password';

	if (values.password && values.password_confirmation) {
		if (values.password !== values.password_confirmation) errors.password_confirmation = 'Passwords do not match';
	}

	return errors;
}

export const userLoginValidation = (values: Pick<UserModel, 'email' | 'password'>) => {
	const errors: Errors<Pick<UserModel, 'email' | 'password'>> = {};

	if (!values.email) errors.email = 'Please enter your email';
	else if (!isEmail(values.email)) errors.email = 'Please enter a valid email address';

	if (!values.password) errors.password = 'Please enter your password';

	return errors;
}

export const addEventsValidation = (values: AddEventType) => {
	const errors: Errors<AddEventType> = {};

	// Step 1 Validation
	if (!values.step_1_info.cover_image) errors.step_1_info = { ...errors.step_1_info, cover_image: 'Cover image is required' };
	if (!values.step_1_info.title) errors.step_1_info = { ...errors.step_1_info, title: 'Event title is required' };

	// if (!values.step_1_info.duration) errors.step_1_info = { ...errors.step_1_info, duration: 'Event duration is required' };

	// if (values.step_1_info.duration) {
	// 	const [hour, minutes] = values.step_1_info.duration.split(':');
	// 	if (isNaN(parseInt(hour)) || isNaN(parseInt(minutes))) errors.step_1_info = { ...errors.step_1_info, duration: 'Please enter a valid duration' };
	// 	if (parseInt(hour) > 24) errors.step_1_info = { ...errors.step_1_info, duration: 'Hours cannot be more than 24 hours' };
	// 	if (parseInt(minutes) > 60) errors.step_1_info = { ...errors.step_1_info, duration: 'Minutes cannot be more than 60 minutes' };
	// }

	if (values.step_1_info.categories.length < 1) errors.step_1_info = { ...errors.step_1_info, categories: 'Please select at least one category' };

	if (!values.step_1_info.about) errors.step_1_info = { ...errors.step_1_info, about: 'Event description is required' };

	if (values.step_1_info.about && values.step_1_info.about.length < 50) errors.step_1_info = { ...errors.step_1_info, about: 'Event description must be at least 50 characters' };

	// Step 2 Validation
	// if (values.step_2_info.artists.length < 1) errors.step_2_info = { ...errors.step_2_info, artists: 'Please select at least one artist' };

	if (!values.step_2_info.terms) errors.step_2_info = { ...errors.step_2_info, terms: 'Terms and Conditions is required' };

	if (values.step_2_info.terms && values.step_2_info.terms.length < 50) errors.step_2_info = { ...errors.step_2_info, terms: 'Terms and Conditions must be at least 50 characters' };

	if (values.step_2_info.faqs && values.step_2_info.faqs.length > 0) {
		const faqErrors: Errors<FaqDataType>[] = [];

		values.step_2_info.faqs.forEach((faq, index) => {
			const faqError: Errors<FaqDataType> = {};

			if (!faq.question) faqError.question = 'Question is required';

			if (!faq.answer) faqError.answer = 'Answer is required';
			else if (faq.answer.length > 500) faqError.answer = 'Answer should not exceed 500 characters';

			if (Object.keys(faqError).length > 0) faqErrors[index] = faqError;
		});

		if (faqErrors.length > 0) {
			errors.step_2_info = {
				...errors.step_2_info,
				faqs: faqErrors
			};
		}
	}

	if (values.step_2_info.activities && values.step_2_info.activities.length > 0) {
		const activityErrors: Errors<ActivityDataType>[] = [];

		values.step_2_info.activities.forEach((activity, index) => {
			const activityError: Errors<ActivityDataType> = {};

			if (!activity.title) activityError.title = 'Title is required';

			if (!activity.details) activityError.details = 'Details is required';
			else if (activity.details.length > 500) activityError.details = 'Details should not exceed 500 characters';

			if (Object.keys(activityError).length > 0) activityErrors[index] = activityError;
		});

		if (activityErrors.length > 0) {
			errors.step_2_info = {
				...errors.step_2_info,
				activities: activityErrors
			};
		}
	}

	// Step 3 Validation
	if (values.step_3_info.venues.length < 1) errors.step_3_info = { ...errors.step_3_info, venues: 'Please select at least one venue' };

	if (values.step_3_info.use_external_ticket_link) {
		if (values.step_3_info.external_ticket_link) {
			if (!isValidUrl(values.step_3_info.external_ticket_link)) {
				errors.step_3_info = { ...errors.step_3_info, external_ticket_link: 'Please enter a valid URL' };
			}
		} else {
			errors.step_3_info = { ...errors.step_3_info, external_ticket_link: 'External ticket link is required' };
		}
	}

	if (values.step_3_info.venues.length > 0) {
		const venueDataErrors: Errors<AddEventType['step_3_info']['venue_data']> = {};

		Object.entries(values.step_3_info.venue_data).forEach(([venueId, venueData]) => {
			const venueError: Errors<VenueDataType> = {};

			// if (!venueData.date_time) venueError.date_time = 'Date and time is required';
			if (!venueData.start_date_time) venueError.start_date_time = 'Start date and time are required';
			if (!venueData.end_date_time) venueError.end_date_time = 'End date and time are required';

			if (!venueData.ticket_type) venueError.ticket_type = 'Please select a ticket type';

			if ((venueData.ticket_type === 'paid' || venueData.ticket_type === 'free') && venueData.tickets && !values.step_3_info.use_external_ticket_link) {
				const ticketErrors: Errors<NonNullable<VenueDataType['tickets']>> = [];

				venueData.tickets.forEach((ticket, index) => {
					const ticketError: Errors<NonNullable<VenueDataType['tickets']>[0]> = {};

					if (!ticket.name) ticketError.name = 'Ticket name is required';
					if (ticket.name.length > 20) ticketError.name = 'Ticket name must be less than 20 characters';

					if (venueData.ticket_type === 'paid') {
						if (ticket.count === '') {
							ticketError.count = 'Ticket Count Required';
						} else if (isNaN(parseInt(ticket.count))) {
							ticketError.count = 'Must be a number';
						}

						if (ticket.amount === '') {
							ticketError.amount = 'Ticket Price Required';
						} else if (isNaN(parseFloat(ticket.amount))) {
							ticketError.amount = 'Enter a valid price';
						}
					}

					if (Object.keys(ticketError).length > 0) {
						ticketErrors[index] = ticketError;
					}
				});

				if (ticketErrors.length > 0) {
					venueError.tickets = ticketErrors;
				}
			}

			if (Object.keys(venueError).length > 0) {
				venueDataErrors[venueId] = venueError;
			}
		});

		if (Object.keys(venueDataErrors).length > 0) {
			errors.step_3_info = { ...errors.step_3_info, venue_data: venueDataErrors };
		}
	}

	return errors;
}

export const addCategoryValidation = (values: AddCategoryType) => {
	const errors: Errors<AddCategoryType> = {};

	if (!values.name) errors.name = 'Please enter a category name';

	if (!values.app_type) errors.app_type = 'Please select an app type';

	if (values.info) {
		if (values.info.length < 30) errors.info = 'Info must be at least 30 characters';
		else if (values.info.length > 500) errors.info = 'Info should not exceed 500 characters';
	}

	if (values.meta_keywords.length > 0 && values.meta_keywords.length < 3) errors.meta_keywords = 'Meta keywords must be at least 3 keywords';

	if (values.meta_description) {
		if (values.meta_description.length < 50) errors.meta_description = 'Meta description must be at least 50 characters';
		else if (values.meta_description.length > 500) errors.meta_description = 'Meta description should not exceed 500 characters';
	}

	return errors;
}

export const addArtistValidation = (values: AddArtistType) => {
	const errors: Errors<AddArtistType> = {};

	if (!values.name) errors.name = 'Please enter the name of the artist';

	if (!values.title) errors.title = 'Please enter a title for the artist';

	if (!values.about) errors.about = 'Please provide info about the artist';

	if (values.about && values.about.length < 30) errors.about = 'About must be at least 30 characters';

	if (values.meta_keywords && values.meta_keywords.length > 0 && values.meta_keywords.length < 3) errors.meta_keywords = 'Meta keywords must be at least 3 keywords';

	if (values.meta_desc) {
		if (values.meta_desc.length < 50) errors.meta_desc = 'Meta description must be at least 50 characters';
		else if (values.meta_desc.length > 500) errors.meta_desc = 'Meta description should not exceed 500 characters';
	}

	return errors;
}

export const addVenueValidation = (values: AddVenueType) => {
	const errors: Omit<Errors<AddVenueType>, 'address'> & {
    address?: string
	} = {};
	if (!values.name) errors.name = 'Please enter the name of the venue';

	if (values.about) {
		if (values.about.length < 30) errors.about = 'About must be at least 30 characters';
		else if (values.about.length > 500) errors.about = 'About should not exceed 500 characters';
	}

	if (!values.image) {
		errors.image = 'Please upload an image for the venue';
	}

	if (!values.logo) {
		errors.logo = 'Please upload a logo for the venue';
	}

	if (values.phone_number && !isPhone(values.phone_number, 'landline')) errors.phone_number = 'Please enter a valid phone number';

	if (values.mobile_number && !isPhone(values.mobile_number, 'mobile')) errors.mobile_number = 'Please enter a valid mobile number';

	if (values.email && !isEmail(values.email)) errors.email = 'Please enter a valid email address';

	if (values.website && !isValidUrl(values.website)) errors.website = 'Please enter a valid website URL';

	if (values.attractions) {
		if (values.attractions.length < 30) errors.attractions = 'Attractions must be at least 30 characters';
		else if (values.attractions.length > 500) errors.attractions = 'Attractions should not exceed 500 characters';
	}

	if (!values.address) errors.address = 'Please enter your venue address';

	if (values.meta_keywords && values.meta_keywords.length > 0 && values.meta_keywords.length < 3) errors.meta_keywords = 'Meta keywords must be at least 3 keywords';

	if (values.meta_desc) {
		if (values.meta_desc.length < 50) errors.meta_desc = 'Meta description must be at least 50 characters';
		else if (values.meta_desc.length > 500) errors.meta_desc = 'Meta description should not exceed 500 characters';
	}

	// Social media validation
	const urlPatterns = {
		facebook: /^(https?:\/\/)?(www\.)?facebook\.com\/.+$/,
		youtube: /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/,
		twitter: /^(https?:\/\/)?(www\.)?x\.com\/.+$/,
		instagram: /^(https?:\/\/)?(www\.)?instagram\.com\/.+$/,
	};

	if (values.facebook && !urlPatterns.facebook.test(values.facebook)) {
		errors.facebook = 'Please provide a valid Facebook link';
	}

	if (values.youtube && !urlPatterns.youtube.test(values.youtube)) {
		errors.youtube = 'Please provide a valid YouTube link';
	}

	if (values.twitter && !urlPatterns.twitter.test(values.twitter)) {
		errors.twitter = 'Please provide a valid Twitter link';
	}

	if (values.instagram && !urlPatterns.instagram.test(values.instagram)) {
		errors.instagram = 'Please provide a valid Instagram link';
	}

	return errors;
}

export const updateGeneralInfoValidation = (values: UpdateGeneralInfoType) => {
	const errors: Errors<UpdateGeneralInfoType> = {};

	if (values.email_one && !isEmail(values.email_one)) errors.email_one = 'Please enter a valid email address';

	if (values.email_two && !isEmail(values.email_two)) errors.email_two = 'Please enter a valid email address';

	if (values.meta_keywords && values.meta_keywords.length > 0 && values.meta_keywords.length < 3) errors.meta_keywords = 'Meta keywords must be at least 3 keywords';

	if (values.meta_desc) {
		if (values.meta_desc.length < 50) errors.meta_desc = 'Meta description must be at least 50 characters';
		else if (values.meta_desc.length > 500) errors.meta_desc = 'Meta description should not exceed 500 characters';
	}

	const urlPatterns = {
		facebook: /^(https?:\/\/)?(www\.)?facebook\.com\/.+$/i,
		youtube: /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/i,
		twitter: /^(https?:\/\/)?(www\.)?x\.com\/.+$/i,
		instagram: /^(https?:\/\/)?(www\.)?instagram\.com\/.+$/i,
		linkedin: /^(https?:\/\/)?(www\.)?linkedin\.com\/.+$/i,
		google: /^(https?:\/\/)?(www\.)?google\.com\/.+$/i,
		pinterest: /^(https?:\/\/)?(www\.)?pinterest\.com\/.+$/i,
		site: /^https:\/\/[^\s/$.?#].[^\s]*$/i,
	};

	if (values.site_url && !urlPatterns.site.test(values.site_url)) {
		errors.site_url = 'Please provide a valid HTTPS website link';
	}

	if (values.facebook && !urlPatterns.facebook.test(values.facebook)) {
		errors.facebook = 'Please provide a valid Facebook link';
	}

	if (values.yt_channel && !urlPatterns.youtube.test(values.yt_channel)) {
		errors.yt_channel = 'Please provide a valid YouTube link';
	}

	if (values.twitter && !urlPatterns.twitter.test(values.twitter)) {
		errors.twitter = 'Please provide a valid Twitter link';
	}

	if (values.instagram && !urlPatterns.instagram.test(values.instagram)) {
		errors.instagram = 'Please provide a valid Instagram link';
	}

	if (values.linkedin && !urlPatterns.linkedin.test(values.linkedin)) {
		errors.linkedin = 'Please provide a valid LinkedIn link';
	}

	if (values.google && !urlPatterns.google.test(values.google)) {
		errors.google = 'Please provide a valid Google link';
	}

	if (values.pinterest && !urlPatterns.pinterest.test(values.pinterest)) {
		errors.pinterest = 'Please provide a valid Pinterest link';
	}

	return errors;
}
