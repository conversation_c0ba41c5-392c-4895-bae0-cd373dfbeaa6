import { Request, Response, NextFunction } from 'express';
import { type ValidationError } from 'express-validator';
import { paginatedData } from './pagination.util';

export const response = (_: Request, res: Response, next: NextFunction) => {
	/**
   * @param message
	 * @param status
	 * @param data
	 * @returns
   */
	res.success = (message: string = '', data = undefined, status: number = 200) => res.status(status).send({ message, data });

	/**
	 * @param message
	 * @param data
	 * @param status
	 * @returns
	 */
	res.created = (message: string = 'created successfully', data = undefined, status: number = 201) => res.status(status).send({ message, data });

	/**
	 *
	 * @param message
	 * @param data
	 * @param status
	 * @returns
	 */
	res.deleted = (message: string = 'deleted successfully ', data = undefined, status: number = 204) => res.status(status).send({ message, data });

	/**
	 * @param status
	 * @param data
	 * @returns
	 */
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	res.paginated = (data: any, req: Request, status: number = 200) => res.status(status).send(paginatedData(data, req));

	/**
	 * @param status
	 * @param data
	 * @returns
	 */
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	res.collection = (data: any, status: number = 200) => res.status(status).send(data);

	/**
	 * @param message
	 * @param data
	 * @param status
	 * @returns
	 */
	res.updated = (message: string = '', data = undefined, status: number = 200) => res.status(status).send({ message, data });

	/**
	 * @param status
	 * @param data
	 * @returns
	 */
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	res.single = (data: any, status: number = 200) => res.status(status).send(data);

	/**
     *
     * @param message
     * @param status
     * @returns
     */
	res.failure = (message: string = 'failed', status: number = 400) => res.status(status).send({ message });

	/**
     *
     * @param message
     * @param status
     * @returns
     */
	res.forbidden = (message: string = 'Forbidden', status: number = 403) => res.status(status).send({ message });

	/**
     *
     * @param message
     * @param status
     * @returns
     */
	res.unauthorized = (message: string = 'Not authorized', status: number = 401) => res.status(status).send({ message });

	/**
     *
     * @param errors
     * @param message
     * @param status
     * @returns
     */
	res.invalid = (message: string = 'Invalid', errors: ValidationError[], status: number = 403) => res.status(status).send({ message, errors });

	/**
     *
     * @param errors
     * @param message
     * @param status
     * @returns
     */
	res.notFound = (message: string = 'Not found', status: number = 404) => res.status(status).send({ message });

	/**
	 * @param status
	 * @param data
	 * @returns
	 */
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	res.file = (data: any, fileName: string, fileType: 'xlsx' | 'pdf', status: number = 200) => {
		const filename = `${fileName}.${fileType}`;

		res.setHeader('Access-Control-Expose-Headers', 'X-Filename');
		res.setHeader('X-Filename', filename);
		return res.status(status).send(data);
	};

	if (next !== null) {
		next();
	}
};
