/* eslint-disable @typescript-eslint/no-explicit-any */
import { AppDispatch } from "@/store";

type ActionWrapperArgs = {
  tryBlock: () => Promise<any>; // Function for the main logic in try block
  stateChangeAction: (payload: { key: string; value: any }) => { payload: any; type: string }; // Reducer action for updating state
};

export const actionWrapper = async (
	{ tryBlock, stateChangeAction }: ActionWrapperArgs,
	dispatch: AppDispatch
) => {
	try {
		return await tryBlock();
	} catch (error: any) {
		// eslint-disable-next-line no-console
		console.error(error);
		dispatch(
			stateChangeAction({
				key: "errors",
				value: {
					status: !error.response ? null : error.response?.status || 500,
					message: error.response?.data?.message || error.message || error.data?.message || "An unknown error occurred",
				},
			})
		);
		// if (error?.response) return error.response;
		// return Promise.reject(error.response?.data?.message || error.message || error.data?.message || "An unknown error occurred");
	} finally {
		dispatch(stateChangeAction({ key: "status", value: "idle" }));
	}
};
