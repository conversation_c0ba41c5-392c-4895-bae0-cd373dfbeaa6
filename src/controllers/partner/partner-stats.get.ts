import authMiddleware from '@/middlewares/auth.middleware';
import { Event, PurchaseInfo } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Get Partner Stats
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/partner/stats', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user, company } = req;
	if (!user || !company) return res.forbidden('Invalid request. User and company are required');

	const totalEvents = await Event.count({
		where: {
			company_id: company.id,
		}
	});

	const totalTicketsSold = await PurchaseInfo.count({
		include: [{
			model: Event,
			where: {
				company_id: company.id,
			},
		}],
	});

	return res.collection({ totalEvents, totalTicketsSold });
}));

export default router;
