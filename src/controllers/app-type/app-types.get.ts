import { App } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Get App Types
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/app-types', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const appTypes = await App.findAndCountAll();

	return res.paginated(appTypes, req);
}));

export default router;
