import authMiddleware from '@/middlewares/auth.middleware';
import { UserLog } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Get User Logs
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/user/logs', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) return res.forbidden('Invalid request. User is required');

	const logs = await UserLog.findAll({
		where: {
			user_id: user.id,
		},
		limit: 4,
		order: [['created_at', 'DESC']],
	});

	return res.collection(logs);
}));

export default router;
