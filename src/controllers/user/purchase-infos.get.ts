import authMiddleware from '@/middlewares/auth.middleware';
import { Event, PurchaseInfo, Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';
import { Op, WhereOptions } from 'sequelize';

const router = Router();

/**
 * Get Purchase info with event and venue details
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/user/purchase-infos', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) return res.forbidden('Unauthorized Request. User is required');

	const { type } = req.query;

	const eventWhereClause: WhereOptions = {
		published_at: { [Op.ne]: null },
		approved_at: { [Op.ne]: null },
		deleted_at: null,
		status: true,
	};

	const now = dayjs();

	// Filter using status - upcoming, ongoing, past
	switch (type) {
	case 'upcoming':
		eventWhereClause.start_date_time = { [Op.gt]: now.toDate() };
		break;
	case 'ongoing':
		eventWhereClause.start_date_time = { [Op.lte]: now.toDate() };
		eventWhereClause.end_date_time = { [Op.gt]: now.toDate() };
		break;
	case 'past':
		eventWhereClause.end_date_time = { [Op.lte]: now.toDate() };
		break;
	default:
		break;
	}

	const purchaseInfos = await PurchaseInfo.findAndCountAll({
		where: {
			user_id: user.id,
		},
		attributes: ['id', 'ticket_id'],
		distinct: true,
		include: [
			{
				model: Event,
				where: {
					...eventWhereClause,
				},
				required: true,
				attributes: ['id', 'title', 'image', 'slug', 'about', 'start_date_time', 'end_date_time'],
			},
			{
				model: Venue,
				required: true,
				attributes: ['id', 'name', 'slug'],
			}
		],
		...(paginated(req))
	});

	return res.paginated(purchaseInfos, req);
}));

export default router;
