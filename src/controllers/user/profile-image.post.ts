import { errorWrapper } from '@/utils/error-wrapper.util';
import { Router, type Request, type Response } from 'express';
import authMiddleware from '@/middlewares/auth.middleware';
import { User } from '@/models';
import { userLog } from '@/utils/logger.util';
import { getClientIP } from '@/utils/general.util';

const router = Router();

/**
 * Update user profile image
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 */
router.post('/profile-image', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) {
		userLog({
			user_id: req.user?.id,
			event_type: 'USER',
			type: false,
			details: 'Invalid request',
			action: 'USER-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/profile-image'
		});
		return res.forbidden('Invalid request');
	}

	const { image } = req.body;
	if (!image) {
		userLog({
			user_id: req.user?.id,
			event_type: 'USER',
			type: false,
			details: 'Invalid request. Image is required',
			action: 'USER-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/profile-image'
		});
		return res.forbidden('Invalid request. Image is required');
	}

	await User.update(
		{ image },
		{ where: { id: user.id }}
	);
	userLog({
		user_id: req.user?.id,
		event_type: 'USER',
		type: true,
		details: 'Profile image updated successfully',
		action: 'USER-UPDATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/profile-image'
	});
	return res.success('Profile image updated successfully');
}));

export default router;
