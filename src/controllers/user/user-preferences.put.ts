import { errorWrapper } from '@/utils/error-wrapper.util';
import { Router, type Request, type Response } from 'express';
import authMiddleware from '@/middlewares/auth.middleware';
import { UserCategoryPreference, UserPreference } from '@/models';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';

const router = Router();

/**
 * Update user preferences
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 */
router.put('/user/preferences', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) {
		userLog({
			user_id: req.user?.id,
			event_type: 'USER',
			type: false,
			details: 'Invalid request',
			action: 'USER-PREFERENCES-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/user/preferences'
		});
		return res.forbidden('Invalid request. User is required.');
	}

	const { notification_email, notification_whatsapp, selectedCategories } = req.body;

	const userPreference = await UserPreference.findOne({
		where: {
			user_id: user.id,
		}
	});

	if (userPreference) {
		await userPreference.update({
			notification_email: notification_email || false,
			notification_whatsapp: notification_whatsapp || false,
		});
	} else {
		await UserPreference.create({
			user_id: user.id,
			notification_email: notification_email || false,
			notification_whatsapp: notification_whatsapp || false,
		});
	}

	const userCatPreferences = await UserCategoryPreference.findAll({
		where: {
			user_id: user.id,
		}
	});

	if (userCatPreferences.length > 0) {
		const existingCategoryIds = userCatPreferences.map(p => p.category_id);
		const newCategoryIds: number[] = selectedCategories;

		// Categories to add (in new list but not in DB)
		const toAdd = newCategoryIds.filter(id => !existingCategoryIds.includes(id));

		// Categories to remove (in DB but not in new list)
		const toRemove = existingCategoryIds.filter(id => !newCategoryIds.includes(id));

		// Add new preferences
		if (toAdd.length > 0) {
			await UserCategoryPreference.bulkCreate(
				toAdd.map(category_id => ({
					user_id: user.id,
					category_id,
				}))
			);
		}

		// Remove unselected preferences
		if (toRemove.length > 0) {
			await UserCategoryPreference.destroy({
				where: {
					user_id: user.id,
					category_id: toRemove,
				}
			});
		}
	}
	else {
		await UserCategoryPreference.bulkCreate(selectedCategories.map((category: number) => ({
			user_id: user.id,
			category_id: category,
		})));
	}

	userLog({
		user_id: req.user?.id,
		event_type: 'USER',
		type: true,
		details: 'Preferences updated successfully',
		action: 'USER-PREFERENCES-UPDATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PUT',
		api_endpoint: '/user/preferences'
	});

	return res.success('Preferences updated successfully');
}));

export default router;
