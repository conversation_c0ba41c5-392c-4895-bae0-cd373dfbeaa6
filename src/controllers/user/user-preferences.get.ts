import authMiddleware from '@/middlewares/auth.middleware';
import { User, UserCategoryPreference, UserPreference } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Get User Preferences
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/user/preferences', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) return res.forbidden('Invalid request. User is required');

	const userWithPrefs = await User.findByPk(user.id, {
		attributes: [],
		include: [
			{
				model: UserPreference,
				attributes: ['notification_email', 'notification_whatsapp'],
			},
			{
				model: UserCategoryPreference,
				attributes: ['category_id'],
			}
		]
	});

	return res.single(userWithPrefs);
}));

export default router;
