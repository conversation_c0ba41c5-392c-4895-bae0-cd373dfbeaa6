import { errorWrapper } from '@/utils/error-wrapper.util';
import { Router, type Request, type Response } from 'express';
import authMiddleware from '@/middlewares/auth.middleware';
import { Company, User } from '@/models';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';

const router = Router();

/**
 * Update user personal info
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 */
router.put('/user', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) {
		userLog({
			user_id: req.user?.id,
			event_type: 'USER',
			type: false,
			details: 'Invalid request',
			action: 'USER-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/user'
		});
		return res.forbidden('Invalid request');
	}

	const { f_name, l_name, phone, image, dob, gender } = req.body;

	await User.update({
		f_name: f_name || null,
		l_name: l_name || null,
		phone: phone || null,
		image: image || null,
		dob: dob || null,
		gender: gender || null,
	}, {
		where: { id: user.id },
	});

	const updatedUser = await User.findOne({
		where: {
			id: user.id,
		},
		include: [
			{
				model: Company,
				where: { status: true },
				attributes: ['id', 'name', 'email', 'phone', 'about', 'address', 'logo', 'domain'],
				required: false,
			}
		]
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'USER',
		type: true,
		details: 'Personal Info updated successfully',
		action: 'USER-UPDATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PUT',
		api_endpoint: '/user'
	});

	return res.success('Personal Info updated successfully', updatedUser!.toJSON());
}));

export default router;
