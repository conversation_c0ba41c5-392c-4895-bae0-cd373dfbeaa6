import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { initializeKhaltiPayment } from '@/utils/khalti.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Initiate Khalti Payment
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/khalti-payment', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { amount, purchase_order_name, purchase_order_id, return_url } = req.body;
	const paymentInitiate = await initializeKhaltiPayment({
		amount: Number(amount) * 100, //converting to paisa
		purchase_order_id: purchase_order_id,
		purchase_order_name: purchase_order_name,
		return_url: return_url,
		website_url: process.env.CLIENT_ENDPOINT,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'PAYMENT',
		type: true,
		details: 'khalti payment',
		action: 'PAYMENT-KHALTI-INITIATE',
		payloads: { ...req.body, paymentInitiate: paymentInitiate },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/khalti-payment'
	});
		
	return res.created('khalti payment', paymentInitiate);
})
);

export default router;
