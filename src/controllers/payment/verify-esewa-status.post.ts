import authMiddleware from '@/middlewares/auth.middleware';
import { Event, EventTicket, PurchaseInfo, TicketBuyer, Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { sendEmail, ticketEmail } from '@/utils/postmark.util';
import { generateTicketPdf } from '@/utils/ticket-pdf.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Verify Esewa Payment
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/verify-esewa-status', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { total_amount, transaction_uuid, event_id, ticket, ticketCount, venue, email, phone, full_name } = req.body;

	const url = `${process.env.ESEWA_GATEWAY_URL}?product_code=${process.env.ESEWA_SECRET_KEY}&total_amount=${total_amount}&transaction_uuid=${transaction_uuid}`;
	const response = await fetch(url);

	const fullName = full_name || `${req.user?.f_name ?? ''} ${req.user?.l_name ?? ''}`.trim() || 'N/A';
	const userEmail = email || req.user?.email;
	const userPhone = phone || req.user?.phone || 'N/A';

	const event = await Event.findOne({
		where: {
			id: Number(event_id),
		},
		include: [
			{
				model: Venue,
				where: {
					id: venue
				}
			},
			{
				model: EventTicket,
				where: {
					id: ticket
				}
			}
		]
	});
	if (!event) {
		userLog({
			user_id: req.user?.id,
			event_type: 'PAYMENT',
			type: false,
			details: 'Event not found',
			action: 'PAYMENT-BY-ESEWA',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/verify-esewa-status'
		});
		return res.notFound('Event not found');
	};

	const formattedDateRange = event.start_date_time && event.end_date_time
		? `${dayjs(event.start_date_time).format('ddd, Do MMM YYYY hh:mm A')} - ${dayjs(event.end_date_time).format('ddd, Do MMM YYYY hh:mm A')}`
		: 'N/A';

	const data = await response.json();

	if (data && data.status === 'COMPLETE') {
		const buyer = await TicketBuyer.create({
			user_id: req.user?.id,
			full_name: fullName,
			email: userEmail,
			phone_number: userPhone,
			total_amount: total_amount,
			payment_method: 'esewa',
			payment_method_response: data,
			was_payment_successful: true,
			ticket_count: ticketCount,
			event_id: event.id,
		});

		const attachments: any[] = [];

		for (let i = 0; i < Number(ticketCount); i++) {
			const purchaseInfo = await PurchaseInfo.create({
				event_id: event.id,
				venue_id: venue,
				ticket_id: ticket,
				user_id: req.user?.id,
				ticket_buyer_id: buyer.id,
				paid_amount: event.tickets?.[0]?.price || null,
			});
			
			const ticketData = {
				ticketNo: purchaseInfo.id.toString(),
				eventId: event.id.toString(),
				eventName: event.title || 'N/A',
				eventDate: formattedDateRange,
				eventLocation: event.venues?.[0]?.name || 'N/A',
				userName: fullName,
				ticketType: event.tickets?.[0]?.name || 'N/A',
				price: event.tickets?.[0]?.price ? `NRs. ${event.tickets[0].price}` : 'N/A',
				issueDate: dayjs(purchaseInfo.createdAt).format('ddd, Do MMM YYYY hh:mm A'),
			};
			
			try {
				const pdfBuffer = await generateTicketPdf(ticketData);
				attachments.push({
					Content: pdfBuffer.toString('base64'),
					Name: `${event.title || 'Event'}_Ticket-${ticketData.ticketNo}.pdf`,
					ContentType: 'application/pdf',
					ContentID: `${event.title || 'Event'}_Ticket-${ticketData.ticketNo}`,
				});
			} catch (error) {
				console.error(`Error generating ticket pdf for purchase info - ${purchaseInfo.id}`, error);
			}
		}

		if (userEmail) {
			try {
				const emailData = {
					eventName: event.title || 'N/A',
					eventDate: formattedDateRange,
					eventLocation: event.venues?.[0]?.name || 'N/A',
					userName: fullName,
					ticketCount: buyer.ticket_count || 'N/A',
					totalAmount: buyer.total_amount ? `NRs. ${buyer.total_amount}` : 'N/A',
				};

				await sendEmail(
					ticketEmail(
						userEmail,
						emailData,
						attachments,
					)
				);
			} catch (error) {
				console.error(`Error sending ticket email for buyer - ${buyer.id}`, error);
			}
		}

		userLog({
			user_id: req.user?.id,
			event_type: 'PAYMENT',
			type: true,
			details: 'Ticket purchased successfully by Esewa payment',
			action: 'PAYMENT-BY-ESEWA',
			payloads: { ...req.body, merchant_payload: data },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/verify-esewa-status'
		});

		return res.created(
			'Ticket purchased successfully',
			{
				buyerId: buyer.id,
				ticketSentTo: userEmail,
			}
		);
	} else {
		userLog({
			user_id: req.user?.id,
			event_type: 'PAYMENT',
			type: false,
			details: 'Payment was not complete',
			action: 'PAYMENT-BY-ESEWA',
			payloads: { ...req.body, merchant_payload: data },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/verify-esewa-status'
		});
		return res.forbidden('Payment was not complete. Please verify and try again.');
	}
}));

export default router;
