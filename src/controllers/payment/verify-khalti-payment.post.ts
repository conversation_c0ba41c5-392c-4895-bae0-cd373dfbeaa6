import authMiddleware from '@/middlewares/auth.middleware';
import { EventTicket, TicketBuyer, Venue, Event } from '@/models';
import PurchaseInfo from '@/models/purchase-info.model';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { verifyKhaltiPayment } from '@/utils/khalti.util';
import { userLog } from '@/utils/logger.util';
import { sendEmail, ticketEmail } from '@/utils/postmark.util';
import { generateTicketPdf } from '@/utils/ticket-pdf.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Verify Khalti Payment
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/verify-khalti-payment', authMiddleware,
	errorWrapper(async (req: Request, res: Response): Promise<Response> => {
		const {
			pidx, total_amount, venue, ticketCount, ticket, event_id, email, phone, full_name
		} = req.body;

		const verifyPayment = await verifyKhaltiPayment(pidx);

		const fullName = full_name || `${req.user?.f_name ?? ''} ${req.user?.l_name ?? ''}`.trim() || '';
		const userEmail = email || req.user?.email;
		const userPhone = phone || req.user?.phone || '';

		const event = await Event.findOne({
			where: {
				id: event_id,
			},
			include: [
				{
					model: Venue,
					where: {
						id: venue
					}
				},
				{
					model: EventTicket,
					where: {
						id: ticket
					}
				}
			]
		});

		if (!event) {
			userLog({
				user_id: req.user?.id,
				event_type: 'PAYMENT',
				type: false,
				details: 'Event not found',
				action: 'PAYMENT-BY-KHALTI',
				payloads: { ...req.body },
				ip_address: getClientIP(req),
				user_agent: 'web',
				read_access: true,
				referrer: null,
				method: 'POST',
				api_endpoint: '/verify-khalti-payment'
			});
			return res.notFound('Event not found');
		}

		const formattedDateRange = event.start_date_time && event.end_date_time
			? `${dayjs(event.start_date_time).format('ddd, Do MMM YYYY hh:mm A')} - ${dayjs(event.end_date_time).format('ddd, Do MMM YYYY hh:mm A')}`
			: 'N/A';

		if (verifyPayment && verifyPayment.status === 'Completed') {
			const buyer = await TicketBuyer.create({
				user_id: req.user?.id,
				full_name: fullName,
				email: userEmail,
				phone_number: userPhone,
				total_amount: total_amount / 100, // converting paisa to rs
				payment_method: 'khalti',
				payment_method_response: verifyPayment,
				was_payment_successful: true,
				ticket_count: ticketCount,
				event_id: event.id,
			});

			const attachments: any[] = [];

			for (let i = 0; i < Number(ticketCount); i++) {
				const purchaseInfo = await PurchaseInfo.create({
					event_id: event.id,
					venue_id: venue,
					ticket_id: ticket,
					user_id: req.user?.id,
					ticket_buyer_id: buyer.id,
					paid_amount: event.tickets?.[0]?.price || null,
				});
			
				const ticketData = {
					ticketNo: purchaseInfo.id.toString(),
					eventId: event.id.toString(),
					eventName: event.title || 'N/A',
					eventDate: formattedDateRange,
					eventLocation: event.venues?.[0]?.name || 'N/A',
					userName: fullName,
					ticketType: event.tickets?.[0]?.name || 'N/A',
					price: event.tickets?.[0]?.price ? `NRs. ${event.tickets[0].price}` : 'N/A',
					issueDate: dayjs(purchaseInfo.createdAt).format('ddd, Do MMM YYYY hh:mm A'),
				};
			
				try {
					const pdfBuffer = await generateTicketPdf(ticketData);
					attachments.push({
						Content: pdfBuffer.toString('base64'),
						Name: `${event.title || 'Event'}_Ticket-${ticketData.ticketNo}.pdf`,
						ContentType: 'application/pdf',
						ContentID: `${event.title || 'Event'}_Ticket-${ticketData.ticketNo}`,
					});
				} catch (error) {
					console.error(`Error generating ticket pdf for purchase info - ${purchaseInfo.id}`, error);
				}
			}

			if (userEmail) {
				try {
					const emailData = {
						eventName: event.title || 'N/A',
						eventDate: formattedDateRange,
						eventLocation: event.venues?.[0]?.name || 'N/A',
						userName: fullName,
						ticketCount: buyer.ticket_count || 'N/A',
						totalAmount: buyer.total_amount ? `NRs. ${buyer.total_amount}` : 'N/A',
					};

					await sendEmail(
						ticketEmail(
							userEmail,
							emailData,
							attachments,
						)
					);
				} catch (error) {
					console.error(`Error sending ticket email for buyer - ${buyer.id}`, error);
				}
			}

			userLog({
				user_id: req.user?.id,
				event_type: 'PAYMENT',
				type: true,
				details: 'Ticket purchased successfully by Khalti payment',
				action: 'PAYMENT-BY-KHALTI',
				payloads: { ...req.body, merchant_payload: verifyPayment },
				ip_address: getClientIP(req),
				user_agent: 'web',
				read_access: true,
				referrer: null,
				method: 'POST',
				api_endpoint: '/verify-khalti-payment'
			});
			
			return res.created(
				'Ticket purchased successfully',
				{
					buyerId: buyer.id,
					ticketSentTo: userEmail,
				}
			);
		} else {
			userLog({
				user_id: req.user?.id,
				event_type: 'PAYMENT',
				type: false,
				details: 'Payment was not complete',
				action: 'PAYMENT-BY-KHALTI',
				payloads: { ...req.body, merchant_payload: verifyPayment },
				ip_address: getClientIP(req),
				user_agent: 'web',
				read_access: true,
				referrer: null,
				method: 'POST',
				api_endpoint: '/verify-khalti-payment'
			});

			return res.forbidden('Payment was not complete. Please verify and try again.');
		}
	})
);

export default router;
