import { GeneralInfo } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

router.get('/general-info', errorWrapper(async (_: Request, res: Response): Promise<Response> => {
	const generalInfo = await GeneralInfo.findOne();
	if (!generalInfo) {
		return res.single({});
	}

	return res.single(generalInfo);
}));

export default router;
