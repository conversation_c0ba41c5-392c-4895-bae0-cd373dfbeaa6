import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { GeneralInfo } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Upsert General Info
 */
router.put('/general-info', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const {
		name,
		app_info,
		address_one,
		address_two,
		phone_one,
		phone_two,
		mobile_one,
		mobile_two,
		email_one,
		email_two,
		site_url,
		copyright,
		yt_channel,
		facebook,
		twitter,
		linkedin,
		instagram,
		google,
		pinterest,
		meta_desc,
		meta_keywords,
	} = req.body;

	const payload = {
		name: name || null,
		app_info: app_info || null,
		address_one: address_one || null,
		address_two: address_two || null,
		phone_one: phone_one || null,
		phone_two: phone_two || null,
		mobile_one: mobile_one || null,
		mobile_two: mobile_two || null,
		email_one: email_one || null,
		email_two: email_two || null,
		site_url: site_url || null,
		copyright: copyright || null,
		yt_channel: yt_channel || null,
		facebook: facebook || null,
		twitter: twitter || null,
		linkedin: linkedin || null,
		instagram: instagram || null,
		google: google || null,
		pinterest: pinterest || null,
		meta_desc: meta_desc || null,
		meta_keywords: Array.isArray(meta_keywords) ? meta_keywords.join(',') : null,
	};

	const generalInfo = await GeneralInfo.findOne();

	if (generalInfo) {
		await generalInfo.update(payload);
	} else {
		await GeneralInfo.create(payload);
	}

	userLog({
		user_id: req.user?.id,
		event_type: 'GENERAL-INFO',
		type: true,
		details: 'General info saved successfully',
		action: 'GENERAL-INFO-SAVE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PUT',
		api_endpoint: '/general-info',
	});

	return res.success('General info saved successfully');
}));

export default router;
