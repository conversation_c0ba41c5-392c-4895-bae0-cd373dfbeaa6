import sequelize from '@/configs/database.config';
import authMiddleware from '@/middlewares/auth.middleware';
import { Artist, Category, Event, EventActivity, EventDate, EventFaq, EventGallery, EventSlider, EventTerm, EventTicket, PurchaseInfo, Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';
import { IncludeOptions, Op, Sequelize, WhereOptions } from 'sequelize';

const router = Router();

/**
 * Get All Events - for admin and partner
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/protected/events', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user || (user.user_type === 'normal' && !user.is_partner)) return res.forbidden('Unauthorized Request');

	const { company } = req;
	if (!company) return res.forbidden('Partner company is missing');

	const { type } = req.query;

	const whereClause: WhereOptions = {};

	if (user.is_partner) {
		whereClause.company_id = company.id;
	}

	const isMobile = req.headers['x-platform'] === 'mobile';

	const now = dayjs();

	switch (type) {
	case 'published':
		whereClause.published_at = { [Op.ne]: null };
		whereClause.approved_at = { [Op.ne]: null };
		whereClause.end_date_time = { [Op.gte]: now.toDate() };
		whereClause.deleted_at = null;
		whereClause.rejected_at = null;
		whereClause.cancelled_at = null;
		whereClause.status = true;
		if (req.headers['x-platform'] === 'mobile' && user.user_type === 'admin') {
			whereClause.company_id = company.id;
		}
		break;
	case 'on-review':
		whereClause.published_at = null;
		whereClause.approved_at = null;
		whereClause.end_date_time = { [Op.gte]: now.toDate() };
		whereClause.deleted_at = null;
		whereClause.status = true;
		if (user.user_type === 'admin') {
			whereClause.rejected_at = null;
			whereClause.cancelled_at = null;
		}
		break;
	case 'draft':
		if (user.user_type === 'admin') {
			whereClause.company_id = company.id;
		}
		whereClause.published_at = null;
		whereClause.approved_at = null;
		whereClause.deleted_at = null;
		whereClause.status = false;
		break;
	case 'expired': {
		whereClause.published_at = { [Op.ne]: null };
		whereClause.approved_at = { [Op.ne]: null };
		whereClause.end_date_time = { [Op.lte]: now.toDate() };
		whereClause.deleted_at = null;
		break;
	}
	case 'deleted':
		whereClause.deleted_at = { [Op.ne]: null };
		break;
	default:
		break;
	}

	const includeOptions: IncludeOptions[] = [];

	if (isMobile) {
		includeOptions.push(
			{ model: Venue },
			{
				model: EventTicket,
				where: {
					ticket_type: { [Op.ne]: 'free' }
				},
				required: true
			}
		);
	} else {
		includeOptions.push(
			{ model: Category },
			{ model: Venue },
			{ model: EventSlider },
			{ model: Artist },
			{ model: EventDate },
			{ model: EventTerm },
			{ model: EventTicket },
			{ model: EventFaq },
			{ model: EventGallery },
			{ model: EventActivity }
		);
	}

	const events = await Event.findAndCountAll({
		where: {
			...whereClause,
		},
		distinct: true,
		order: [
			[
				sequelize.literal(`
					CASE 
						WHEN "Event"."published_at" IS NOT NULL THEN 0 
						ELSE 1 
					END
				`),
				'ASC'
			],
			[
				sequelize.literal(`
					CASE 
						WHEN "Event"."status" = true THEN 0 
						ELSE 1 
					END
				`),
				'ASC'
			],
			['created_at', 'DESC']
		],
		include: includeOptions,
		...(paginated(req))
	});

	if (isMobile) {
		const eventIds = events.rows.map(event => event.id);

		// Get all purchased ticket counts grouped by event_id
		const purchaseCountsRaw = await PurchaseInfo.findAll({
			attributes: ['event_id', [Sequelize.fn('COUNT', Sequelize.col('id')), 'purchased']],
			where: {
				event_id: eventIds,
			},
			group: ['event_id'],
			raw: true,
		});

		const purchaseMap = new Map(purchaseCountsRaw.map(p => [p.event_id, parseInt((p as any).purchased)]));

		// Get all scanned ticket counts grouped by event_id
		const scanCountsRaw = await PurchaseInfo.findAll({
			attributes: ['event_id', [Sequelize.fn('COUNT', Sequelize.col('id')), 'scanned']],
			where: {
				event_id: eventIds,
				is_scan_success: true,
			},
			group: ['event_id'],
			raw: true,
		});

		const scanMap = new Map(scanCountsRaw.map(s => [s.event_id, parseInt((s as any).scanned)]));

		// Add total_tickets and purchased_tickets to each event
		for (const event of events.rows) {
			const eventTickets = event.tickets || [];

			const totalTickets = eventTickets.reduce((sum, ticket) => {
				return sum + (ticket.number_of_tickets || 0);
			}, 0);

			event.setDataValue('total_tickets', totalTickets);
			event.setDataValue('total_sold_tickets', purchaseMap.get(event.id) || 0);
			event.setDataValue('total_scanned_tickets', scanMap.get(event.id) || 0);
		}
	}

	return res.paginated(events, req);
}));

export default router;
