import authMiddleware from '@/middlewares/auth.middleware';
import { Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Delete Event - (Soft Delete)
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.delete('/events/:id', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: true,
			details: 'Event id is required',
			action: 'EVENT-DELETE',
			payloads: { id: id },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'DELETE',
			api_endpoint: '/events/:id'
		});
		return res.forbidden('Event id is required.');
	}
	const { user } = req;
	if (!user || !user.is_partner) return res.forbidden('Unauthorized Request');
	if (isNaN(Number(id))) return res.invalid('Event id is invalid.');

	await Event.update({
		deleted_at: new Date(),
	}, {
		where: { id },
	});
	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event deleted successfully',
		action: 'EVENT-DELETE',
		payloads: { id: id },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'DELETE',
		api_endpoint: '/events/:id'
	});
	return res.success('Event deleted successfully');
}));

export default router;
