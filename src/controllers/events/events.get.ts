import { Category, Event, EventTicket, Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';
import { col, fn, IncludeOptions, literal, Op, Order, WhereOptions } from 'sequelize';

const router = Router();

/**
 * Get All Events - for normal users
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/events', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { category, search, price, date, longitude, latitude } = req.query;

	const lat = parseFloat(latitude as string);
	const lng = parseFloat(longitude as string);
	const hasValidCoordinates = !isNaN(lat) && !isNaN(lng);

	const whereClause: WhereOptions = {};

	// Filter using search term - name of event
	if (search) whereClause.slug = { [Op.iLike]: `%${(search as string).trim()}%` };

	// Filter using category - category ID or name
	const categoryInclude: IncludeOptions = {
		model: Category,
		through: { attributes: []},
		attributes: []
	};
	if (typeof category === 'string') {
		const categoryIds = category.split(',').map((id) => Number(id)).filter(Boolean);
		if (categoryIds.length > 0) {
			categoryInclude.where = {
				[Op.or]: [
					{ id: { [Op.in]: categoryIds }},
					{ sub_id: { [Op.in]: categoryIds }},
				],
			};
		}
	}

	// Filter using price - ticket price (less than or equal to)
	const ticketInclude: IncludeOptions = {
		model: EventTicket,
		attributes: ['id', 'ticket_type', 'price']
	};
	if (!isNaN(Number(price))) {
		ticketInclude.where = { price: { [Op.lte]: Number(price) }};
	}

	// Filter using date
	// const eventDateInclude: IncludeOptions = {
	// 	model: EventDate,
	// };
	if (date) {
		const now = dayjs();
		switch (date) {
		case 'today':
			// eventDateInclude.where = {
			// 	date: {
			// 		[Op.between]: [now.startOf('day').toDate(), now.endOf('day').toDate()],
			// 	},
			// };
			whereClause.start_date_time = {
				[Op.between]: [now.startOf('day').toDate(), now.endOf('day').toDate()],
			};
			break;
		case 'tomorrow': {
			const tomorrow = now.add(1, 'day');
			// eventDateInclude.where = {
			// 	date: {
			// 		[Op.between]: [tomorrow.startOf('day').toDate(), tomorrow.endOf('day').toDate()],
			// 	},
			// };
			whereClause.start_date_time = {
				[Op.between]: [tomorrow.startOf('day').toDate(), tomorrow.endOf('day').toDate()],
			};
			break;
		}
		case 'this-week':
			// eventDateInclude.where = {
			// 	date: {
			// 		[Op.between]: [now.startOf('week').toDate(), now.endOf('week').toDate()],
			// 	},
			// };
			whereClause.start_date_time = {
				[Op.between]: [now.startOf('week').toDate(), now.endOf('week').toDate()],
			};
			break;
		case 'next-week': {
			const startOfNextWeek = now.endOf('week').add(1, 'day');
			const endOfNextWeek = startOfNextWeek.endOf('week');
			// eventDateInclude.where = {
			// 	date: {
			// 		[Op.between]: [startOfNextWeek.toDate(), endOfNextWeek.toDate()],
			// 	},
			// };
			whereClause.start_date_time = {
				[Op.between]: [startOfNextWeek.toDate(), endOfNextWeek.toDate()],
			};
			break;
		}
		default:
			break;
		}
	}

	const orderClause: Order = [];

	let userLocation: string | null = null;
	if (hasValidCoordinates) {
		userLocation = `ST_SetSRID(ST_MakePoint(${lng}, ${lat}), 4326)`;
	}

	const venueAttributes = [
		'id',
		'name',
		'slug',
	] as (string | [any, string])[];

	if (hasValidCoordinates && userLocation) {
		venueAttributes.push([
			fn('ST_DistanceSphere', col('coordinates'), literal(userLocation)),
			'distance_meters'
		]);
	}

	const venueInclude: IncludeOptions = {
		model: Venue,
		through: { attributes: []},
		required: hasValidCoordinates,
		attributes: venueAttributes,
	};

	if (hasValidCoordinates && userLocation) {
		// Order by nearest venue distance
		const subQuery = `(
            SELECT MIN(ST_DistanceSphere(v.coordinates, ${userLocation}))
            FROM venues v
            INNER JOIN "event_venue" ev ON v.id = ev."venue_id"
            WHERE ev."event_id" = "Event"."id"
        )`;
		orderClause.push([literal(subQuery), 'ASC']);
	}

	// Secondary ordering by publish time
	orderClause.push(['published_at', 'DESC']);

	const events = await Event.findAndCountAll({
		where: {
			...whereClause,
			status: true,
			published_at: {
				[Op.not]: null,
			},
			approved_at: {
				[Op.not]: null,
			},
			end_date_time: {
				[Op.gt]: dayjs().toDate()
			},
			deleted_at: null,
			rejected_at: null,
			cancelled_at: null,
		},
		attributes: ['id', 'image', 'slug', 'title', 'start_date_time', 'published_at', 'is_delayed', 'about'],
		distinct: true,
		order: orderClause,
		include: [
			categoryInclude,
			// { model: Artist, through: { attributes: []}},
			venueInclude,
			// eventDateInclude,
			// { model: EventTerm },
			ticketInclude,
			// { model: EventFaq },
			// { model: EventSlider },
			// { model: EventGallery },
			// { model: EventActivity },
		],
		...(paginated(req))
	});

	return res.paginated(events, req);
}));

export default router;
