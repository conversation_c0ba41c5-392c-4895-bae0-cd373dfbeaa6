import sequelize from '@/configs/database.config';
import { Artist, Category, Company, Event, EventActivity, EventDate, EventFaq, EventGallery, EventTerm, EventTicket, User, Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getDecodedUser } from '@/utils/general.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';
import { Op, WhereOptions } from 'sequelize';

const router = Router();

/**
 * Get Event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/events/:idOrSlug', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { idOrSlug } = req.params;
	if (!idOrSlug) return res.forbidden('Event id is required.');

	const decodedUser = getDecodedUser(req);

	let user: User | null = null;
	let partnerCompanyId: number | null = null;

	if (decodedUser) {
		const existingUser = await User.findOne({
			where: {
				id: decodedUser.id,
				email: decodedUser.email,
			},
			include: [
				{ model: Company, attributes: ['id']}
			]
		});
		if (existingUser) {
			user = existingUser;
			if ((user.is_partner || user.user_type === 'admin') && user.companies && user.companies.length > 0) {
				partnerCompanyId = user.companies[0]!.id;
			}
		}
	}

	const baseWhere: WhereOptions = isNaN(Number(idOrSlug))
		? { slug: idOrSlug, deleted_at: null }
		: { id: Number(idOrSlug), deleted_at: null };

	let accessControlWhere: WhereOptions = {};

	if (user && user.user_type === 'admin') {
		accessControlWhere = {};
	}
	else if (user && user.is_partner && partnerCompanyId !== null) {
		accessControlWhere = {
			[Op.or]: [
				// Condition 1: Event belongs to THIS partner's company (can be draft, published, expired, etc.)
				{ company_id: partnerCompanyId },
				// Condition 2: Event belongs to ANOTHER company BUT is published and not expired
				{
					published_at: { [Op.ne]: null },
					approved_at: { [Op.ne]: null },
					end_date_time: { [Op.gt]: dayjs().toDate() },
					rejected_at: null,
					cancelled_at: null,
				}
			]
		};
	} else {
		accessControlWhere = {
			published_at: { [Op.ne]: null },
			approved_at: { [Op.ne]: null },
			end_date_time: { [Op.gt]: dayjs().toDate() },
			rejected_at: null,
			cancelled_at: null,
		};
	}

	const finalWhere: WhereOptions = {
		[Op.and]: [
			baseWhere,
			accessControlWhere
		]
	};

	const event = await Event.findOne({
		where: finalWhere,
		replacements: { userId: user?.id },
		attributes: {
			include: [
				[sequelize.literal('(SELECT COUNT(*) FROM "event_likes" WHERE "event_likes"."event_id" = "Event"."id")'), 'likes_count'],
				[sequelize.literal(`
					CASE 
						WHEN ${user ? 'EXISTS (SELECT 1 FROM "event_likes" WHERE "event_likes"."event_id" = "Event"."id" AND "event_likes"."user_id" = :userId)' : 'false'} 
					THEN true ELSE false 
					END
				`), 'is_liked'],
				[sequelize.literal(`
					CASE 
						WHEN ${user ? 'EXISTS (SELECT 1 FROM "event_subscriptions" WHERE "event_subscriptions"."event_id" = "Event"."id" AND "event_subscriptions"."user_id" = :userId)' : 'false'}
					THEN true ELSE false 
					END
				`), 'is_subscribed'],
			]
		},
		include: [
			{ model: Category, through: { attributes: []}},
			{ model: Artist, through: { attributes: []}},
			{ model: Venue, through: { attributes: []}},
			{ model: EventTerm },
			{ model: EventTicket },
			{ model: EventDate },
			{ model: EventFaq },
			{ model: EventGallery },
			{ model: EventActivity },
		],
	});

	if (!event) return res.notFound('Event not found.');

	return res.single(event);
}));

export default router;
