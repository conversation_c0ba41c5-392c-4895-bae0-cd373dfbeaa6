import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Event, EventSlider } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Use event as slider
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/events-slider/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event id is required',
			action: 'EVENT-SLIDER-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-slider/:id'
		});
		return res.forbidden('Event id is required.');
	}
	if (isNaN(Number(id))) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event id is invalid.',
			action: 'EVENT-SLIDER-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-slider/:id'
		});
		return res.invalid('Event id is invalid.');
	}

	const { user } = req;
	if (!user || user.user_type !== 'admin') {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Unauthorized Request',
			action: 'EVENT-SLIDER-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-slider/:id'
		});
		return res.forbidden('Unauthorized Request');
	}

	const { fromDate, toDate } = req.body;
	if (!fromDate || !toDate) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'From and To date is required',
			action: 'EVENT-SLIDER-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-slider/:id'
		});
		return res.forbidden('From and To date is required.');
	}

	const event = await Event.findOne({ where: { id }});
	if (!event) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event not found',
			action: 'EVENT-SLIDER-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-slider/:id'
		});
		return res.notFound('Event not found');
	}

	await EventSlider.create({
		event_id: event.id,
		status: true,
		from: fromDate,
		to: toDate,
	});
	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event Set as slider successfully',
		action: 'EVENT-SLIDER-UPDATE',
		payloads: { id: id, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/events-slider/:id'
	});

	return res.created('Event Set as slider successfully');
}));

export default router;
