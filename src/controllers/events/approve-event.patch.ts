import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Approve Event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/events-approve/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Invalid request. Event ID is required',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-approve/:id'
		});
		return res.forbidden('Invalid request. Event ID is required');
	}

	const existingEvent = await Event.findOne({ where: { id }});
	if (!existingEvent) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event does not exist',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-approve/:id'
		});
		return res.forbidden('Event does not exist');
	}

	if (existingEvent.approved_at) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event is already approved',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-approve/:id'
		});
		return res.forbidden('Event is already approved');
	}

	if (!existingEvent.status) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event is not active and cannot be approved',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-approve/:id'
		});
		return res.forbidden('Event is not active and cannot be approved');
	}

	await existingEvent.update({
		approved_at: new Date(),
		published_at: new Date(),
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event approved successfully',
		action: 'EVENT-UPDATE',
		payloads: { ...req.params },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/events-approve/:id'
	});
	return res.success('Event approved successfully');
}));

export default router;
