import sequelize from '@/configs/database.config';
import authMiddleware from '@/middlewares/auth.middleware';
import { Event, EventActivity, EventArtist, EventCategory, EventDate, EventFaq, EventGallery, EventTerm, EventTicket, EventVenue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateSlug, getClientIP, hasOnlyEmptyValues } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Update Event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.put('/events/:id', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event id is required',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/events/:id'
		});
		return res.forbidden('Event id is required.');
	}
	const { user, company } = req;
	if (!user || !company || (user.user_type === 'normal' && !user.is_partner)) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Unauthorized Request',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/events/:id'
		});
		return res.forbidden('Unauthorized Request.');
	}

	const { step_1_info, step_2_info, step_3_info, is_published } = req.body;

	if (hasOnlyEmptyValues(step_1_info) && hasOnlyEmptyValues(step_2_info) && hasOnlyEmptyValues(step_3_info)) return res.forbidden('Please input at least one field.');

	const event = await Event.findOne({
		where: { id },
	});

	if (!event) {
		return res.forbidden('Event with given id does not exist.');
	}
	if (user.user_type !== 'admin' && (event.company_id !== company.id)) {
		return res.forbidden('You are not authorized to update this event.');
	}

	const slug = generateSlug(step_1_info.title);
	if (event.slug !== slug) {
		const existingEvent = await Event.findOne({
			where: { slug },
			paranoid: false,
		});
		if (existingEvent) return res.forbidden('Event with given title already exists.');
	}

	const eventStartDateTime = step_3_info.venues.length > 0 ? step_3_info?.venue_data[step_3_info.venues[0]]?.start_date_time || null : null;
	const eventEndDateTime = step_3_info.venues.length > 0 ? step_3_info?.venue_data[step_3_info.venues[0]]?.end_date_time || null : null;
		
	let durationInMinutes: number | null = null;
	if (eventStartDateTime && eventEndDateTime) {
		durationInMinutes = dayjs(eventEndDateTime).diff(dayjs(eventStartDateTime), 'minute');
	}

	const transaction = await sequelize.transaction();
	try {
		// Update main event details
		await Event.update({
			title: step_1_info.title || event.title,
			slug: step_1_info.title ? generateSlug(step_1_info.title) : event.slug,
			duration: durationInMinutes?.toString() || event.duration,
			about: step_1_info.about || event.about,
			image: step_1_info.cover_image || event.image,
			published_at: (is_published && user.user_type === 'admin') ? new Date() : event.published_at,
			status: is_published ? true : event.status,
			start_date_time: eventStartDateTime || event.start_date_time,
			end_date_time: eventEndDateTime || event.end_date_time,
			approved_at: (is_published && user.user_type === 'admin') ? new Date() : event.approved_at,
			rejected_at: is_published ? null : event.rejected_at,
			external_ticket_link: step_3_info.external_ticket_link || null,
		}, {
			where: { id },
			transaction
		});

		// Update categories
		// Remove existing categories
		await EventCategory.destroy({
			where: { event_id: id },
			transaction
		});

		if (step_1_info.categories?.length > 0) {
			// Add new categories
			await EventCategory.bulkCreate(
				step_1_info.categories.map((categoryId: number) => ({
					category_id: categoryId,
					event_id: id,
				})),
				{ transaction }
			);
		}

		// Update artists
		// Remove existing artists
		await EventArtist.destroy({
			where: { event_id: id },
			transaction
		});

		if (step_2_info.artists?.length > 0) {
			// Add new artists
			await EventArtist.bulkCreate(
				step_2_info.artists.map((artistId: number) => ({
					artist_id: artistId,
					event_id: id,
				})),
				{ transaction }
			);
		}

		// Update terms
		// First try to find existing terms
		const existingTerms = await EventTerm.findOne({
			where: { event_id: id },
			transaction
		});

		if (existingTerms) {
			// Update existing terms
			await EventTerm.update({
				terms: step_2_info.terms,
				status: true,
			}, {
				where: { event_id: id },
				transaction
			});
		} else {
			// Create new terms
			await EventTerm.create({
				event_id: id,
				terms: step_2_info.terms,
				status: true,
			}, {
				transaction
			});
		}

		// Update galleries
		// Remove existing galleries
		await EventGallery.destroy({
			where: { event_id: id },
			transaction
		});

		// Add new galleries
		if (step_2_info.galleries?.length > 0) {
			await EventGallery.bulkCreate(
				step_2_info.galleries.map((name: string) => ({
					event_id: id,
					name,
					status: true,
				})),
				{ transaction }
			);
		}

		// Update FAQs
		// Remove existing FAQs
		await EventFaq.destroy({
			where: { event_id: id },
			transaction
		});

		// Add new FAQs
		if (step_2_info.faqs?.length > 0) {
			await EventFaq.bulkCreate(
				step_2_info.faqs.map((faq: { question: string, answer: string }) => ({
					event_id: id,
					question: faq.question,
					answer: faq.answer,
					status: true,
				})),
				{ transaction }
			);
		}

		// Update activities
		// Remove existing activities
		await EventActivity.destroy({
			where: { event_id: id },
			transaction
		});

		// Add new activities
		if (step_2_info.activities?.length > 0) {
			await EventActivity.bulkCreate(
				step_2_info.activities.map((activity: { title: string, details: string }) => ({
					event_id: id,
					title: activity.title,
					details: activity.details,
					status: true,
				})),
				{ transaction }
			);
		}

		//Update Venues
		// Remove existing venues - this also removes event tickets and dates because they are associated with venues
		await EventVenue.destroy({
			where: { event_id: id },
			transaction
		});

		// Add new venues, tickets and dates
		if (step_3_info.venues) {
			for (const venueId of step_3_info.venues) {
				const eventVenue = await EventVenue.create({
					venue_id: venueId,
					event_id: id,
				}, { transaction });

				if (step_3_info.venue_data[venueId]) {
					const venueData = step_3_info.venue_data[venueId];
					
					if (venueData.date_time) {
						await EventDate.create({
							event_id: id,
							venue_id: venueId,
							event_venue_id: eventVenue.id,
							date: venueData.date_time,
						}, { transaction });
					}

					if (venueData.tickets) {
						const tickets = venueData.tickets;

						if (tickets.length > 0) {
							await EventTicket.bulkCreate(
								tickets
									.map((ticket: { name: string, count: string, amount: string }) => ({
										event_id: id,
										venue_id: venueId,
										event_venue_id: eventVenue.id,
										ticket_type: venueData.ticket_type || null,
										name: ticket.name || null,
										number_of_tickets: ticket.count || null,
										price: ticket.amount || null,
									})),
								{ transaction }
							);
						}
					}
				}
			}
		}

		await transaction.commit();
	} catch (error) {
		await transaction.rollback();
		// eslint-disable-next-line no-console
		console.error('An error occurred while updating the event', error);
		return res.failure('Event update failed. Please try again.');
	}
	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event published successfully',
		action: 'EVENT-UPDATE',
		payloads: { ...req.params, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PUT',
		api_endpoint: '/events/:id'
	});
	return is_published ? res.updated('Event published successfully') : res.updated();
}));

export default router;
