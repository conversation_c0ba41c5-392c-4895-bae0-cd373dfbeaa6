import authMiddleware from '@/middlewares/auth.middleware';
import { Event, EventLike } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Like or Unlike Event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/events-likes/:id', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: true,
			details: 'Invalid request. Event ID is required',
			action: 'EVENT-LIKE-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-likes/:id'
		});
		return res.forbidden('Invalid request. Event ID is required');
	}
	const { user } = req;
	if (!user) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: true,
			details: 'Unauthorized Request',
			action: 'EVENT-LIKE-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-likes/:id'
		});
		return res.forbidden('Unauthorized Request');
	}

	const { like } = req.body;
	if (typeof like !== 'boolean') return res.forbidden('Invalid data type. Like must be a boolean value');

	const existingEvent = await Event.findOne({ where: { id }});
	if (!existingEvent){
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: true,
			details: 'Event does not exist',
			action: 'EVENT-LIKE-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-likes/:id'
		});
		return res.forbidden('Event does not exist');
	}

	if (like) {
		await EventLike.create({ event_id: existingEvent.id, user_id: user.id });
	} else await EventLike.destroy({ where: { event_id: existingEvent.id, user_id: user.id }});
	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event like updated',
		action: 'EVENT-LIKE-UPDATE',
		payloads: { id: id, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/events-likes/:id'
	});
	return res.updated();
}));

export default router;
