import { Event, EventTicket, Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';
import { Op } from 'sequelize';

const router = Router();

/**
 * Get featured events or count of featured events
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/events-featured', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { getCount } = req.query;

	if (getCount === 'true') {
		const count = await Event.count({
			where: {
				status: true,
				deleted_at: null,
				published_at: { [Op.ne]: null },
				approved_at: { [Op.ne]: null },
				feature_from: { [Op.ne]: null },
				feature_expiry: { [Op.gte]: dayjs().startOf('day').toDate() }, // Feature expiry today or later
			}
		});

		return res.single({ count });
	} else {
		const events = await Event.findAndCountAll({
			where: {
				status: true,
				deleted_at: null,
				published_at: { [Op.ne]: null },
				approved_at: { [Op.ne]: null },
				end_date_time: { [Op.gt]: dayjs().toDate() },
				feature_from: { [Op.lte]: dayjs().startOf('day').toDate() }, // Feature from today or before
				feature_expiry: { [Op.gte]: dayjs().startOf('day').toDate() }, // Feature expiry today or later
			},
			attributes: ['id', 'image', 'slug', 'title', 'start_date_time', 'feature_from', 'is_delayed'],
			order: [['feature_from', 'ASC']],
			distinct: true,
			include: [
				{
					model: Venue,
					attributes: ['id', 'name', 'slug'],
				},
				{
					model: EventTicket,
					attributes: ['id', 'ticket_type', 'price'],
				}
			],
			...(paginated(req))
		});

		return res.paginated(events, req);
	}
}));

export default router;
