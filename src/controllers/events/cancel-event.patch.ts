import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Company, Event, PurchaseInfo, TicketBuyer, User } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { cancelEventEmail, notifyUserEventCancelledEmail, sendEmail } from '@/utils/postmark.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Cancel Event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/events-cancel/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (isNaN(Number(id))) {
		return res.forbidden('Invalid request. Please provide a valid event ID');
	}

	const { reason } = req.body;
	if (!reason || reason.trim().length === 0) {
		return res.forbidden('Please provide a valid reason');
	}

	const existingEvent = await Event.findOne({
		where: { id: Number(id) },
		include: [
			{
				model: Company,
				as: 'company',
				include: [
					{
						model: User,
						as: 'users',
					},
				],
			},
		],
	});
	if (!existingEvent) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event to be cancelled not found',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-cancel/:id'
		});
		return res.forbidden('Event does not exist');
	}

	if (existingEvent.cancelled_at) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event is already cancelled',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-cancel/:id'
		});
		return res.forbidden('Event is already cancelled');
	}

	await existingEvent.update({
		cancelled_at: new Date(),
		cancel_reason: reason,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event cancelled successfully',
		action: 'EVENT-UPDATE',
		payloads: { ...req.params, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/events-cancel/:id'
	});

	// Send email to the partner
	try {
		const user = existingEvent?.company?.users?.[0];

		if (!user || !user.email) {
			throw new Error('No valid company user with an email found');
		}

		await sendEmail(cancelEventEmail(
			user.email,
			reason,
			existingEvent.title,
		));
	} catch (error) {
		// eslint-disable-next-line no-console
		console.error('Error sending event cancellation email to partner', error);
	}

	// Send email to the users
	try {
		const purchaseInfos = await PurchaseInfo.findAll({
			where: {
				event_id: Number(id),
			},
			include: [
				{
					model: TicketBuyer,
					as: 'ticket_buyer',
					attributes: ['id', 'full_name', 'email'],
					required: true,
				},
			],
		});

		if (purchaseInfos?.length > 0) {
			for (const purchaseInfo of purchaseInfos) {
				try {
					await sendEmail(notifyUserEventCancelledEmail(
						purchaseInfo.ticket_buyer.email,
						purchaseInfo.ticket_buyer.full_name,
						existingEvent.title,
						reason,
					));
				} catch (error) {
					// eslint-disable-next-line no-console
					console.error(`Error sending event cancellation email to buyer id: ${purchaseInfo.ticket_buyer.id}`, error);
				}
			}
		}
	} catch (error) {
		// eslint-disable-next-line no-console
		console.error(`Error sending event cancellation email to users for event id: ${id}`, error);
	}

	return res.success('Event cancelled successfully');
}));

export default router;
