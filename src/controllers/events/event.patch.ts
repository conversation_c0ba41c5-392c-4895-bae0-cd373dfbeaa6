import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Activate or Deactivate Event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/events/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Invalid request. Event ID is required',
			action: 'EVENT-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events/:id'
		});
		return res.forbidden('Invalid request. Event ID is required');
	}
	const { user } = req;
	if (!user || user.user_type !== 'admin') {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Unauthorized Request',
			action: 'EVENT-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events/:id'
		});
		return res.forbidden('Unauthorized Request');
	}

	const { status } = req.body;
	if (typeof status !== 'boolean') {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Invalid request. Event status is required and must be a boolean value',
			action: 'EVENT-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events/:id'
		});
		return res.forbidden('Invalid request. Event status is required and must be a boolean value');
	}

	const existingEvent = await Event.findOne({ where: { id }});
	if (!existingEvent) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event does not exist',
			action: 'EVENT-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events/:id'
		});
		return res.forbidden('Event does not exist');
	}

	// Check if Event being activated is published or not
	if (status && !existingEvent.published_at) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event is not published and cannot be activated',
			action: 'EVENT-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events/:id'
		});
		return res.forbidden('Event is not published and cannot be activated');
	}

	await existingEvent.update({ status });

	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: `Event ${existingEvent.title} ${status ? 'activated' : 'deactivated'} successfully`,
		action: 'EVENT-UPDATE',
		payloads: { id: id, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/events/:id'
	});
	return res.success(`Event ${existingEvent.title} ${status ? 'activated' : 'deactivated'} successfully`);
}));

export default router;
