import authMiddleware from '@/middlewares/auth.middleware';
import { Event, PurchaseInfo, TicketBuyer } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { notifyUserEventDelayedEmail, sendEmail } from '@/utils/postmark.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Delay Event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/events-delay/:id', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (isNaN(Number(id))) {
		return res.forbidden('Invalid request. Please provide a valid event ID');
	}

	const { user, company }= req;
	if (!user?.is_partner || !company) {
		return res.forbidden('Unauthorized Request');
	}

	const { startDate, endDate } = req.body;
	if (!startDate || !endDate) {
		return res.forbidden('Event start and end date is required.');
	}

	const existingEvent = await Event.findOne({
		where: {
			id: Number(id),
			company_id: company.id,
		},
	});
	if (!existingEvent) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event to be delayed not found',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-delay/:id'
		});
		return res.forbidden('Event does not exist');
	}

	if (existingEvent.cancelled_at) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Cannot delay a cancelled event',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-delay/:id'
		});
		return res.forbidden('Cannot delay a cancelled event');
	}

	const oldStartDate = existingEvent.start_date_time;
	const oldEndDate = existingEvent.end_date_time;

	await existingEvent.update({
		is_delayed: true,
		start_date_time: startDate,
		end_date_time: endDate,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event delayed successfully',
		action: 'EVENT-UPDATE',
		payloads: { ...req.params, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/events-delay/:id'
	});

	// Send email to the ticket buyers
	try {
		const purchaseInfos = await PurchaseInfo.findAll({
			where: {
				event_id: Number(id),
			},
			include: [
				{
					model: TicketBuyer,
					as: 'ticket_buyer',
					attributes: ['id', 'full_name', 'email'],
					required: true,
				},
			],
		});

		if (purchaseInfos?.length > 0) {
			for (const purchaseInfo of purchaseInfos) {
				try {
					await sendEmail(
						notifyUserEventDelayedEmail({
							email: purchaseInfo.ticket_buyer.email,
							userName: purchaseInfo.ticket_buyer.full_name,
							eventName: existingEvent.title,
							oldStartDateTime: dayjs(oldStartDate).format('Do MMMM YYYY, h:mm A'),
							oldEndDateTime: dayjs(oldEndDate).format('Do MMMM YYYY, h:mm A'),
							newStartDateTime: dayjs(startDate).format('Do MMMM YYYY, h:mm A'),
							newEndDateTime: dayjs(endDate).format('Do MMMM YYYY, h:mm A'),
						})
					);
				} catch (error) {
					// eslint-disable-next-line no-console
					console.error(`Error sending event delayed email to buyer id: ${purchaseInfo.ticket_buyer.id}`, error);
				}
			}
		}
	} catch (error) {
		// eslint-disable-next-line no-console
		console.error(`Error sending event delayed email to users for event id: ${id}`, error);
	}

	return res.success('Event delayed successfully');
}));

export default router;
