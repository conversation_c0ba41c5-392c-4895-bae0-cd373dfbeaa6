import sequelize from '@/configs/database.config';
import authMiddleware from '@/middlewares/auth.middleware';
import { Event, EventActivity, EventArtist, EventCategory, EventFaq, EventGallery, EventTerm, EventTicket, EventVenue } from '@/models';
import { IEvent } from '@/models/event.model';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateSlug, getClientIP, hasOnlyEmptyValues } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Add Event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/events', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user || (user.user_type === 'normal' && !user.is_partner)) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Unauthorized Request',
			action: 'EVENT-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/events'
		});
		return res.forbidden('Unauthorized Request.');
	}
	const { company } = req;
	if (!company) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Company not found',
			action: 'EVENT-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/events'
		});
		return res.forbidden('Company not found.');
	}
	
	const { step_1_info, step_2_info, step_3_info } = req.body;
	if (hasOnlyEmptyValues(step_1_info) && hasOnlyEmptyValues(step_2_info) && hasOnlyEmptyValues(step_3_info)) return res.forbidden('Please input at least one field.');

	if (!step_1_info.title) return res.forbidden('Event title is required.');

	const slug = generateSlug(step_1_info.title);
	const existingEvent = await Event.findOne({
		where: { slug },
		paranoid: false,
	});
	if (existingEvent) return res.forbidden('Event with given title already exists.');

	let newEvent: IEvent | null = null;

	const eventStartDateTime = step_3_info.venues.length > 0 ? step_3_info?.venue_data[step_3_info.venues[0]]?.start_date_time || null : null;
	const eventEndDateTime = step_3_info.venues.length > 0 ? step_3_info?.venue_data[step_3_info.venues[0]]?.end_date_time || null : null;
	
	let durationInMinutes: number | null = null;
	if (eventStartDateTime && eventEndDateTime) {
		durationInMinutes = dayjs(eventEndDateTime).diff(dayjs(eventStartDateTime), 'minute');
	}

	const transaction = await sequelize.transaction();
	try {
		newEvent = await Event.create({
			title: step_1_info.title || null,
			slug: step_1_info.title ? generateSlug(step_1_info.title) : null,
			duration: durationInMinutes?.toString(),
			about: step_1_info.about || null,
			image: step_1_info.cover_image || null,
			company_id: company.id || null,
			status: false,
			start_date_time: eventStartDateTime,
			end_date_time: eventEndDateTime,
		},
		{ transaction });

		if (step_1_info.categories.length > 0) {
			await EventCategory.bulkCreate(
				step_1_info.categories.map((categoryId: number) => ({
					category_id: categoryId,
					event_id: newEvent?.id,
				})),
				{ transaction }
			);
		}

		if (step_2_info.artists.length > 0) {
			await EventArtist.bulkCreate(
				step_2_info.artists.map((artistId: number) => ({
					artist_id: artistId,
					event_id: newEvent?.id,
				})),
				{ transaction }
			);
		}
		
		if (step_2_info.terms) {
			await EventTerm.create({
				event_id: newEvent.id,
				terms: step_2_info.terms,
				status: true,
			},
			{ transaction });
		}

		if (step_2_info.galleries?.length > 0) {
			await EventGallery.bulkCreate(
				step_2_info.galleries.map((name: string) => ({
					event_id: newEvent?.id,
					name,
					status: true,
				})),
				{ transaction }
			);
		}

		if (step_2_info.faqs?.length > 0) {
			await EventFaq.bulkCreate(
				step_2_info.faqs.map((faq: { question: string, answer: string }) => ({
					event_id: newEvent?.id,
					question: faq.question,
					answer: faq.answer,
					status: true,
				})),
				{ transaction }
			);
		}

		if (step_3_info.activities?.length > 0) {
			await EventActivity.bulkCreate(
				step_3_info.activities.map((activity: { title: string, details: string }) => ({
					event_id: newEvent?.id,
					title: activity.title,
					details: activity.details,
					status: true,
				})),
				{ transaction }
			);
		}

		if (step_3_info.venues) {
			for (const venueId of step_3_info.venues) {
				const eventVenue = await EventVenue.create({
					venue_id: venueId,
					event_id: newEvent?.id,
				}, { transaction });

				if (step_3_info.venue_data[venueId]) {
					const venueData = step_3_info.venue_data[venueId];
					
					// if (venueData.date_time) {
					// 	await EventDate.create({
					// 		event_id: newEvent?.id,
					// 		venue_id: venueId,
					// 		event_venue_id: eventVenue.id,
					// 		date: venueData.date_time,
					// 	}, { transaction });
					// }

					const tickets = venueData.tickets;

					if (tickets.length > 0) {
						await EventTicket.bulkCreate(
							tickets.map((ticket: { name: string, count: string, amount: string }) => ({
								event_id: newEvent?.id,
								venue_id: venueId,
								event_venue_id: eventVenue.id,
								ticket_type: venueData.ticket_type || null,
								name: ticket.name || null,
								number_of_tickets: ticket.count || null,
								price: ticket.amount || null,
							})),
							{ transaction }
						);
					}
				}
			}
		}

		await transaction.commit();
	} catch (e) {
		// eslint-disable-next-line no-console
		console.log(e);
		await transaction.rollback();
		return res.failure('Event creation failed. Please try again.');
	}
	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event was added successfully',
		action: 'EVENT-CREATE',
		payloads: { step_1_inf0: step_1_info, step_2_info: step_2_info, step_3_info: step_3_info },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/events/:id?'
	});
	return res.created('Event was added successfully', newEvent);
}));

export default router;
