import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Set event as featured
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/events-featured/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id){
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event id is required',
			action: 'EVENT-FEATURED-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-featured/:id'
		});
		return res.forbidden('Event id is required.');
	}
	if (isNaN(Number(id))) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event id is invalid',
			action: 'EVENT-FEATURED-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-featured/:id'
		});
		return res.invalid('Event id is invalid.');
	}

	const { user } = req;
	if (!user || user.user_type !== 'admin') {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Unauthorized Request',
			action: 'EVENT-FEATURED-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-featured/:id'
		});
		return res.forbidden('Unauthorized Request');
	}

	const { fromDate, toDate } = req.body;

	const event = await Event.findOne({ where: { id }});
	if (!event) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event not found',
			action: 'EVENT-FEATURED-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-featured/:id'
		});
		return res.notFound('Event not found');
	}

	await event.update({
		feature_from: fromDate,
		feature_expiry: toDate,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event set as featured successfully',
		action: 'EVENT-FEATURED-UPDATE',
		payloads: { id: id, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/events-featured/:id'
	});

	return res.success('Event set as featured successfully');
}));

export default router;
