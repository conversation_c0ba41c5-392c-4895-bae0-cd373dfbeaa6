import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Company, Event, User } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { rejectEventEmail, sendEmail } from '@/utils/postmark.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Reject Event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/events-reject/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (isNaN(Number(id))) {
		return res.forbidden('Invalid request. Please provide a valid event ID');
	}

	const { reason } = req.body;
	if (!reason || reason.trim().length === 0) {
		return res.forbidden('Please provide a valid reason');
	}

	const existingEvent = await Event.findOne({
		where: { id: Number(id) },
		include: [
			{
				model: Company,
				as: 'company',
				include: [
					{
						model: User,
						as: 'users',
					},
				],
			},
		],
	});
	if (!existingEvent) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event to be rejected not found',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-reject/:id'
		});
		return res.forbidden('Event does not exist');
	}

	if (existingEvent.approved_at) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event to be rejected is already approved',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-reject/:id'
		});
		return res.forbidden('Event is already approved. It cannot be rejected');
	}

	if (existingEvent.rejected_at) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Event is already rejected',
			action: 'EVENT-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/events-reject/:id'
		});
		return res.forbidden('Event is already rejected');
	}

	await existingEvent.update({
		rejected_at: new Date(),
		reject_reason: reason,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event rejected successfully',
		action: 'EVENT-UPDATE',
		payloads: { ...req.params, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/events-reject/:id'
	});

	try {
		const user = existingEvent?.company?.users?.[0];

		if (!user || !user.email) {
			throw new Error('No valid company user with an email found');
		}

		await sendEmail(rejectEventEmail(
			user.email,
			reason,
			existingEvent.title,
		));

		return res.success('Event rejected and email sent to the partner successfully');
	} catch (error) {
		// eslint-disable-next-line no-console
		console.error(`Error sending event rejection email for event id: ${id}`, error);
		return res.success('Event rejected successfully but email could not be sent to the partner');
	}
	
}));

export default router;
