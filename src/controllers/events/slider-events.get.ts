import { Event, EventSlider } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';
import { Op } from 'sequelize';

const router = Router();

/**
 * Get slider events or count of slider events
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/events-slider', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { getCount } = req.query;

	if (getCount === 'true') {
		const count = await Event.count({
			where: {
				status: true,
				deleted_at: null,
				published_at: { [Op.ne]: null },
				approved_at: { [Op.ne]: null },
				image: { [Op.ne]: null },
			},
			distinct: true,
			include: [
				{
					model: EventSlider,
					where: {
						status: true,
						from: { [Op.ne]: null },
						to: { [Op.gte]: dayjs().startOf('day').toDate() }, // expiry today or later
					}
				}
			]
		});

		return res.single({ count });
	} else {
		const events = await Event.findAndCountAll({
			where: {
				status: true,
				deleted_at: null,
				published_at: { [Op.ne]: null },
				approved_at: { [Op.ne]: null },
				end_date_time: { [Op.gt]: dayjs().toDate() },
				image: { [Op.ne]: null },
			},
			attributes: ['id', 'slug', 'image', 'published_at', 'is_delayed'],
			order: [['published_at', 'DESC']],
			distinct: true,
			include: [
				{
					model: EventSlider,
					attributes: [],
					where: {
						status: true,
						from: { [Op.lte]: dayjs().startOf('day').toDate() }, // from today or before
						to: { [Op.gte]: dayjs().startOf('day').toDate() }, // expiry today or later
					}
				}
			]
		});

		return res.collection(events);
	}
}));

export default router;
