import authMiddleware from '@/middlewares/auth.middleware';
import { PurchaseInfo, TicketBuyer, Event, ScanLog, EventTicket, Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Verify ticket from the scan
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/scan/verify-ticket', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user, company } = req;
	if (!user || !user.is_partner || !company) return res.forbidden('Unauthorized Request');

	if (req.headers['x-platform'] !== 'mobile') {
		return res.forbidden('Unauthorized Request');
	}

	const { ticket_id } = req.body;
	if (!ticket_id) return res.forbidden('Invalid request. Ticket ID is required');
	if (isNaN(Number(ticket_id))) return res.forbidden('Invalid Ticket ID');

	const ticket = await PurchaseInfo.findOne({
		where: {
			id: Number(ticket_id),
		},
		include: [
			{
				model: Event,
				as: 'event',
				attributes: ['company_id', 'id', 'start_date_time', 'end_date_time', 'title'],
			},
			{
				model: Venue,
				as: 'venue',
				attributes: ['id', 'name', 'address']
			},
			{
				model: TicketBuyer,
				as: 'ticket_buyer',
				attributes: ['id', 'full_name', 'email', 'phone_number', 'total_amount', 'payment_method'],
			},
			{
				model: EventTicket,
				as: 'event_ticket',
				attributes: ['id', 'name', 'price'],
			}
		]
	});

	if (!ticket) {
		return res.forbidden('Invalid Ticket ID. Ticket not found');
	}

	if (!ticket.event) {
		return res.forbidden('Invalid Ticket. Ticket is not associated with any event');
	}

	if (!ticket.ticket_buyer) {
		return res.forbidden('Invalid Ticket. Ticket is not associated with any ticket buyer');
	}

	if (ticket.event.company_id !== company.id) {
		return res.forbidden('Invalid Ticket. Ticket is not associated with your company');
	}

	if (ticket.is_scan_success) {
		return res.forbidden('Ticket is already scanned and verified');
	}

	const eventEndDateTime = dayjs(ticket.event.end_date_time);
	if (eventEndDateTime.isBefore(dayjs(), 'minute')) {
		await ScanLog.create({
			partner_id: user.id,
			ticket_id: ticket.id,
			event_id: ticket.event.id,
			buyer_id: ticket.ticket_buyer.id,
			status: 'fail',
			title: 'Verification failed',
			details: 'Event of this ticket has already ended',
		});
		return res.forbidden('Event of this ticket has already ended.');
	}

	await ticket.update({ is_scan_success: true });

	await ScanLog.create({
		partner_id: user.id,
		ticket_id: ticket.id,
		event_id: ticket.event.id,
		buyer_id: ticket.ticket_buyer.id,
		status: 'success',
		title: 'Verification successful',
		details: 'Ticket verified successfully',
	});

	const updatedTotalScannedTickets = await PurchaseInfo.count({
		where: {
			event_id: ticket.event.id,
			is_scan_success: true,
		}
	});

	return res.success('Ticket verified successfully', {
		ticket,
		updatedTotalScannedTickets,
	});
}));

export default router;
