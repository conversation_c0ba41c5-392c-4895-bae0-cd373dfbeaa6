import authMiddleware from '@/middlewares/auth.middleware';
import { Event, PurchaseInfo, ScanLog, TicketBuyer } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import { type Request, type Response, Router } from 'express';
import { WhereOptions } from 'sequelize';

const router = Router();

/**
 * Get Scan Logs of an event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/scan/:eventId/logs', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user, company } = req;
	if (!user || !user.is_partner || !company) return res.forbidden('Unauthorized Request');

	const { eventId } = req.params;
	if (isNaN(Number(eventId))) return res.forbidden('Invalid event id');

	const { filter } = req.query;

	const whereClause: WhereOptions<ScanLog> = {
		event_id: Number(eventId),
		partner_id: user.id,
	};

	if (filter === 'success') {
		whereClause.status = 'success';
	} else if (filter === 'fail') {
		whereClause.status = 'fail';
	}

	const event = await Event.findOne({
		where: {
			id: Number(eventId),
		}
	});

	if (!event) return res.notFound('Event not found');

	const scanLogs = await ScanLog.findAndCountAll({
		where: {
			...whereClause,
		},
		include: [
			{
				model: PurchaseInfo,
				as: 'purchase_info',
			},
			{
				model: TicketBuyer,
				as: 'ticket_buyer',
			}
		],
		...(paginated(req))
	});

	return res.paginated(scanLogs, req);
}));

export default router;
