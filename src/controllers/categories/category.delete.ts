import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Category, Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Delete Category
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.delete('/categories/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: true,
			details: 'Invalid request. Category ID is required',
			action: 'CATEGORY-DELETE',
			payloads: { id: id },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'DELETE',
			api_endpoint: '/categories/:id'
		});
		return res.forbidden('Invalid request. Category ID is required');
	}

	const { user } = req;
	if (!user || user.user_type !== 'admin') {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: true,
			details: 'Unauthorized Request',
			action: 'CATEGORY-DELETE',
			payloads: { id: id },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'DELETE',
			api_endpoint: '/categories/:id'
		});
		return res.forbidden('Unauthorized Request');
	}

	const existingCategory = await Category.findOne({ where: { id }});
	if (!existingCategory) {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: true,
			details: 'Category does not exist',
			action: 'CATEGORY-DELETE',
			payloads: { id: id },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'DELETE',
			api_endpoint: '/categories/:id'
		});
		return res.forbidden('Category does not exist');
	}

	// Check if category is being used by any events
	const event = await Event.findOne({
		include: [
			{
				model: Category,
				where: { id: Number(id) }
			}
		]
	});
	if (event) return res.forbidden('Category is being used by some events and cannot be deleted');

	await existingCategory.destroy();
	userLog({
		user_id: req.user?.id,
		event_type: 'CATEGORY',
		type: true,
		details: 'Category deleted successfully',
		action: 'CATEGORY-DELETE',
		payloads: { id: id },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'DELETE',
		api_endpoint: '/categories/:id'
	});

	return res.success('Category deleted successfully');
}));

export default router;
