import sequelize from '@/configs/database.config';
import { Category } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import { type Request, type Response, Router } from 'express';
import { Order } from 'sequelize';

const router = Router();

/**
 * Get Categories
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/categories', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { getMainCategories, type, getNavCategories } = req.query;

	let categoryStatus: boolean | null;
	if (type) {
		categoryStatus = type === 'active' ? true : type === 'inactive' ? false : null;
	} else categoryStatus = null;

	const queryOptions = {
		where: {
			app_type: 1,
			...(getNavCategories ? { navigation_list: true } : {}),
			...(getMainCategories ? { is_main: true } : {}),
			...(typeof categoryStatus === 'boolean' ? { status: categoryStatus } : {})
		},
		...(getMainCategories
			? {
				include: {
					model: Category,
					as: 'sub_categories'
				}
			}
			: {}),
		order: getMainCategories
			? [['id', 'ASC']] as Order
			: [
				[
					sequelize.literal(`
					CASE 
						WHEN "Category"."is_main" = true THEN 0 
						ELSE 1 
					END
				`),
					'ASC'
				],
				['id', 'ASC']
			] as Order
	};

	const categories = await Category.findAndCountAll({
		...queryOptions,
		...(paginated(req))
	});

	return res.paginated(categories, req);
}));

export default router;
