import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Category } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateSlug, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Create Category
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/categories', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user || user.user_type !== 'admin') {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: false,
			details: 'Unauthorized Request',
			action: 'CATEGORY-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/categories'
		});
		return res.forbidden('Unauthorized Request');
	}
	
	const { name, sub_id, app_type, info, icon_name, meta_description, meta_keywords } = req.body;

	if (!name || !app_type) {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: false,
			details: 'Invalid data. Category name and app type are required',
			action: 'CATEGORY-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/categories'
		});
		return res.forbidden('Invalid data. Category name and app type are required');
	}

	const slug = generateSlug(name);

	const existingCategory = await Category.findOne({ where: { slug }});
	
	if (existingCategory) {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: false,
			details: `Category "${name}" already exists`,
			action: 'CATEGORY-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/categories'
		});
		return res.forbidden(`Category "${name}" already exists`);
	}

	await Category.create({
		name,
		sub_id,
		app_type,
		info: info || null,
		slug,
		status: true,
		is_main: sub_id ? false : true,
		icon_name: icon_name || null,
		meta_description: meta_description || null,
		meta_keywords: meta_keywords ? meta_keywords.join(',') : null,
	});
	userLog({
		user_id: req.user?.id,
		event_type: 'CATEGORY',
		type: true,
		details: 'Category created successfully',
		action: 'CATEGORY-CREATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/categories'
	});
	return res.created('Category created successfully');
}));

export default router;
