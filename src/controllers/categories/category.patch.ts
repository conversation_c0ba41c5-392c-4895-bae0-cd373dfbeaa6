import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Category, Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Activate or Deactivate Category
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/categories/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) return res.forbidden('Invalid request. Category ID is required');

	const { user } = req;
	if (!user || user.user_type !== 'admin') return res.forbidden('Unauthorized Request');

	const { status, navigation_list } = req.body;
	if (typeof status !== 'boolean' && typeof navigation_list !== 'boolean') return res.forbidden('Invalid request. Status and navigation list are required');

	const existingCategory = await Category.findOne({ where: { id }});
	if (!existingCategory) return res.forbidden('Category does not exist');

	// Check if category is being used by any events if it is being deactivated
	if (!status) {
		const event = await Event.findOne({
			include: [
				{
					model: Category,
					where: { id: Number(id) }
				}
			]
		});
		if (event) return res.forbidden('Category is being used by some events and cannot be deactivated');
	}

	await existingCategory.update({
		status: typeof status === 'boolean' ? status : existingCategory.status,
		navigation_list: typeof navigation_list === 'boolean' ? navigation_list : existingCategory.navigation_list,
	});

	if (typeof status === 'boolean'){
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: true,
			details: `Category ${status ? 'activated' : 'deactivated'} successfully`,
			action: 'CATEGORY-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/categories/:id'
		});
		return res.success(`Category ${status ? 'activated' : 'deactivated'} successfully`);
	} else if (typeof navigation_list === 'boolean') {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: true,
			details: `Category ${navigation_list ? 'Set in' : 'Removed From'} Navigation list successfully`,
			action: 'CATEGORY-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/categories/:id'
		});
		return res.success(`Category ${navigation_list ? 'Set in' : 'Removed From'} Navigation list successfully`);
	} else {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: true,
			details: 'Category updated successfully',
			action: 'CATEGORY-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/categories/:id'
		});
		return res.success('Category updated successfully');
	}
}));

export default router;
