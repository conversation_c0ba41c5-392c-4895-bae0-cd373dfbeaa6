import { Category } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Get Category by id
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/categories/:id', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) return res.forbidden('Invalid request. Category ID is required');

	const existingCategory = await Category.findOne({ where: { id }});
	if (!existingCategory) return res.forbidden('Category does not exist');

	return res.single(existingCategory);
}));

export default router;
