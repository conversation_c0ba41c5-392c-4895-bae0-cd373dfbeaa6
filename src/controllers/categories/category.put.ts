import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Category } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateSlug, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Edit Category
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.put('/categories/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: false,
			details: 'Invalid request. Category ID is required',
			action: 'CATEGORY-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/categories/:id'
		});
		return res.forbidden('Invalid request. Category ID is required');
	}

	const { user } = req;
	if (!user || user.user_type !== 'admin') {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: false,
			details: 'Unauthorized Request',
			action: 'CATEGORY-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/categories/:id'
		});
		return res.unauthorized('Unauthorized Request');
	}

	const { name, sub_id, app_type, info, icon_name, meta_description, meta_keywords } = req.body;

	if (!name || !app_type) {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: false,
			details: 'Invalid data. Category name and app type are required',
			action: 'CATEGORY-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/categories/:id'
		});
		return res.forbidden('Invalid data. Category name and app type are required');
	}

	const existingCategory = await Category.findOne({
		where: { id },
		include: [
			{
				model: Category,
				as: 'sub_categories',
			}
		]
	});
	if (!existingCategory) {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: false,
			details: 'Category does not exist',
			action: 'CATEGORY-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/categories/:id'
		});
		return res.forbidden('Category does not exist');
	}
	if (sub_id && existingCategory.sub_categories && existingCategory.sub_categories.length > 0) {
		userLog({
			user_id: req.user?.id,
			event_type: 'CATEGORY',
			type: false,
			details: 'This Category cannot be changed into a sub-category, it is a main category and has existing sub-categories.',
			action: 'CATEGORY-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/categories/:id'
		});
		return res.forbidden('This Category cannot be changed into a sub-category, it is a main category and has existing sub-categories.');
	}

	const slug = generateSlug(name);

	if (existingCategory.slug !== slug) {
		const newExistingCategory = await Category.findOne({ where: { slug }});
		if (newExistingCategory) return res.forbidden(`Category "${name}" already exists`);
	}

	await existingCategory.update({
		name,
		sub_id,
		app_type,
		info: info || null,
		slug,
		is_main: sub_id ? false : true,
		icon_name: icon_name || null,
		meta_description: meta_description || null,
		meta_keywords: meta_keywords ? meta_keywords.join(',') : null,
	});
	userLog({
		user_id: req.user?.id,
		event_type: 'CATEGORY',
		type: true,
		details: 'Category updated successfully',
		action: 'CATEGORY-UPDATE',
		payloads: { id: id, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PUT',
		api_endpoint: '/categories/:id'
	});

	return res.success('Category updated successfully');
}));

export default router;
