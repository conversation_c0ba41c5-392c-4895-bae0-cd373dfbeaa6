import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';
import multer from 'multer';
import AWS from 'aws-sdk';
import { FileMimeType } from '@/configs/file.config';
import { generateRandomString } from '@/utils/general.util';
import envUtil from '@/utils/env.util';

AWS.config.update({
	accessKeyId: envUtil.AWS_ACCESS_KEY_ID,
	secretAccessKey: envUtil.AWS_SECRET_KEY
});

const s3 = new AWS.S3();
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

const router = Router();

/**
 * Upload image to S3 bucket
 */
router.post(
	'/upload',
	upload.single('file'),
	errorWrapper(
		async (req: Request, res: Response): Promise<Response> => {
			if (!req.file) return res.forbidden('File is required');

			const params = {
				Bucket: envUtil.AWS_S3_BUCKET,
				Key: `img/${generateRandomString(12)}.${FileMimeType[req.file.mimetype]}`,
				Body: req.file.buffer,
				ContentType: req.file.mimetype,
				ACL: 'public-read'
			};
			const response = await s3.upload(params).promise();

			return res.single(response);
		}
	)
);

export default router;
