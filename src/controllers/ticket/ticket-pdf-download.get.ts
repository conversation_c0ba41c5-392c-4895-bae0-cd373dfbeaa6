import { errorWrapper } from '@/utils/error-wrapper.util';
import dayjs from 'dayjs';
import { Router, type Request, type Response } from 'express';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { Event, EventTicket, PurchaseInfo, TicketBuyer, User, Venue } from '@/models';
import authMiddleware from '@/middlewares/auth.middleware';
import { generateTicketPdf } from '@/utils/ticket-pdf.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';

dayjs.extend(advancedFormat);

const router = Router();

/**
 * Download event ticket pdf
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 */
router.get('/ticket-pdf/:id', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) return res.forbidden('Unauthorized access. User is required');
	
	const { id } = req.params;
	if (!id) return res.forbidden('Invalid request. Ticket id is required');

	const purchaseInfo = await PurchaseInfo.findOne({
		where: {
			id,
			user_id: user.id,
		},
		attributes: ['id', 'created_at'],
		include: [
			{
				model: User,
				required: true,
				attributes: ['f_name', 'l_name'],
			},
			{
				model: TicketBuyer,
				required: true,
			},
			{
				model: Event,
				required: true,
				attributes: ['title', 'start_date_time', 'end_date_time', 'id'],
			},
			{
				model: Venue,
				required: true,
				attributes: ['name'],
			},
			{
				model: EventTicket,
				required: true,
				attributes: ['name', 'price'],
			}
		]
	});
	if (!purchaseInfo) return res.notFound('Ticket details not found');

	const formattedDateRange = purchaseInfo.event?.start_date_time && purchaseInfo.event?.end_date_time
		? `${dayjs(purchaseInfo.event.start_date_time).format('ddd, Do MMM YYYY hh:mm A')} - ${dayjs(purchaseInfo.event.end_date_time).format('ddd, Do MMM YYYY hh:mm A')}`
		: 'N/A';

	const ticketData = {
		ticketNo: purchaseInfo.id.toString(),
		eventId: purchaseInfo.event.id.toString(),
		eventName: purchaseInfo.event.title,
		eventDate: formattedDateRange,
		eventLocation: purchaseInfo.venue.name,
		userName: purchaseInfo.ticket_buyer.full_name,
		ticketType: purchaseInfo.event_ticket.name,
		price: purchaseInfo.paid_amount ? `NRs. ${purchaseInfo.paid_amount}` : 'N/A',
		issueDate: dayjs(purchaseInfo.createdAt).format('ddd, Do MMM YYYY hh:mm A'),
	};

	const pdfBuffer = await generateTicketPdf(ticketData);

	userLog({
		event_type: 'TICKET',
		type: false,
		details: 'Ticket pdf generated successfully',
		action: 'TICKET-GENERATED',
		payloads: { ...req.params },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/ticket-pdf/:id'
	});

	res.setHeader('Content-Type', 'application/pdf');

	return res.file(pdfBuffer, `${ticketData.eventName}_Ticket-${ticketData.ticketNo}`, 'pdf');
}));

export default router;
