import authMiddleware from '@/middlewares/auth.middleware';
import { ActiveTicketSession } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Get Active Ticket Session
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/active-ticket-session/:id', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;

	const { id } = req.params;
	if (isNaN(Number(id))) return res.forbidden('Invalid Session ID');

	const session = await ActiveTicketSession.findOne({
		where: {
			id: Number(id),
		}
	});

	if (!session) return res.notFound('Session not found');
	if (session.user_id !== user?.id) return res.forbidden('Invalid Session ID');

	return res.single(session);
}));

export default router;
