import authMiddleware from '@/middlewares/auth.middleware';
import { PurchaseInfo, EventTicket, ActiveTicketSession } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';
import { Op } from 'sequelize';

const router = Router();

/**
 * Check ticket availability
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/check-ticket-availability', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;

	const { event_id, ticket_id, ticket_count } = req.body;
	
	if (!event_id || !ticket_id || !ticket_count) return res.forbidden('Event ID, Ticket ID and ticket count are required');
	if (isNaN(Number(ticket_count)) || Number(ticket_count) <= 0) return res.forbidden('Ticket count is not valid');

	const ticket = await EventTicket.findOne({
		where: {
			id: Number(ticket_id),
			event_id: Number(event_id),
		},
	});

	if (!ticket) return res.forbidden('Ticket not found');

	const soldTicketsCount = await PurchaseInfo.count({
		where: {
			event_id: Number(event_id),
			ticket_id: Number(ticket_id)
		}
	});

	if (soldTicketsCount >= ticket.number_of_tickets) {
		return res.forbidden('Sorry, this ticket is sold out.');
	}
	
	const now = dayjs().toDate();
	const activeSessions = await ActiveTicketSession.findAll({
		where: {
			event_id: Number(event_id),
			ticket_id: Number(ticket_id),
			end_time: { [Op.gt]: now },
		},
	});

	const lockedTicketsCount = activeSessions.reduce(
		(sum, session) => sum + session.ticket_count,
		0
	);

	const availableTicketsCount = ticket.number_of_tickets - soldTicketsCount - lockedTicketsCount;

	if (
		availableTicketsCount <= 0 &&
      soldTicketsCount < ticket.number_of_tickets
	) {
		return res.forbidden(
			'Sorry, No tickets are available right now. Some tickets are in buying session. You can check back later or try another ticket.'
		);
	}

	if (availableTicketsCount < Number(ticket_count)) {
		return res.forbidden(
			`Only ${availableTicketsCount} ticket(s) are available right now.`
		);
	}

	const ticketSession = await ActiveTicketSession.create({
		event_id: Number(event_id),
		user_id: Number(user?.id),
		ticket_id: Number(ticket_id),
		ticket_count: Number(ticket_count),
		start_time: dayjs().toDate(),
		end_time: dayjs().add(15, 'minute').toDate(),
	});

	return res.single(ticketSession);
}));

export default router;
