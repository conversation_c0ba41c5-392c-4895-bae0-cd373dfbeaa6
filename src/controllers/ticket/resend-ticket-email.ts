import { errorWrapper } from '@/utils/error-wrapper.util';
import dayjs from 'dayjs';
import { Router, type Request, type Response } from 'express';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { Event, EventTicket, PurchaseInfo, TicketBuyer, Venue } from '@/models';
import { generateTicketPdf } from '@/utils/ticket-pdf.util';
import { sendEmail, ticketEmail } from '@/utils/postmark.util';
import { userLog } from '@/utils/logger.util';
import { getClientIP } from '@/utils/general.util';

dayjs.extend(advancedFormat);

const router = Router();

/**
 * Resent ticket email to the buyer
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 */
router.post('/resend-ticket-email/:buyerId', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { buyerId } = req.params;
	if (isNaN(Number(buyerId))) return res.forbidden('Invalid request. Buyer id is required');

	const buyer = await TicketBuyer.findOne({
		where: {
			id: Number(buyerId),
		},
		include: [
			{
				model: PurchaseInfo,
				as: 'purchase_infos',
				required: true,
				include: [
					{
						model: Event,
						as: 'event',
						required: true,
						attributes: ['id', 'title', 'start_date_time', 'end_date_time'],
					},
					{
						model: Venue,
						as: 'venue',
						required: true,
						attributes: ['name'],
					},
					{
						model: EventTicket,
						as: 'event_ticket',
						required: true,
						attributes: ['name', 'price'],
					}
				]
			}
		]
	});
	if (!buyer) return res.notFound('Ticket details not found');

	const tickets = buyer.purchase_infos;

	const attachments: any[] = [];

	const event = tickets[0]?.event;
	const venue = tickets[0]?.venue;

	const formattedDateRange = event?.start_date_time && event?.end_date_time
		? `${dayjs(event.start_date_time).format('ddd, Do MMM YYYY hh:mm A')} - ${dayjs(event.end_date_time).format('ddd, Do MMM YYYY hh:mm A')}`
		: 'N/A';

	for (const ticket of tickets) {
		const ticketData = {
			ticketNo: ticket.id.toString(),
			eventId: ticket.event.id.toString(),
			eventName: event?.title || 'N/A',
			eventDate: formattedDateRange,
			eventLocation: venue?.name || 'N/A',
			userName: buyer.full_name || 'N/A',
			ticketType: ticket.event_ticket.name || 'N/A',
			price: ticket.event_ticket.price ? `NRs. ${ticket.event_ticket.price}` : 'N/A',
			issueDate: dayjs(ticket.createdAt).format('ddd, Do MMM YYYY hh:mm A'),
		};

		try {
			const pdfBuffer = await generateTicketPdf(ticketData);
			attachments.push({
				Content: pdfBuffer.toString('base64'),
				Name: `${ticket.event.title || 'Event'}_Ticket-${ticketData.ticketNo}.pdf`,
				ContentType: 'application/pdf',
				ContentID: `${ticket.event?.title || 'Event'}_Ticket-${ticketData.ticketNo}`,
			});
		} catch (error) {
			console.error(`Error generating ticket pdf for purchase info - ${ticket.id}`, error);
		}
	}

	const emailData = {
		eventName: event?.title || 'N/A',
		eventDate: formattedDateRange,
		eventLocation: venue?.name || 'N/A',
		userName: buyer.full_name || 'N/A',
		ticketCount: buyer.ticket_count || 'N/A',
		totalAmount: buyer.total_amount ? `NRs. ${buyer.total_amount}` : 'N/A',
	};

	await sendEmail(
		ticketEmail(
			buyer.email,
			emailData,
			attachments,
		)
	);

	userLog({
		event_type: 'TICKET',
		type: false,
		details: 'Resent ticket email successfully',
		action: 'TICKET-GENERATED',
		payloads: { ...req.params },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/resend-ticket-email/:id'
	});

	return res.success('Ticket has been sent to your email');
}));

export default router;
