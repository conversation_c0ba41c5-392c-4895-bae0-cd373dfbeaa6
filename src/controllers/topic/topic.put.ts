import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Topic } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateSlug, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Update Topic
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.put('/topics/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) return res.forbidden('Invalid request. Topic ID is required');
	
	const { title, order } = req.body;
	if (!title) return res.forbidden('Invalid data. Topic title is required');

	const topic = await Topic.findOne({ where: { id }});
	if (!topic) return res.forbidden('Topic with this ID does not exist');

	const slug = generateSlug(title);

	if (slug !== topic.slug) {
		const existTopic = await Topic.findOne({
			where: {
				slug,
			}
		});
		if (existTopic) return res.forbidden('This topic already exists');
	}

	await topic.update({
		title,
		slug: generateSlug(title),
		order: order || null,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'TOPIC',
		type: true,
		details: `Topic with ID "${topic.id}" updated successfully`,
		action: 'TOPIC-UPDATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PUT',
		api_endpoint: '/topics/:id'
	});

	return res.updated('Topic updated successfully');
}));

export default router;
