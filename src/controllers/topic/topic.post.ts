import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Topic } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateSlug, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Create Topic
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/topics', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { title, order } = req.body;
	if (!title) return res.forbidden('Invalid data. Topic title is required');

	const slug = generateSlug(title);

	const existTopic = await Topic.findOne({
		where: {
			slug,
		}
	});
	if (existTopic) return res.forbidden('This topic already exists');
	
	const topic = await Topic.create({
		title,
		slug,
		order: order || null,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'TOPIC',
		type: true,
		details: `Topic "${topic.title}" created successfully`,
		action: 'TOPIC-CREATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/topics'
	});

	return res.created(`Topic "${topic.title}" created successfully`);
}));

export default router;
