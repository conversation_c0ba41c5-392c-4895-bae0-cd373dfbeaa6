import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { TopicQas } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateSlug, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Create Topic QA
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/topics/qa', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { question, answer, topic_id, order } = req.body;
	if (!question || !answer || !topic_id) return res.forbidden('Invalid data. Question, answer and topic id are required');
	
	const slug = generateSlug(question);
	const existTopicQa = await TopicQas.findOne({
		where: {
			slug,
		}
	});
	if (existTopicQa) return res.forbidden('This question already exists');

	const topicQa = await TopicQas.create({
		topic_id,
		question,
		answer,
		slug,
		order: order || null,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'TOPIC',
		type: true,
		details: `Topic QA "${topicQa.question}" created successfully`,
		action: 'TOPIC-QA-CREATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/topics/qa'
	});

	return res.created('Topic QA created successfully');
}));

export default router;
