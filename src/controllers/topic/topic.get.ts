import { Topic, TopicQas } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Get Topic by id
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/topics/:id', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) return res.forbidden('Invalid request. Topic ID is required');

	const { getQas } = req.query;
	
	const topic = await Topic.findOne({
		where: { id },
		...(getQas === 'true' && {
			include: [
				{
					model: TopicQas,
					where: {
						status: true,
					},
					separate: true,
					order: [
						['order', 'ASC']
					],
				}
			]
		})
	});
	if (!topic) return res.forbidden('Topic does not exist');

	return res.single(topic);
}));

export default router;
