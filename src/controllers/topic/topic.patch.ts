import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Topic } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Activate or Deactivate Topic
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/topics/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'TOPIC',
			type: false,
			details: 'Invalid request. Topic ID is required',
			action: 'TOPIC-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/topics/:id'
		});
		return res.forbidden('Invalid request. Topic ID is required');
	}

	const { status } = req.body;
	if (typeof status !== 'boolean') {
		userLog({
			user_id: req.user?.id,
			event_type: 'TOPIC',
			type: false,
			details: 'Invalid request. Topic status is required and must be a boolean value',
			action: 'TOPIC-UPDATE',
			payloads: { ...req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/topics/:id'
		});
		return res.forbidden('Invalid request. Topic status is required and must be a boolean value');
	}

	const topic = await Topic.findOne({ where: { id }});
	if (!topic) return res.forbidden('Topic with this ID does not exist');

	await topic.update({ status });
	userLog({
		user_id: req.user?.id,
		event_type: 'TOPIC',
		type: true,
		details: `Topic with ID "${topic.id}" ${status ? 'activated' : 'deactivated'} successfully`,
		action: 'TOPIC-UPDATE',
		payloads: { ...req.params, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/topics/:id'
	});

	return res.success(`Topic "${topic.title}" ${status ? 'activated' : 'deactivated'} successfully`);
}));

export default router;
