import { TopicQas } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Update Topic QA View
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/topics/qa/view/:id', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'TOPIC',
			type: false,
			details: 'Invalid request. Topic QA ID is required',
			action: 'TOPIC-UPDATE',
			payloads: { ...req.params },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/topics/qa/:id'
		});
		return res.forbidden('Invalid request. Topic QA ID is required');
	}

	const topicQa = await TopicQas.findOne({ where: { id }});
	if (!topicQa) return res.forbidden('Topic QA with this ID does not exist');

	await topicQa.update({
		view: topicQa.view ? topicQa.view + 1 : 1,
	});
	userLog({
		user_id: req.user?.id,
		event_type: 'TOPIC',
		type: true,
		details: `View of Topic QA with ID "${topicQa.id}" increased successfully`,
		action: 'TOPIC-UPDATE',
		payloads: { ...req.params },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/topics/qa/view/:id'
	});

	return res.success();
}));

export default router;
