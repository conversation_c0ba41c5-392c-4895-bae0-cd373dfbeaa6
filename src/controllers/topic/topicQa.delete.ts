import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { TopicQas } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Delete Topic QA
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.delete('/topics/qa/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'TOPIC',
			type: false,
			details: 'Invalid request. Topic QA ID is required',
			action: 'TOPIC-QA-DELETE',
			payloads: { ...req.params },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'DELETE',
			api_endpoint: '/topics/qa/:id'
		});
		return res.forbidden('Invalid request. Topic QA ID is required');
	}

	const topicQa = await TopicQas.findOne({ where: { id }});
	if (!topicQa) return res.forbidden('Topic QA with this ID does not exist');

	await topicQa.destroy();

	userLog({
		user_id: req.user?.id,
		event_type: 'TOPIC',
		type: true,
		details: `Topic QA with ID "${topicQa.id}" deleted successfully`,
		action: 'TOPIC-QA-DELETE',
		payloads: { ...req.params },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'DELETE',
		api_endpoint: '/topics/qa/:id'
	});

	return res.success('Topic QA deleted successfully');
}));

export default router;
