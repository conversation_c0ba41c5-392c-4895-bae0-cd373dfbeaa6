import { Topic, TopicQas } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Get Topic Qa by slug
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/topics/qa/:slug', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { slug } = req.params;
	if (!slug) return res.forbidden('Invalid request. QA slug is required');

	const topicQa = await TopicQas.findOne({
		where: {
			slug,
			status: true,
		},
		include: [
			{
				model: Topic,
				attributes: ['title']
			}
		]
	});
	if (!topicQa) return res.forbidden('QA does not exist');

	return res.single(topicQa);
}));

export default router;
