import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { TopicQas } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateSlug, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Update Topic QA
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.put('/topics/qa/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) return res.forbidden('Invalid request. Topic QA ID is required');
	
	const { question, answer, topic_id, order } = req.body;
	if (!question || !answer || !topic_id) return res.forbidden('Invalid data. Question, answer and topic id are required');

	const topicQa = await TopicQas.findOne({ where: { id }});
	if (!topicQa) return res.forbidden('Topic QA with this ID does not exist');
	
	const slug = generateSlug(question);

	if (slug !== topicQa.slug) {
		const existTopicQa = await TopicQas.findOne({
			where: {
				slug,
			}
		});
		if (existTopicQa) return res.forbidden('This question already exists');
	}

	await topicQa.update({
		topic_id,
		question,
		answer,
		slug,
		order: order || null,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'TOPIC',
		type: true,
		details: `Topic QA with ID "${topicQa.id}" updated successfully`,
		action: 'TOPIC-QA-UPDATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PUT',
		api_endpoint: '/topics/qa/:id'
	});

	return res.updated('Topic QA updated successfully');
}));

export default router;
