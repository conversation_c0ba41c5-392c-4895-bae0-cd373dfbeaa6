import { Topic, TopicQas } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import { type Request, type Response, Router } from 'express';
import { FindAndCountOptions, Op, WhereOptions } from 'sequelize';
import sequelize from 'sequelize/lib/sequelize';

const router = Router();

/**
 * Get Topics
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/topics', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { type, search } = req.query;
	
	let topicStatus: boolean | null;
	if (type) {
		topicStatus = type === 'active' ? true : type === 'inactive' ? false : null;
	} else topicStatus = null;

	const topicQaWhere: WhereOptions<TopicQas> = {
		...(typeof topicStatus === 'boolean' ? { status: topicStatus } : {}),
		...(search ? { question: { [Op.iLike]: `%${search}%` }} : {})
	};
	
	const queryOptions: FindAndCountOptions<Topic> = {
		where: {
			...(typeof topicStatus === 'boolean' ? { status: topicStatus } : {})
		},
		order: [
			[
				sequelize.literal(`
						CASE 
							WHEN "Topic"."status" = true THEN 0 
							ELSE 1 
						END
					`),
				'ASC'
			],
			['order', 'ASC']
		],
		include: [
			{
				model: TopicQas,
				where: topicQaWhere,
				required: topicStatus === true,
				separate: true,
				order: [
					[
						sequelize.literal(`
            CASE 
              WHEN "TopicQas"."status" = true THEN 0 
              ELSE 1 
            END
          `),
						'ASC'
					],
					['order', 'ASC']
				],
				include: [
					{
						model: Topic,
						attributes: ['title']
					}
				]
			},
		],
	};
	
	const topics = await Topic.findAndCountAll({
		...queryOptions,
		...(paginated(req))
	});
	
	return res.paginated(topics, req);
}));

export default router;
