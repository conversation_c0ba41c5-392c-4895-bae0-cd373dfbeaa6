import { errorWrapper } from '@/utils/error-wrapper.util';
import { Router, type Request, type Response } from 'express';
import { Subscriber } from '@/models';
import { newsletterSubscriptionEmail, sendEmail } from '@/utils/postmark.util';
import { userLog } from '@/utils/logger.util';
import { getClientIP } from '@/utils/general.util';

const router = Router();

/**
 * Subscribe to newsletter
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 */
router.post('/subscribe', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { email } = req.body;

	const existingSubscription = await Subscriber.findOne({ where: { email }});
	if (existingSubscription) {
		userLog({
			user_id: req.user?.id,
			event_type: 'NEWSLETTER',
			type: false,
			details: 'You have already subscribed to our newsletter',
			action: 'NEWSLETTER-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/subscribe'
		});
		return res.success('You have already subscribed to our newsletter');
	}

	await Subscriber.create({
		email,
		status: true,
	});

	try {
		await sendEmail(
			newsletterSubscriptionEmail(email)
		);
	} catch (error) {
		userLog({
			user_id: req.user?.id,
			event_type: 'NEWSLETTER',
			type: false,
			details: 'Error sending newsletter subscription email',
			action: 'NEWSLETTER-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/subscribe'
		});
		console.error('Error sending newsletter subscription email:', error);
	}
	userLog({
		user_id: req.user?.id,
		event_type: 'NEWSLETTER',
		type: true,
		details: 'Subscribed to newsletter successfully',
		action: 'NEWSLETTER-CREATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/subscribe'
	});
	return res.success('Subscribed to newsletter successfully');
}));

export default router;
