import sequelize from '@/configs/database.config';
import authMiddleware from '@/middlewares/auth.middleware';
import { Artist } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import { type Request, type Response, Router } from 'express';
import { Op, type FindAndCountOptions } from 'sequelize';

const router = Router();

/**
 * Get Artists
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/artists', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { type, search } = req.query;

	let artistStatus: boolean | null;
	if (type) {
		artistStatus = type === 'active' ? true : type === 'inactive' ? false : null;
	} else artistStatus = null;

	const queryOptions: FindAndCountOptions<Artist> = {
		where: {
			...(typeof artistStatus === 'boolean' ? { status: artistStatus } : {}),
			...(search && typeof search === 'string'
				? {
					[Op.or]: [
						{ name: { [Op.iLike]: `%${search.replace(/\s+/g, '%')}%` }},
						{ stage_name: { [Op.iLike]: `%${search.replace(/\s+/g, '%')}%` }},
						{ band_name: { [Op.iLike]: `%${search.replace(/\s+/g, '%')}%` }}
					]
				}
				: {})
		},
		order: [
			[
				sequelize.literal(`
					CASE 
						WHEN "Artist"."status" = true THEN 0 
						ELSE 1 
					END
				`),
				'ASC'
			],
			['id', 'ASC']
		]
	};

	const artists = await Artist.findAndCountAll({
		...queryOptions,
		...(paginated(req))
	});

	return res.paginated(artists, req);
}));

export default router;
