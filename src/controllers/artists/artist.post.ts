import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Artist } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Create Artist
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/artists', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const {
		name,
		title,
		stage_name,
		band_name,
		image,
		dob,
		home_town,
		about,
		meta_keywords,
		meta_desc
	} = req.body;

	if (!name || !title || !about) {
		userLog({
			user_id: req.user?.id,
			event_type: 'ARTIST',
			type: true,
			details: 'Artist updated successfully',
			action: 'ARTIST-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/artists/:id'
		});
		
		return res.forbidden('Invalid data. Artist name, title and about are required');

	}

	const existingArtist = await Artist.findOne({ where: { name }});
	if (existingArtist) {
		userLog({
			user_id: req.user?.id,
			event_type: 'ARTIST',
			type: true,
			details: `Artist "${name}" already exists`,
			action: 'ARTIST-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/artists/:id'
		});
		return res.forbidden(`Artist "${name}" already exists`);
	}

	await Artist.create({
		name,
		title,
		stage_name: stage_name || null,
		band_name: band_name || null,
		image: image || null,
		dob: dob || null,
		home_town: home_town || null,
		about,
		status: true,
		meta_desc: meta_desc || null,
		meta_keywords: meta_keywords ? meta_keywords.join(',') : null,
	});

	return res.created(`Artist ${name} created successfully`);
}));

export default router;
