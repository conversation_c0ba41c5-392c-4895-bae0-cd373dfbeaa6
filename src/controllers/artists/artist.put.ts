import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Artist } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Edit Artist
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.put('/artists/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'ARTIST',
			type: false,
			details: 'Invalid request. Artist ID is required',
			action: 'ARTIST-UPDATE',
			payloads: { id: id },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/artists/:id'
		});
		return res.forbidden('Invalid request. Artist ID is required');
	}

	const {
		name,
		title,
		stage_name,
		band_name,
		image,
		dob,
		home_town,
		about,
		meta_desc,
		meta_keywords
	} = req.body;

	if (!name || !title || !about) {
		userLog({
			user_id: req.user?.id,
			event_type: 'ARTIST',
			type: false,
			details: 'Invalid data. Artist name, title and about are required',
			action: 'ARTIST-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/artists/:id'
		});
		return res.forbidden('Invalid data. Artist name, title and about are required');
	}
	
	const existingArtist = await Artist.findOne({
		where: { id },
	});
	if (!existingArtist) return res.forbidden('Artist does not exist');

	await existingArtist.update({
		name,
		title,
		stage_name: stage_name || null,
		band_name: band_name || null,
		image: image || null,
		dob: dob || null,
		home_town: home_town || null,
		about,
		meta_desc: meta_desc || null,
		meta_keywords: meta_keywords ? meta_keywords.join(',') : null,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'ARTIST',
		type: true,
		details: 'Artist updated successfully',
		action: 'ARTIST-UPDATE',
		payloads: { id: id, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PUT',
		api_endpoint: '/artists/:id'
	});

	return res.success('Artist updated successfully');
}));

export default router;
