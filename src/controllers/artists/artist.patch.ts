import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Artist, Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Activate or Deactivate Artist
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/artists/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'ARTIST',
			type: false,
			details: 'Invalid request. Artist ID is required',
			action: 'ARTIST-UPDATE',
			payloads: { id: id },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/artists/:id'
		});
		return res.forbidden('Invalid request. Artist ID is required');
	}

	const { status } = req.body;
	if (typeof status !== 'boolean') {
		userLog({
			user_id: req.user?.id,
			event_type: 'ARTIST',
			type: false,
			details: 'Invalid request. Artist status is required and must be a boolean value',
			action: 'ARTIST-UPDATE',
			payloads: { id: id, status: status },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/artists/:id'
		});
		return res.forbidden('Invalid request. Artist status is required and must be a boolean value');
	}

	const existingArtist = await Artist.findOne({ where: { id }});
	if (!existingArtist) return res.forbidden('Artist does not exist');

	// Check if Artist is being used by any events if it is being deactivated
	if (!status) {
		const event = await Event.findOne({
			include: [
				{
					model: Artist,
					where: { id: Number(id) }
				}
			]
		});
		if (event) {
			userLog({
				user_id: req.user?.id,
				event_type: 'ARTIST',
				type: false,
				details: 'Artist is being used by some events and cannot be deactivated',
				action: 'ARTIST-UPDATE',
				payloads: { id: id, status: status },
				ip_address: getClientIP(req),
				user_agent: 'web',
				read_access: true,
				referrer: null,
				method: 'PATCH',
				api_endpoint: '/artists/:id'
			});
			return res.forbidden('Artist is being used by some events and cannot be deactivated');
		}
	}

	await existingArtist.update({ status });
	userLog({
		user_id: req.user?.id,
		event_type: 'ARTIST',
		type: true,
		details: `Artist ${status ? 'activated' : 'deactivated'} successfully`,
		action: 'ARTIST-UPDATE',
		payloads: { id: id, status: status },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/artists/:id'
	});
	return res.success(`Artist ${status ? 'activated' : 'deactivated'} successfully`);
}));

export default router;
