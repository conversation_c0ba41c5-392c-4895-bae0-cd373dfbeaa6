import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Artist, Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Delete Artist
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.delete('/artists/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'ARTIST',
			type: false,
			details: 'Invalid request. Artist ID is required',
			action: 'ARTIST-DELETE',
			payloads: { id: id },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'DELETE',
			api_endpoint: '/artists/:id'
		});
		return res.forbidden('Invalid request. Artist ID is required');
	}

	const existingArtist = await Artist.findOne({ where: { id }});
	if (!existingArtist) return res.forbidden('Artist does not exist');

	// Check if artist is being used by any events
	const event = await Event.findOne({
		include: [
			{
				model: Artist,
				where: { id: Number(id) }
			}
		]
	});
	if (event) {
		userLog({
			user_id: req.user?.id,
			event_type: 'ARTIST',
			type: false,
			details: 'Artist is being used by some events and cannot be deleted',
			action: 'ARTIST-DELETE',
			payloads: { id: id },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'DELETE',
			api_endpoint: '/artists/:id'
		});
		return res.forbidden('Artist is being used by some events and cannot be deleted');
	}

	await existingArtist.destroy();

	userLog({
		user_id: req.user?.id,
		event_type: 'ARTIST',
		type: true,
		details: 'Artist deleted successfully',
		action: 'ARTIST-DELETE',
		payloads: { id: id },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'DELETE',
		api_endpoint: '/artists/:id'
	});
	return res.success('Artist deleted successfully');
}));

export default router;
