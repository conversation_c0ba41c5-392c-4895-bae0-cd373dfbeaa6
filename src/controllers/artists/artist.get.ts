import { Artist, Event, Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';
import { Op } from 'sequelize';

const router = Router();

/**
 * Get Artist by id
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/artists/:id', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) return res.forbidden('Invalid request. Artist ID is required');

	const now = dayjs();

	const existingArtist = await Artist.findOne({
		where: { id },
		include: [
			{
				model: Event,
				where: {
					published_at: { [Op.ne]: null },
					approved_at: { [Op.ne]: null },
					end_date_time: { [Op.gte]: now.toDate() },
					cancelled_at: null,
					deleted_at: null,
				},
				attributes: ['id', 'title', 'image', 'slug', 'start_date_time'],
				through: { attributes: []},
				include: [{
					model: Venue,
					attributes: ['id', 'name'],
					through: { attributes: []},
				}],
				required: false,
			}
		]
	});
	if (!existingArtist) return res.forbidden('Artist does not exist');

	if (existingArtist.events?.length > 5) {
		existingArtist.events = existingArtist.events.slice(0, 4);
	}

	return res.single(existingArtist);
}));

export default router;
