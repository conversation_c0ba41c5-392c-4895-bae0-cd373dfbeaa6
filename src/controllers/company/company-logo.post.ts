import { errorWrapper } from '@/utils/error-wrapper.util';
import { Router, type Request, type Response } from 'express';
import authMiddleware from '@/middlewares/auth.middleware';
import { Company, User } from '@/models';
import { userLog } from '@/utils/logger.util';
import { getClientIP } from '@/utils/general.util';

const router = Router();

/**
 * Update company logo
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 */
router.post('/company-logo', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) {
		userLog({
			user_id: req.user?.id,
			event_type: 'COMPANY',
			type: false,
			details: 'Invalid request',
			action: 'COMPANY-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/company-logo'
		});
		return res.forbidden('Invalid request');
	}

	const { logo, companyId } = req.body;
	if (!logo || !companyId) return res.forbidden('Invalid request. Image and company id are required');

	const company = await Company.findOne({
		where: { id: companyId },
		include: {
			model: User,
			where: { id: user.id },
			required: true
		}
	});
	if (!company) return res.forbidden('Invalid request. Company not associated with user.');

	await company.update({ logo });
	userLog({
		user_id: req.user?.id,
		event_type: 'COMPANY',
		type: true,
		details: 'Company logo updated successfully',
		action: 'COMPANY-UPDATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/company-logo'
	});
	return res.success('Company logo updated successfully');
}));

export default router;
