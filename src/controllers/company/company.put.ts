import { errorWrapper } from '@/utils/error-wrapper.util';
import { Router, type Request, type Response } from 'express';
import authMiddleware from '@/middlewares/auth.middleware';
import { Company, User } from '@/models';
import { userLog } from '@/utils/logger.util';
import { getClientIP } from '@/utils/general.util';

const router = Router();

/**
 * Update company info
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 */
router.put('/company/:id', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) {
		userLog({
			user_id: req.user?.id,
			event_type: 'COMPANY',
			type: false,
			details: 'Invalid request',
			action: 'COMPANY-UPDATE',
			payloads: { id: req.params, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/company/:id'
		});
		return res.forbidden('Invalid request');
	}

	const { id } = req.params;
	if (!id) return res.forbidden('Company id is required.');

	const company = await Company.findOne({
		where: { id },
		include: {
			model: User,
			where: { id: user.id },
			required: true,
			attributes: [],
		},
		attributes: ['id', 'name', 'email', 'phone', 'about', 'address', 'logo', 'domain'],
	});
	if (!company) return res.forbidden('Invalid request. Company not associated with user.');

	const { name, domain, email, phone, address, about, logo } = req.body;

	await company.update({
		name,
		domain,
		email,
		phone,
		address,
		about,
		logo,
	});

	userLog({
		user_id: req.user?.id,
		event_type: 'COMPANY',
		type: false,
		details: 'Company info updated successfully',
		action: 'COMPANY-UPDATE',
		payloads: { id: req.params, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PUT',
		api_endpoint: '/company/:id'
	});

	return res.updated('Company info updated successfully', company);
}));

export default router;
