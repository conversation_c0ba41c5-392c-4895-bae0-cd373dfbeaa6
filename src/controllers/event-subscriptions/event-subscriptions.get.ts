import authMiddleware from '@/middlewares/auth.middleware';
import { Event, EventSubscription } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Get Subscribed Events for a User
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/event-subscriptions', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) return res.forbidden('Unauthorized access. User is required');

	const eventSubscriptions = await EventSubscription.findAndCountAll({
		where: {
			user_id: user.id,
		},
		include: [
			{
				model: Event,
				attributes: ['id', 'title', 'image', 'slug', 'about'],
				required: true,
			}
		],
		order: [['created_at', 'DESC']],
		...(paginated(req))
	});

	return res.paginated(eventSubscriptions, req);
}));

export default router;
