import authMiddleware from '@/middlewares/auth.middleware';
import { Event, EventSubscription } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Subscribe or Unsubscribe Event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/event-subscriptions/:id', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Invalid request. Event ID is required',
			action: 'EVENT-SUBSCRIPTION-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/event-subscriptions/:id'
		});
		return res.forbidden('Invalid request. Event ID is required');
	}
	const { user } = req;
	if (!user) {
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Unauthorized Request',
			action: 'EVENT-SUBSCRIPTION-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/event-subscriptions/:id'
		});
		return res.forbidden('Unauthorized Request');
	}

	const { subscribe } = req.body;
	if (typeof subscribe !== 'boolean'){
		userLog({
			user_id: req.user?.id,
			event_type: 'EVENT',
			type: false,
			details: 'Invalid data type. Please try again',
			action: 'EVENT-SUBSCRIPTION-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/event-subscriptions/:id'
		});
		return res.forbidden('Invalid data type. Please try again');
	}

	const existingEvent = await Event.findOne({ where: { id }});
	if (!existingEvent) return res.forbidden('Event does not exist');

	if (subscribe) {
		await EventSubscription.create({ event_id: existingEvent.id, user_id: user.id });
	} else await EventSubscription.destroy({ where: { event_id: existingEvent.id, user_id: user.id }});
	userLog({
		user_id: req.user?.id,
		event_type: 'EVENT',
		type: true,
		details: 'Event subscribed updated',
		action: 'EVENT-SUBSCRIPTION-UPDATE',
		payloads: { id: id, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/event-subscriptions/:id'
	});
	return res.updated();
}));

export default router;
