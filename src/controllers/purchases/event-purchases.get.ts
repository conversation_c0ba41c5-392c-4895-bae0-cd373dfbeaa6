import authMiddleware from '@/middlewares/auth.middleware';
import { TicketBuyer, Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Get Purchase List of an event
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/event-purchases/:id', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user || (user.user_type === 'normal' && !user.is_partner)) return res.forbidden('Unauthorized Request');

	const { id } = req.params;
	if (isNaN(Number(id))) return res.forbidden('Invalid request. Provide a valid event ID.');

	const event = await Event.findOne({
		where: {
			id,
		}
	});

	if (!event) return res.notFound('Event not found');

	if (user.is_partner && (event.company_id !== req.company?.id)) {
		return res.forbidden('Unauthorized Request');
	}

	const purchases = await TicketBuyer.findAndCountAll({
		where: {
			event_id: Number(id),
		},
		...(paginated(req)),
	});

	return res.paginated(purchases, req);
}));

export default router;
