import authMiddleware from '@/middlewares/auth.middleware';
import { TicketBuyer, Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';
import ExcelJS from 'exceljs';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';

dayjs.extend(advancedFormat);

const router = Router();

/**
 * Get Purchase List of an event in excel
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/event-purchases/excel/:id', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user || (user.user_type === 'normal' && !user.is_partner)) return res.forbidden('Unauthorized Request');

	const { id } = req.params;
	if (isNaN(Number(id))) return res.forbidden('Invalid request. Provide a valid event ID.');

	const event = await Event.findOne({
		where: {
			id,
		}
	});

	if (!event) return res.notFound('Event not found');

	if (user.is_partner && (event.company_id !== req.company?.id)) {
		return res.forbidden('Unauthorized Request');
	}

	const ticketBuyers = await TicketBuyer.findAll({
		where: {
			event_id: Number(id),
		},
	});

	const totalTickets = ticketBuyers.reduce((acc, curr) => acc + (curr.ticket_count || 0), 0);
	const totalAmount = ticketBuyers.reduce((acc, curr) => acc + Number(curr.total_amount || 0), 0);

	const workbook = new ExcelJS.Workbook();
	const worksheet = workbook.addWorksheet('Event Purchases Report', { properties: { tabColor: { argb: '00B050' }}, views: [{ state: 'frozen', ySplit: 1 }]});

	worksheet.columns = [
		{ header: '#', key: 'index', width: 10 },
		{ header: 'Buyer Name', key: 'buyer_name', width: 30 },
		{ header: 'Buyer Phone', key: 'buyer_phone', width: 20 },
		{ header: 'Buyer Email', key: 'buyer_email', width: 30 },
		{ header: 'Payment Method', key: 'payment_method', width: 17 },
		{ header: 'Ticket Count', key: 'ticket_count', width: 15 },
		{ header: 'Paid Amount', key: 'paid_amount', width: 20 },
		{ header: 'Bought At', key: 'bought_at', width: 25 },
	];
	worksheet.getRow(1).font = { bold: true };

	ticketBuyers.forEach((buyer, index) => {
		worksheet.addRow({
			index: (index + 1).toString(),
			buyer_name: buyer.full_name || 'N/A',
			buyer_phone: buyer.phone_number || 'N/A',
			buyer_email: buyer.email || 'N/A',
			payment_method: buyer.payment_method || 'N/A',
			ticket_count: buyer.ticket_count?.toString() || 'N/A',
			paid_amount: buyer.total_amount ? `NRs. ${buyer.total_amount}` : 'N/A',
			bought_at: buyer.created_at ? dayjs(buyer.created_at).format('Do MMM YYYY h:mm A') : 'N/A',
		});
	});

	worksheet.addRow({});

	const totalTicketsRow = worksheet.addRow({
		index: '',
		buyer_name: 'Total Tickets Sold',
		buyer_phone: totalTickets.toString(),
		buyer_email: '',
		payment_method: '',
		ticket_count: '',
		paid_amount: '',
		bought_at: '',
	});
	totalTicketsRow.font = { bold: true };

	const totalAmountRow = worksheet.addRow({
		index: '',
		buyer_name: 'Total Amount',
		buyer_phone: `NRs. ${totalAmount}`,
		buyer_email: '',
		payment_method: '',
		ticket_count: '',
		paid_amount: '',
		bought_at: '',
	});
	totalAmountRow.font = { bold: true };

	const buffer = await workbook.xlsx.writeBuffer();

	res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset-UTF-8');

	return res.file(Buffer.from(buffer), `${event.title} Purchases Report`, 'xlsx');
}));

export default router;
