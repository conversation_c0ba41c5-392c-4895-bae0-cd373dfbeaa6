import { Company, User } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';
import bcrypt from 'bcrypt';
import { userLog } from '@/utils/logger.util';
import { getClientIP } from '@/utils/general.util';
import authMiddleware from '@/middlewares/auth.middleware';

const router = Router();

/**
 * Change Password
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/change-password', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { user } = req;
	if (!user) {
		userLog({
			user_id: req.user?.id,
			event_type: 'AUTH',
			type: false,
			details: 'Invalid request',
			action: 'AUTH-PASSWORD-CHANGE',
			payloads: { email: req.body.email },
			ip_address: getClient<PERSON>(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/change-password'
		});
		return res.forbidden('Invalid request');
	}

	const { old_password, new_password, confirm_password, email } = req.body;
	if (!old_password || !new_password || !confirm_password) return res.forbidden('Invalid request. Required fields are missing');

	const existingUser = await User.findOne({
		where: {
			email,
			id: user.id,
		},
		include: [
			{
				model: Company,
				where: { status: true },
				attributes: ['id', 'name', 'email', 'phone', 'about', 'address', 'logo', 'domain'],
				required: false,
			}
		]
	});

	if (!existingUser) {
		userLog({
			user_id: req.user?.id,
			event_type: 'AUTH',
			type: false,
			details: 'User not found',
			action: 'AUTH-PASSWORD-CHANGE',
			payloads: { email: email },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/change-password'
		});
		return res.forbidden('Invalid request. User not found');
	}

	const isPasswordValid = await bcrypt.compare(old_password, existingUser.password);

	if (!isPasswordValid) return res.invalid('Old Password is incorrect');

	if (new_password !== confirm_password) return res.forbidden('New Passwords do not match');
	const hashedPassword = await bcrypt.hash(new_password, 10);

	await existingUser.update({
		password: hashedPassword,
		verification_code: null,
		action_link_sent_at: null,
		password_updated_at: new Date(),
	});
	userLog({
		event_type: 'AUTH',
		type: true,
		details: 'Password changed successfully',
		action: 'AUTH-PASSWORD-CHANGE',
		payloads: { email: email },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/change-password'
	});
	return res.success('Password changed successfully', existingUser!.toJSON());
}));

export default router;
