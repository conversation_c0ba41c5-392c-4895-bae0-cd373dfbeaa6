import { Company, User } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import envUtil from '@/utils/env.util';
import { userLog } from '@/utils/logger.util';
import { getClientIP } from '@/utils/general.util';
// import Cookies from 'cookies';

const router = Router();

/**
 * User login
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/login/user', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { email, password } = req.body;
	if (!email || !password) return res.forbidden('Missing required fields');

	const user = await User.findOne({
		where: {
			email,
		},
		include: [
			{
				model: Company,
				where: { status: true },
				attributes: ['id', 'name', 'email', 'phone', 'about', 'address', 'logo', 'domain'],
				required: false,
			}
		]
	});

	if (!user) {

		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'User not found. Invalid email',
			action: 'AUTH-LOGIN',
			payloads: { email },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/login/user'
		});
	
		return res.invalid('User not found. Invalid email');
	}

	if (!user.email_verified_at) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Email not verified. Please verify your email',
			action: 'AUTH-LOGIN',
			payloads: { email },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/login/user'
		});
		return res.failure('Email not verified. Please verify your email');
	}

	const isPasswordValid = await bcrypt.compare(password, user.password);
	if (!isPasswordValid) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Invalid password',
			action: 'AUTH-LOGIN',
			payloads: { email },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/login/user'
		});
		return res.invalid('Invalid password');
	}

	if (req.headers['x-platform'] === 'mobile' && !(user.is_partner || user.user_type === 'admin')) {
		return res.invalid('Invalid Request. Normal users cannot login from mobile app');
	}

	await user.update({
		last_logged_in_at: new Date(),
	});
	
	const jwtData = {
		id: user.id,
		email: user.email,
		is_partner: user.is_partner,
		user_type: user.user_type,
		...(user.companies.length > 0 && { companies: user.companies.map((company) => ({ id: company.id })) }),
	};

	const accessToken = jwt.sign(
		jwtData,
		envUtil.JWT_SALT,
		{ expiresIn: '30d' }
	);
	const refreshToken = jwt.sign(
		jwtData,
		envUtil.JWT_SALT,
		{ expiresIn: '30d' }
	);
	userLog({
		user_id: user.id,
		// company_id: 1,
		event_type: 'AUTH',
		type: true,
		details: 'User logged in successfully',
		action: 'AUTH-LOGIN',
		payloads: { email },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/login/user'
	});

	return res.success(
		'Logged in successfully',
		{
			tokens: {
				accessToken,
				refreshToken,
			},
			userInfo: user.toJSON(),
		}
	);
}));

export default router;
