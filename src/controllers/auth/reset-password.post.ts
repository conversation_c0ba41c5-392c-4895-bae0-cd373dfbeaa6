import { User } from '@/models';
import envUtil from '@/utils/env.util';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import bcrypt from 'bcrypt';
import { Router, type Request, type Response } from 'express';
import { Op } from 'sequelize';

const router = Router();

/**
 * Reset Password
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/reset-password', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { email, code, password, password_confirmation } = req.body;
	if (!email || !code || !password || !password_confirmation) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Invalid request. Missing required information',
			action: 'AUTH-RESET-PASSWORD',
			payloads: { email: email, code: code },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/reset-password'
		});
		return res.forbidden('Invalid request. Missing required information');
	}

	if (password !== password_confirmation){
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Passwords do not match',
			action: 'AUTH-RESET-PASSWORD',
			payloads: { email: email, code: code },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/reset-password'
		});
		return res.forbidden('Passwords do not match');
	}

	const user = await User.findOne({
		where: {
			email,
			email_verified_at: { [Op.ne]: null },
		}
	});

	if (!user) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'User not found. Invalid email',
			action: 'AUTH-RESET-PASSWORD',
			payloads: { email: email, code: code },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/reset-password'
		});
		return res.invalid('User not found. Invalid email');
	}
	if (!user.action_link_sent_at || !user.verification_code) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Invalid request. Please request a new link',
			action: 'AUTH-RESET-PASSWORD',
			payloads: { email: email, code: code },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/reset-password'
		});
		return res.invalid('Invalid request. Please request a new link');
	}

	const timeDifference = (new Date().getTime()) - (new Date(user.action_link_sent_at).getTime());

	if (timeDifference > envUtil.LINK_VERIFICATION_EXPIRY) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Password reset link is expired. Please request a new link',
			action: 'AUTH-RESET-PASSWORD',
			payloads: { email: email, code: code },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/reset-password'
		});
		return res.forbidden('Password reset link is expired. Please request a new link');
	}

	if (user.verification_code !== code) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Invalid verification code',
			action: 'AUTH-RESET-PASSWORD',
			payloads: { email: email, code: code },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/reset-password'
		});
		return res.invalid('Invalid verification code');
	}

	const hashedPassword = await bcrypt.hash(password_confirmation, 10);

	await user.update({
		password: hashedPassword,
		verification_code: null,
		action_link_sent_at: null,
	});

	userLog({
		user_id: user.id,
		event_type: 'AUTH',
		type: true,
		details: 'Password reset was successful. You can now login with your new password.',
		action: 'AUTH-RESET-PASSWORD',
		payloads: { email: email, code: code },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/reset-password'
	});
	
	return res.success('Password reset was successful. You can now login with your new password.');
}));

export default router;
