import { Company, User } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';
import jwt from 'jsonwebtoken';
import envUtil from '@/utils/env.util';
import axios from 'axios';
import { userLog } from '@/utils/logger.util';
import { getClientIP } from '@/utils/general.util';

const router = Router();

/**
 * User login with google or facebook
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/login/user/provider', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { accessToken: providerAccessToken, provider } = req.body;

	if (!provider || !providerAccessToken) return res.forbidden('Missing required information');

	let userData;
	if (provider === 'google') {
		const url = 'https://www.googleapis.com/oauth2/v1/userinfo';
		const googleUser = await axios.get(url, {
			headers: {
				Authorization: `Bearer ${providerAccessToken}`,
				Accept: 'application/json'
			}
		});
		userData = googleUser.data;
	} else if (provider === 'facebook') {
		const url = 'https://graph.facebook.com/me';
		const facebookUser = await axios.get(url, {
			params: {
				fields: 'id,name,email,picture'
			},
			headers: {
				Authorization: `Bearer ${providerAccessToken}`,
				Accept: 'application/json'
			}
		});
		// const url = `https://graph.facebook.com/${providerAccountId}?fields=id,name,email,picture&access_token=${providerAccessToken}`;
		// const facebookUser = await axios.get(url);
		userData = facebookUser.data;
	} else {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Invalid provider',
			action: provider === 'google' ? 'AUTH-GOOGLE-LOGIN' : 'AUTH-FB-LOGIN',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/login/user/provider'
		});
		return res.forbidden('Invalid provider');
	}
	console.log('User data from provider:', userData);
	const user = await User.findOne({
		where: {
			email: userData.email,
		},
		include: [
			{
				model: Company,
				where: { status: true },
				attributes: ['id', 'name', 'email', 'phone', 'about', 'address', 'logo', 'domain'],
				required: false,
			}
		]
	});

	if (!user) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'User not found. No account has been registered with the provided email',
			action: provider === 'google' ? 'AUTH-GOOGLE-LOGIN' : 'AUTH-FB-LOGIN',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/login/user/provider'
		});
		return res.invalid('User not found. No account has been registered with the provided email');
	}

	await user.update({
		last_logged_in_at: new Date(),
	});
	
	if (provider === 'google') {
		await user.update({
			email_verified_at: user.email_verified_at || new Date(),
			f_name: user.f_name || userData?.given_name,
			l_name: user.l_name || userData?.family_name,
			verification_code: null,
			action_link_sent_at: null,
		});
	} else if (provider === 'facebook') {
		const nameParts = userData.name.split(' ');

		await user.update({
			email_verified_at: user.email_verified_at || new Date(),
			f_name: user.f_name || nameParts[0],
			l_name: user.l_name || nameParts.slice(1).join(' ').trim(),
			verification_code: null,
			action_link_sent_at: null,
		});
	}

	const jwtData = {
		id: user.id,
		email: user.email,
		is_partner: user.is_partner,
		user_type: user.user_type,
		...(user.companies.length > 0 && { companies: user.companies.map((company) => ({ id: company.id })) }),
	};

	const accessToken = jwt.sign(
		jwtData,
		envUtil.JWT_SALT,
		{ expiresIn: '1d' }
	);
	const refreshToken = jwt.sign(
		jwtData,
		envUtil.JWT_SALT,
		{ expiresIn: '7d' }
	);
	userLog({
		event_type: 'AUTH',
		type: true,
		details: 'Logged in successfully',
		action: provider === 'google' ? 'AUTH-GOOGLE-LOGIN' : 'AUTH-FB-LOGIN',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/login/user/provider'
	});

	return res.success(
		'Logged in successfully',
		{
			tokens: {
				accessToken,
				refreshToken,
			},
			userInfo: user.toJSON(),
		}
	);
}));

export default router;
