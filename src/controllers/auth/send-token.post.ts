import authMiddleware from '@/middlewares/auth.middleware';
import { User } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateRandomNumber, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { sendEmail, emailChangeEmail } from '@/utils/postmark.util';
import { Router, type Request, type Response } from 'express';

const router = Router();

/**
 * Send Token to user for email change
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/token', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	if (!req.user) return res.forbidden('Invalid request');

	const { old_email, new_email } = req.body;
	if (!old_email || !new_email) return res.forbidden('Invalid request. Required fields are missing');

	const user = await User.findOne({
		where: {
			email: old_email,
			id: req.user.id,
		},
	});

	if (!user) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'User not found. Invalid email',
			action: 'AUTH-SEND-VERIFICATION-TOKEN',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/token'
		});
		return res.invalid('User not found. Invalid email');
	}

	const verificationCode = generateRandomNumber(111111, 999999);

	await user.update({
		verification_code: verificationCode,
		action_link_sent_at: new Date(),
	});

	await sendEmail(emailChangeEmail(
		new_email,
		old_email,
		verificationCode.toString(),
	));
	
	userLog({
		event_type: 'AUTH',
		type: false,
		details: 'Verification Token sent successfully',
		action: 'AUTH-SEND-VERIFICATION-TOKEN',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/token'
	});

	return res.success('Verification Code has been sent successfully');
}));

export default router;
