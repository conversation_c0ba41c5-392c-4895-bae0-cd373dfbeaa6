import { User } from '@/models';
import envUtil from '@/utils/env.util';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { Router, type Request, type Response } from 'express';

const router = Router();

/**
 * Verify User Email
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/verify/user', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { verification_code, email } = req.body;
	if (!verification_code || !email) return res.forbidden('Missing required fields');

	const user = await User.findOne({
		where: {
			email,
		}
	});

	if (!user) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'User not found. Invalid email',
			action: 'AUTH-VERIFY-EMAIL',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/verify/user'
		});
	
		return res.invalid('User not found. Invalid email');
	}

	if (user.email_verified_at) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Email has already been verified.',
			action: 'AUTH-VERIFY-EMAIL',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/verify/user'
		});
		return res.success('Email has already been verified.');
	}

	const timeDifference = (new Date().getTime()) - (new Date(user.action_link_sent_at).getTime());
	if (timeDifference > envUtil.LINK_VERIFICATION_EXPIRY) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Link expired. Please request a new link',
			action: 'AUTH-VERIFY-EMAIL',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/verify/user'
		});
		return res.invalid('Link expired. Please request a new link');
	}
	if (user.verification_code !== verification_code) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Invalid verification code',
			action: 'AUTH-VERIFY-EMAIL',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/verify/user'
		});
		return res.invalid('Invalid verification code');
	}

	await user.update({
		email_verified_at: new Date(),
		verification_code: null,
		action_link_sent_at: null,
	});
	
	userLog({
		user_id: user.id,
		event_type: 'AUTH',
		type: true,
		details: 'Email verified successfully',
		action: 'AUTH-VERIFY-EMAIL',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/verify/user'
	});

	return res.success('Email verified successfully');
}));

export default router;
