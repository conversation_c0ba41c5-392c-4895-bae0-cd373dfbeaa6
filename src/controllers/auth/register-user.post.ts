import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';
import { User } from '@/models';
import bcrypt from 'bcrypt';
import { sendEmail, registrationVerificationEmail } from '@/utils/postmark.util';
import envUtil from '@/utils/env.util';
import { generateRandomNumber, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import sequelize from '@/configs/database.config';

const router = Router();

/**
 * User Registration
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/register/user', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { credential, password, password_confirmation, agreement } = req.body;

	if (!credential || !password || !password_confirmation || !agreement) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Missing required fields',
			action: 'AUTH-REGISTER-USER',
			payloads: { credential: credential, agreement },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/register/user'
		});
		return res.forbidden('Missing required fields');
	}

	if (password !== password_confirmation) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Passwords do not match',
			action: 'AUTH-REGISTER-USER',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/register/user'
		});
		return res.invalid('Passwords do not match');
	}

	const phoneRegex = /^[0-9]{10}$/;
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

	const whereClause: { email?: string, phone?: string } = {};
	let credentialType = '';

	if (phoneRegex.test(credential)) {
		whereClause.phone = credential;
		credentialType = 'Phone';
	}
	else if (emailRegex.test(credential)){
		whereClause.email = credential;
		credentialType = 'Email';
	}
	else {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Credential must be a valid email or phone number',
			action: 'AUTH-REGISTER-USER',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/register/user'
		});
		return res.invalid('Credential must be a valid email or phone number');
	}

	const userExists = await User.findOne({
		where: whereClause,
	});

	if (userExists) return res.forbidden(`${credentialType} is already taken`);

	const hashedPassword = await bcrypt.hash(password_confirmation, 10);
	const verificationCode = generateRandomNumber(111111, 999999);

	const transaction = await sequelize.transaction();

	try {
		await User.create({
			email: credentialType === 'Email' ? credential : null,
			phone: credentialType === 'Phone' ? credential : null,
			password: hashedPassword,
			verification_code: verificationCode,
			action_link_sent_at: new Date(),
		}, { transaction });

		if (credentialType === 'Email') {
			await sendEmail(registrationVerificationEmail(
				credential,
				`${envUtil.CLIENT_ENDPOINT}/user/verify?code=${verificationCode}&email=${credential}`,
			));
		}

		await transaction.commit();
	} catch (error) {
		await transaction.rollback();
		console.error('An error occurred', error);
		return res.failure('Registration failed. Please try again.');
	}
	
	userLog({
		event_type: 'AUTH',
		type: true,
		details: 'Registration was successful',
		action: 'AUTH-REGISTER-USER',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/register/user'
	});
	return res.created('Registration was successful');
}));

export default router;
