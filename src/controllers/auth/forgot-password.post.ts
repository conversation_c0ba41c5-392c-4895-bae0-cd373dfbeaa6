import { User } from '@/models';
import envUtil from '@/utils/env.util';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateRandomNumber, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { forgotPasswordEmail, sendEmail } from '@/utils/postmark.util';
import { Router, type Request, type Response } from 'express';
import { Op } from 'sequelize';

const router = Router();

/**
 * Send Reset Password Email
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/forgot-password', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { email } = req.body;
	if (!email) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Email is required',
			action: 'AUTH-PASSWORD-RESET-LINK',
			payloads: { email: email },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/forgot-password'
		});
		return res.forbidden('Email is required');
	}

	const user = await User.findOne({
		where: {
			email,
			email_verified_at: { [Op.ne]: null },
		}
	});

	if (!user) return res.invalid('User not found. Invalid email');

	if (user.action_link_sent_at && user.verification_code) {
		const timeDifference = (new Date().getTime()) - (new Date(user.action_link_sent_at).getTime());

		if (timeDifference < envUtil.RESEND_LINK_COOLDOWN) {
			const remainingTime = envUtil.RESEND_LINK_COOLDOWN - timeDifference;
			const remainingSeconds = Math.ceil(remainingTime / 1000);

			return res.forbidden(`Please wait for ${remainingSeconds} seconds more`);
		}
	}

	const verificationCode = generateRandomNumber(111111, 999999);

	await sendEmail(forgotPasswordEmail(
		email,
		`${envUtil.CLIENT_ENDPOINT}/user/reset?code=${verificationCode}&email=${email}`,
	));

	await user.update({
		verification_code: verificationCode,
		action_link_sent_at: new Date(),
	});

	userLog({
		event_type: 'AUTH',
		type: true,
		details: `A password reset link has been sent to (${user.email}).`,
		action: 'AUTH-PASSWORD-RESET-LINK',
		payloads: { email: email },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/forgot-password'
	});
	
	return res.success(`A password reset link has been sent to (${user.email}).`);
}));

export default router;
