import { User } from '@/models';
import envUtil from '@/utils/env.util';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateRandomNumber, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { sendEmail, registrationVerificationEmail } from '@/utils/postmark.util';
import { Router, type Request, type Response } from 'express';

const router = Router();

/**
 * Resend Verification Email
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/resend/verification-email', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { email } = req.body;
	if (!email) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Email is required',
			action: 'AUTH-RESEND-VERIFICATION-LINK',
			payloads: { email: email },
			ip_address: getClient<PERSON>(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/resend/verification-email'
		});
		return res.forbidden('Email is required');
	}

	const user = await User.findOne({
		where: {
			email,
		}
	});

	if (!user) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'User not found. Invalid email',
			action: 'AUTH-RESEND-VERIFICATION-LINK',
			payloads: { email: email },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/resend/verification-email'
		});
		return res.invalid('User not found. Invalid email');
	}

	if (user.email_verified_at) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Email is already verified',
			action: 'AUTH-RESEND-VERIFICATION-LINK',
			payloads: { email: email },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/resend/verification-email'
		});
		return res.invalid('Email is already verified');
	}

	const timeDifference = new Date().getTime() - new Date(user.action_link_sent_at).getTime();

	if (timeDifference <= envUtil.RESEND_LINK_COOLDOWN) return res.invalid('Please wait for some time before requesting a new link');

	await user.update({
		verification_code: generateRandomNumber(111111, 999999),
		action_link_sent_at: new Date(),
	});

	await sendEmail(registrationVerificationEmail(
		user.email,
		`${envUtil.CLIENT_ENDPOINT}/user/verify?code=${user.verification_code}&email=${user.email}`,
	));
	userLog({
		event_type: 'AUTH',
		type: false,
		details: 'Verification email was resent successfully',
		action: 'AUTH-RESEND-VERIFICATION-LINK',
		payloads: { email: email },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/resend/verification-email'
	});
	
	return res.success('Verification email was resent successfully');
}));

export default router;
