import { Company, CompanyPartnerUser, CompanyUser, PartnerType, User } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { type Request, type Response, Router } from 'express';
import { Op } from 'sequelize';
import bcrypt from 'bcrypt';
import { generateRandomNumber, generateRandomString, getClientIP } from '@/utils/general.util';
import sequelize from '@/configs/database.config';
import envUtil from '@/utils/env.util';
import { sendEmail, registrationVerificationEmail } from '@/utils/postmark.util';
import { userLog } from '@/utils/logger.util';

const router = Router();

/**
 * Partner Registration
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/register/partner', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { auth: authenticationInfo, company: partnerInfo, partnerCategory } = req.body;
	// eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
	const { password, ...authWithoutPassword } = authenticationInfo;
	if (!authenticationInfo || !partnerInfo || !partnerCategory) return res.invalid('Missing required fields');

	if (!authenticationInfo.email && !authenticationInfo.phone) return res.invalid('User\'s email or phone number is required');
	if (!partnerInfo.email && !partnerInfo.phone) return res.invalid('Company\'s email or phone number is required');

	let whereClause: { email?: string, phone?: string } = {};
	if (authenticationInfo.email) whereClause.email = authenticationInfo.email;
	if (authenticationInfo.phone) whereClause.phone = authenticationInfo.phone;

	const userExists = await User.findOne({
		where: {
			[Op.or]: [whereClause]
		}
	});

	if (userExists) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'User\'s email or phone number already registered',
			action: 'AUTH-REGISTER-PARTNER',
			payloads: { auth: authWithoutPassword, company: partnerInfo, partnerCategory },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/register/partner'
		});
		return res.forbidden('User\'s email or phone number already registered');
	}

	whereClause = {};
	if (partnerInfo.email) whereClause.email = partnerInfo.email;
	if (partnerInfo.phone) whereClause.phone = partnerInfo.phone;

	const companyExists = await Company.findOne({
		where: {
			[Op.or]: [whereClause]
		}
	});

	if (companyExists) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Company\'s email or phone number already registered',
			action: 'AUTH-REGISTER-PARTNER',
			payloads: { auth: authWithoutPassword, company: partnerInfo, partnerCategory },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/register/partner'
		});
		return res.forbidden('Company\'s email or phone number already registered');
	}

	const partnerType = await PartnerType.findOne({
		where: { type_name: partnerCategory },
	});

	if (!partnerType) {
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Invalid partner type selected',
			action: 'AUTH-REGISTER-PARTNER',
			payloads: { auth: authWithoutPassword, company: partnerInfo, partnerCategory },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/register/partner'
		});
		return res.forbidden('Invalid partner type selected');
	}

	const hashedPassword = await bcrypt.hash(authenticationInfo.password, 10);
	const verificationCode = generateRandomNumber(111111, 999999);

	const transaction = await sequelize.transaction();
	try {
		const newUser = await User.create({
			f_name: partnerInfo.name,
			email: authenticationInfo?.email,
			phone: authenticationInfo?.phone ? authenticationInfo?.phone : partnerInfo.phone,
			password: hashedPassword,
			is_partner: true,
			verification_code: verificationCode,
			action_link_sent_at: new Date(),
		},
		{ transaction });

		const newCompany = await Company.create({
			code: generateRandomString(32),
			name: partnerInfo.name,
			email: partnerInfo.email,
			phone: partnerInfo.phone,
			address: partnerInfo.address,
		},
		{ transaction });

		const companyUser = await CompanyUser.create({
			company_id: newCompany.id,
			user_id: newUser.id,
		},
		{ transaction });

		await CompanyPartnerUser.create({
			company_user_id: companyUser.id,
			partner_id: partnerType.id,
		},
		{ transaction });

		if (authenticationInfo.email) {
			await sendEmail(registrationVerificationEmail(
				authenticationInfo.email,
				`${envUtil.CLIENT_ENDPOINT}/user/verify?code=${verificationCode}&email=${authenticationInfo.email}`,
			));
		}

		await transaction.commit();
	} catch (error) {
		await transaction.rollback();
		console.error('An error occurred', error);
		return res.failure('Partner registration failed. Please try again.');
	}
	
	userLog({
		event_type: 'AUTH',
		type: true,
		details: `Partner registration was successful # ${partnerInfo.name}`,
		action: 'AUTH-REGISTER-PARTNER',
		payloads: { auth: authWithoutPassword, company: partnerInfo, partnerCategory },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/register/partner'
	});
	return res.created(`Partner registration was successful # ${partnerInfo.name}`);
}));

export default router;
