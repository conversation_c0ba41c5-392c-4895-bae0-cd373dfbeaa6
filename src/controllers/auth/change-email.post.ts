import authMiddleware from '@/middlewares/auth.middleware';
import { Company, User } from '@/models';
import envUtil from '@/utils/env.util';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { Router, type Request, type Response } from 'express';
import jwt from 'jsonwebtoken';

const router = Router();

/**
 * Change User Email
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/change-email', authMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	if (!req.user){
		userLog({
			event_type: 'AUTH',
			type: false,
			details: 'Invalid request',
			action: 'AUTH-EMAIL-UPDATE',
			payloads: { ...req.body },
			ip_address: getClient<PERSON>(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/change-email'
		});
		return res.forbidden('Invalid request');
	}

	const { old_email, new_email, code } = req.body;
	if (!old_email || !new_email || !code) return res.forbidden('Invalid request. Required fields are missing');

	const user = await User.findOne({
		where: {
			email: old_email,
			id: req.user.id,
		},
		include: [
			{
				model: Company,
				where: { status: true },
				attributes: ['id', 'name', 'email', 'phone', 'about', 'address', 'logo', 'domain'],
				required: false,
			}
		]
	});

	if (!user) {
		userLog({
			user_id: req.user?.id,
			event_type: 'AUTH',
			type: false,
			details: 'User not found. Invalid email',
			action: 'AUTH-EMAIL-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/change-email'
		});
		return res.invalid('Invalid Request. User not found');
	}

	const timeDifference = (new Date().getTime()) - (new Date(user.action_link_sent_at).getTime());
	if (timeDifference > envUtil.LINK_VERIFICATION_EXPIRY) return res.invalid('Link expired. Please request a new link');
	if (user.verification_code !== code) return res.invalid('Invalid verification code');

	await user.update({
		email: new_email,
		email_verified_at: new Date(),
		verification_code: null,
		action_link_sent_at: null,
		email_updated_at: new Date(),
	});

	const jwtData = {
		id: user.id,
		email: user.email,
		is_partner: user.is_partner,
		user_type: user.user_type,
		...(user.companies.length > 0 && { companies: user.companies.map((company) => ({ id: company.id })) }),
	};
	
	const accessToken = jwt.sign(
		jwtData,
		envUtil.JWT_SALT,
		{ expiresIn: '30d' }
	);
	const refreshToken = jwt.sign(
		jwtData,
		envUtil.JWT_SALT,
		{ expiresIn: '30d' }
	);
	
	userLog({
		user_id: req.user.id,
		event_type: 'AUTH',
		type: true,
		details: 'Email updated successfully',
		action: 'AUTH-EMAIL-UPDATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/change-email'
	});
	return res.success(
		'Email changed successfully',
		{
			tokens: {
				accessToken,
				refreshToken,
			},
			userInfo: user.toJSON(),
		}
	);
}));

export default router;
