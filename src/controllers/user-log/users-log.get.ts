import { User, UserLog } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import { type Request, type Response, Router } from 'express';
import { Op } from 'sequelize';

const router = Router();

/**
 * Get User logs
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/users-log', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { sort, order, name, log_action } = req.query;
	let searchCondition = {};

	if (name || log_action) {
		searchCondition = {
			[Op.and]: [
				name !== 'null' ? { event_type: name } : {}, // Case-insensitive search (PostgreSQL)
				(log_action && log_action !== null)? { action: log_action } : {},
			].filter(Boolean) // Remove empty conditions
		};
	}
	const userLogs = await UserLog.findAndCountAll({
		where: searchCondition,
		include: [
			{
				model: User,
				as: 'user',
				attributes: ['id', 'f_name', 'l_name']
				// where: {
				// 	u_role: { [Op.not]: 'admin' }
				// }
			}
		],
		order: [[(sort || 'id') as string, (order || 'desc') as string]],
		...paginated(req)
	});

	return res.paginated(userLogs, req);
}));

export default router;
