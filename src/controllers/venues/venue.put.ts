import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateSlug, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Edit Venue
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.put('/venues/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'VENUE',
			type: false,
			details: 'Invalid request. Venue ID is required',
			action: 'VENUE-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/venues/:id'
		});
		return res.forbidden('Invalid request. Venue ID is required');
	}

	const {
		name,
		logo,
		image,
		facebook,
		youtube,
		twitter,
		instagram,
		contact_info,
		about,
		attractions,
		address,
		mobile_number,
		phone_number,
		email,
		website,
		meta_keywords,
		meta_desc
	} = req.body;

	if (!name || !address) {
		userLog({
			user_id: req.user?.id,
			event_type: 'VENUE',
			type: false,
			details: 'Invalid data. Venue name and address are required',
			action: 'VENUE-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/venues/:id'
		});
		return res.forbidden('Invalid data. Venue name and address are required');
	}

	const existingVenue = await Venue.findOne({
		where: { id },
	});
	if (!existingVenue) {
		userLog({
			user_id: req.user?.id,
			event_type: 'VENUE',
			type: false,
			details: 'Venue does not exist',
			action: 'VENUE-UPDATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PUT',
			api_endpoint: '/venues/:id'
		});
		return res.forbidden('Venue does not exist');
	}

	const slug = generateSlug(name);

	if (existingVenue.slug !== slug) {
		const newExistingVenue = await Venue.findOne({ where: { slug }});
		if (newExistingVenue) {
			userLog({
				user_id: req.user?.id,
				event_type: 'VENUE',
				type: false,
				details: `Venue "${name}" already exists`,
				action: 'VENUE-UPDATE',
				payloads: { ...req.body },
				ip_address: getClientIP(req),
				user_agent: 'web',
				read_access: true,
				referrer: null,
				method: 'PUT',
				api_endpoint: '/venues/:id'
			});
			return res.forbidden(`Venue "${name}" already exists`);
		}
	}

	let longitude = null, latitude = null;
	if (address) {
		const { lng, lat } = address;
		longitude = lng;
		latitude = lat;
	}

	await existingVenue.update({
		name,
		slug,
		logo: logo || null,
		image: image || null,
		facebook: facebook || null,
		youtube: youtube || null,
		twitter: twitter || null,
		instagram: instagram || null,
		contact_info: contact_info || null,
		about: about || null,
		attractions: attractions || null,
		coordinates: longitude !== null && latitude !== null ? {
			type: 'Point',
			coordinates: [longitude, latitude], // Note the order: [longitude, latitude]
		} : null,
		address: address || null,
		mobile_number: mobile_number || null,
		phone_number: phone_number || null,
		email: email || null,
		website: website || null,
		meta_keywords: meta_keywords ? meta_keywords.join(',') : null,
		meta_desc: meta_desc || null,
	});
	userLog({
		user_id: req.user?.id,
		event_type: 'VENUE',
		type: true,
		details: 'Venue updated successfully',
		action: 'VENUE-UPDATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PUT',
		api_endpoint: '/venues/:id'
	});
	return res.success('Venue updated successfully');
}));

export default router;
