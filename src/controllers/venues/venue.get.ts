import sequelize from '@/configs/database.config';
import { Event, EventTicket, Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import dayjs from 'dayjs';
import { type Request, type Response, Router } from 'express';
import { Op } from 'sequelize';

const router = Router();

/**
 * Get Venue by id
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/venues/:id', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) return res.forbidden('Invalid request. Venue ID is required');

	const venue = await Venue.findOne({
		where: { id },
		attributes: {
			include: [
				[sequelize.literal('(SELECT COUNT(*) FROM "event_venue" WHERE "event_venue"."venue_id" = "Venue"."id")'), 'total_events_hosted'],
			]
		},
	});
	if (!venue) return res.forbidden('Venue does not exist');

	const upcomingEvents = await Event.findAll({
		attributes: ['id', 'title', 'slug', 'image', 'start_date_time'],
		where: { start_date_time: { [Op.gte]: dayjs().toDate() }},
		include: [
			{
				model: Venue,
				attributes: ['name'],
				through: { attributes: []},
				where: { id: venue.id },
				required: true,
			},
			{ model: EventTicket },
		],
		order: [['start_date_time', 'ASC']],
		limit: 6,
	});

	venue.setDataValue('events', upcomingEvents);

	return res.single(venue);
}));

export default router;
