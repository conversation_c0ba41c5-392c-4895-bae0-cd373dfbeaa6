import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Venue, Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Activate or Deactivate Venue
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.patch('/venues/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'VENUE',
			type: false,
			details: 'Invalid request. Venue ID is required',
			action: 'VENUE-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/venues/:id'
		});
		return res.forbidden('Invalid request. Venue ID is required');
	}

	const { status } = req.body;
	if (typeof status !== 'boolean') {
		userLog({
			user_id: req.user?.id,
			event_type: 'VENUE',
			type: false,
			details: 'Invalid request. Venue status is required and must be a boolean value',
			action: 'VENUE-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/venues/:id'
		});
		return res.forbidden('Invalid request. Venue status is required and must be a boolean value');
	}

	const existingVenue = await Venue.findOne({ where: { id }});
	if (!existingVenue) {
		userLog({
			user_id: req.user?.id,
			event_type: 'VENUE',
			type: false,
			details: 'Venue does not exist',
			action: 'VENUE-UPDATE',
			payloads: { id: id, ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'PATCH',
			api_endpoint: '/venues/:id'
		});
		return res.forbidden('Venue does not exist');
	}

	// Check if category is being used by any events if it is being deactivated
	if (!status) {
		const event = await Event.findOne({
			include: [
				{
					model: Venue,
					where: { id: Number(id) }
				}
			]
		});
		if (event) {
			userLog({
				user_id: req.user?.id,
				event_type: 'VENUE',
				type: false,
				details: 'Venue is being used by some events and cannot be deactivated',
				action: 'VENUE-UPDATE',
				payloads: { id: id, ...req.body },
				ip_address: getClientIP(req),
				user_agent: 'web',
				read_access: true,
				referrer: null,
				method: 'PATCH',
				api_endpoint: '/venues/:id'
			});
			return res.forbidden('Venue is being used by some events and cannot be deactivated');
		}
	}

	await existingVenue.update({ status });
	userLog({
		user_id: req.user?.id,
		event_type: 'VENUE',
		type: true,
		details: `Venue ${existingVenue.name} ${status ? 'activated' : 'deactivated'} successfully`,
		action: 'VENUE-UPDATE',
		payloads: { id: id, ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'PATCH',
		api_endpoint: '/venues/:id'
	});

	return res.success(`Venue ${existingVenue.name} ${status ? 'activated' : 'deactivated'} successfully`);
}));

export default router;
