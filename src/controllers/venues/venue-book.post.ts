import { Venue, VenueBookingInquiry } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP, getDecodedUser } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Create Venue Booking Inquiry
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/venue-booking-inquiry', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { venue_id, name, email, phone, description } = req.body;

	// Get user if authenticated (optional)
	const decodedUser = getDecodedUser(req);
	const user_id = decodedUser?.id || null;

	// Validate required fields
	if (!venue_id || !name || !email || !phone || !description) {
		userLog({
			user_id: user_id,
			event_type: 'VENUE',
			type: false,
			details: 'Missing required fields. Venue ID, name, email, phone, and description are required',
			action: 'VENUE-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/venue-booking-inquiry'
		});
		return res.forbidden('Missing required fields. Venue ID, name, email, phone, and description are required');
	}

	// Validate email format
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	if (!emailRegex.test(email)) {
		userLog({
			user_id: user_id,
			event_type: 'VENUE',
			type: false,
			details: 'Invalid email format',
			action: 'VENUE-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/venue-booking-inquiry'
		});
		return res.forbidden('Invalid email format');
	}

	// Validate phone format (Nepal mobile: 96/97/98 + 8 digits)
	const phoneRegex = /^(96|97|98)\d{8}$/;
	if (!phoneRegex.test(phone)) {
		userLog({
			user_id: user_id,
			event_type: 'VENUE',
			type: false,
			details: 'Invalid phone number format. Phone must be a valid Nepali mobile number',
			action: 'VENUE-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/venue-booking-inquiry'
		});
		return res.forbidden('Invalid phone number format. Phone must be a valid Nepali mobile number');
	}

	// Check if venue exists
	const venue = await Venue.findOne({ where: { id: venue_id }});
	if (!venue) {
		userLog({
			user_id: user_id,
			event_type: 'VENUE',
			type: false,
			details: 'Venue does not exist',
			action: 'VENUE-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/venue-booking-inquiry'
		});
		return res.forbidden('Venue does not exist');
	}

	try {
		// Create venue booking inquiry
		const venueBookingInquiry = await VenueBookingInquiry.create({
			venue_id,
			user_id,
			name,
			email,
			phone,
			description
		});

		userLog({
			user_id: user_id,
			event_type: 'VENUE',
			type: true,
			details: `Venue booking inquiry created successfully for venue: ${venue.name}`,
			action: 'VENUE-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/venue-booking-inquiry'
		});

		return res.status(201).json({
			message: 'Venue booking inquiry submitted successfully',
			data: venueBookingInquiry
		});

	} catch (error) {
		console.error('Error creating venue booking inquiry:', error);

		userLog({
			user_id: user_id,
			event_type: 'VENUE',
			type: false,
			details: 'Failed to create venue booking inquiry',
			action: 'VENUE-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/venue-booking-inquiry'
		});

		return res.status(500).json({
			message: 'Failed to submit venue booking inquiry. Please try again.'
		});
	}
}));

export default router;
