import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Venue, Event } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Delete Venue
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.delete('/venues/:id', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { id } = req.params;
	if (!id) {
		userLog({
			user_id: req.user?.id,
			event_type: 'VENUE',
			type: false,
			details: 'Invalid request',
			action: 'VENUE-DELETE',
			payloads: { id: id },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'DELETE',
			api_endpoint: '/venues/:id'
		});
		return res.forbidden('Invalid request. Venue ID is required');
	}

	const existingVenue = await Venue.findOne({ where: { id }});
	if (!existingVenue) {
		userLog({
			user_id: req.user?.id,
			event_type: 'VENUE',
			type: false,
			details: 'Venue does not exist',
			action: 'VENUE-DELETE',
			payloads: { id: id },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'DELETE',
			api_endpoint: '/venues/:id'
		});
		return res.forbidden('Venue does not exist');
	}

	// Check if category is being used by any events
	const event = await Event.findOne({
		include: [
			{
				model: Venue,
				where: { id: Number(id) }
			}
		]
	});
	if (event) return res.forbidden('Venue is being used by some events and cannot be deleted');

	await existingVenue.destroy();
	userLog({
		user_id: req.user?.id,
		event_type: 'VENUE',
		type: true,
		details: `Venue "${existingVenue.name}" deleted successfully`,
		action: 'VENUE-DELETE',
		payloads: { id: id },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'DELETE',
		api_endpoint: '/venues/:id'
	});
	return res.success(`Venue "${existingVenue.name}" deleted successfully`);
}));

export default router;
