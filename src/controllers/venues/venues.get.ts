import { Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { paginated } from '@/utils/pagination.util';
import { type Request, type Response, Router } from 'express';
import { Op, type FindAndCountOptions } from 'sequelize';
import sequelize from 'sequelize/lib/sequelize';

const router = Router();

/**
 * Get Venues
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.get('/venues', errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const { type, search } = req.query;

	let venueStatus: boolean | null;
	if (type) {
		venueStatus = type === 'active' ? true : type === 'inactive' ? false : null;
	} else venueStatus = null;

	const queryOptions: FindAndCountOptions<Venue> = {
		where: {
			...(typeof venueStatus === 'boolean' ? { status: venueStatus } : {}),
			...(search && typeof search === 'string'
				? { name: { [Op.iLike]: `%${search.replace(/\s+/g, '%')}%` }}
				: {})
		},
		order: [
			[
				sequelize.literal(`
					CASE 
						WHEN "Venue"."status" = true THEN 0 
						ELSE 1 
					END
				`),
				'ASC'
			],
			['id', 'ASC']
		]
	};

	const venues = await Venue.findAndCountAll({
		...queryOptions,
		...(paginated(req))
	});

	return res.paginated(venues, req);
}));

export default router;
