import authMiddleware from '@/middlewares/auth.middleware';
import { checkAdminMiddleware } from '@/middlewares/check-admin.middleware';
import { Venue } from '@/models';
import { errorWrapper } from '@/utils/error-wrapper.util';
import { generateSlug, getClientIP } from '@/utils/general.util';
import { userLog } from '@/utils/logger.util';
import { type Request, type Response, Router } from 'express';

const router = Router();

/**
 * Create Venue
 * @param {Request} req
 * @param {Response} res
 * @returns {Promise<Response>}
 * @throws {Error}
 */
router.post('/venues', authMiddleware, checkAdminMiddleware, errorWrapper(async (req: Request, res: Response): Promise<Response> => {
	const {
		name,
		logo,
		image,
		facebook,
		youtube,
		twitter,
		instagram,
		contact_info,
		about,
		attractions,
		address,
		mobile_number,
		phone_number,
		email,
		website,
		meta_keywords,
		meta_desc
	} = req.body;

	if (!name || !address) {
		userLog({
			user_id: req.user?.id,
			event_type: 'VENUE',
			type: false,
			details: 'Invalid data. Venue name and address are required',
			action: 'VENUE-CREATE',
			payloads: { ...req.body },
			ip_address: getClientIP(req),
			user_agent: 'web',
			read_access: true,
			referrer: null,
			method: 'POST',
			api_endpoint: '/venues'
		});
		return res.forbidden('Invalid data. Venue name and address are required');
	}

	const slug = generateSlug(name);

	const existingVenue = await Venue.findOne({ where: { slug }});
	if (existingVenue) return res.forbidden(`Venue "${name}" already exists`);

	let longitude = null, latitude = null;
	if (address) {
		const { lng, lat } = address;
		longitude = lng;
		latitude = lat;
	}

	await Venue.create({
		name,
		slug,
		logo: logo || null,
		image: image || null,
		facebook: facebook || null,
		youtube: youtube || null,
		twitter: twitter || null,
		instagram: instagram || null,
		contact_info: contact_info || null,
		about: about || null,
		attractions: attractions || null,
		coordinates: longitude !== null && latitude !== null ? {
			type: 'Point',
			coordinates: [longitude, latitude], // Note the order: [longitude, latitude]
		} : null,
		address: address || null,
		mobile_number: mobile_number || null,
		phone_number: phone_number || null,
		email: email || null,
		website: website || null,
		meta_keywords: meta_keywords ? meta_keywords.join(',') : null,
		meta_desc: meta_desc || null,
		status: true,
	});
	userLog({
		user_id: req.user?.id,
		event_type: 'VENUE',
		type: true,
		details: `Venue "${name}" created successfully`,
		action: 'VENUE-CREATE',
		payloads: { ...req.body },
		ip_address: getClientIP(req),
		user_agent: 'web',
		read_access: true,
		referrer: null,
		method: 'POST',
		api_endpoint: '/venues'
	});
	return res.created(`Venue "${name}" created successfully`);
}));

export default router;
