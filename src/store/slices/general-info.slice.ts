/* eslint-disable @typescript-eslint/no-explicit-any */
import { ResponseErrorType, ServerStatus } from "@/types/index.types";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import request from "@/utils/request.util";
import { actionWrapper } from "@/utils/redux.util";
import { GeneralInfoModal } from "@/types/models/general-info.model";

type GeneralInfoState = {
	status: ServerStatus;
	record?: GeneralInfoModal | null;
	errors?: ResponseErrorType | null;
}

const initialState: GeneralInfoState = {
	status: 'idle',
	record: null,
	errors: null
};

const generalInfoSlice = createSlice({
	name: 'generalInfo',
	initialState,
	reducers: {
		generalInfoStateChange: (state, { payload }) => {
			const { key, value }: {key: keyof GeneralInfoState, value: any} = payload;
			(state as Record<keyof GeneralInfoState, any>)[key] = value;
		}
	}
})

export const { generalInfoStateChange } = generalInfoSlice.actions;

export const fetchGeneralInfoAction = () => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(generalInfoStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/general-info');
				if (response.status === 200) {
					dispatch(generalInfoStateChange({ key: 'record', value: response.data }));
					dispatch(generalInfoStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: generalInfoStateChange
		},
		dispatch
	);
};


export const updateGeneralInfoAction = <T>(payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(generalInfoStateChange({ key: 'status', value: 'saving' }));
				const response = await request.put('/api/general-info', payload);
				if (response.status === 200) {
					dispatch(generalInfoStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: generalInfoStateChange
		},
		dispatch
	);
};

export default generalInfoSlice.reducer;
