/* eslint-disable @typescript-eslint/no-explicit-any */
import { ActionQueryPayload, PaginationMeta, ResponseErrorType, ServerStatus } from "@/types/index.types";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import request from "@/utils/request.util";
import { VenueModel } from "@/types/models/venue.model";
import { actionWrapper } from "@/utils/redux.util";
import { constructQuery } from "@/utils/general.util";

type VenueState = {
	status: ServerStatus;
	rows: VenueModel[];
	record?: VenueModel | null;
	meta: PaginationMeta | null;
	errors?: ResponseErrorType | null;
}

const initialState: VenueState = {
	status: 'idle',
	rows: [],
	record: null,
	meta: null,
	errors: null,
};

const venueSlice = createSlice({
	name: 'venue',
	initialState,
	reducers: {
		venueStateChange: (state, { payload }) => {
			const { key, value }: {key: keyof VenueState, value: any} = payload;
			(state as Record<keyof VenueState, any>)[key] = value;
		}
	}
})

export const { venueStateChange } = venueSlice.actions;

export const fetchAllVenuesAction = (payload?: ActionQueryPayload) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(venueStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/venues', { params: constructQuery(payload) });
				if (response.status === 200) {
					dispatch(venueStateChange({ key: 'rows', value: response.data.rows }))
					dispatch(venueStateChange({ key: 'meta', value: response.data.meta }))
					dispatch(venueStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: venueStateChange
		},
		dispatch
	);
}

export const fetchVenueAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(venueStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get(`/api/venues/${id}`);
				if (response.status === 200) {
					dispatch(venueStateChange({ key: 'record', value: response.data }))
					dispatch(venueStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: venueStateChange
		},
		dispatch
	);
};

export const createVenueAction = <T>(payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(venueStateChange({ key: 'status', value: 'saving' }));
				const response = await request.post('/api/venues', payload);
				if (response.status === 201) {
					dispatch(venueStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: venueStateChange
		},
		dispatch
	);
};

export const updateVenueAction = <T>(id: number, payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(venueStateChange({ key: 'status', value: 'saving' }));
				const response = await request.put(`/api/venues/${id}`, payload);
				if (response.status === 200) {
					dispatch(venueStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: venueStateChange
		},
		dispatch
	);
};

export const deleteVenueAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(venueStateChange({ key: 'status', value: 'deleting' }));
				const response = await request.delete(`/api/venues/${id}`);
				if (response.status === 200) {
					dispatch(venueStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: venueStateChange
		},
		dispatch
	);
};

export const toggleVenueStatusAction = (id: number, status: boolean) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(venueStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/venues/${id}`, { status });
				if (response.status === 200) {
					dispatch(venueStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: venueStateChange
		},
		dispatch
	);
};

export const venueBookingInquiryAction = <T>(payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(venueStateChange({ key: 'status', value: 'saving' }));
				const response = await request.post('/api/venue-booking-inquiry', payload);
				if (response.status === 201) {
					dispatch(venueStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: venueStateChange
		},
		dispatch
	);
};

export default venueSlice.reducer;
