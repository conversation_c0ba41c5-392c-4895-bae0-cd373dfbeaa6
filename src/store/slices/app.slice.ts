/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSlice } from "@reduxjs/toolkit"
import { ResponseErrorType, ServerStatus } from "../../types/index.types";
import { AxiosResponse } from "axios";
import request from "@/utils/request.util";
import { AppModel } from "@/types/models/app.model";
import { actionWrapper } from "@/utils/redux.util";
import { AppDispatch } from "..";

type AppState = {
	actionConfirmation: {
		title: string,
		subTitle: string | React.ReactNode,
		modal: boolean,
		status: ServerStatus,
		additionalInfo: string | null,
		btnLabel: string,
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		onBtnClick?: null | ((reason?: string) => Promise<AxiosResponse<any, any>> | Promise<void>)
		requireInput?: boolean,
		inputPlaceholder?: string,
	},
	apps: AppModel[],
	showLoginModal: boolean,
	status: ServerStatus,
	errors?: ResponseErrorType | null;
}

const initialState: AppState = {
	actionConfirmation: {
		status: 'idle',
		title: "",
		subTitle: "",
		modal: false,
		additionalInfo: "Action once done cannot be undone",
		btnLabel: "Delete Now",
		onBtnClick: null,
		requireInput: false,
	},
	apps: [],
	showLoginModal: false,
	status: 'idle',
	errors: null
}

const authSlice = createSlice({
	name: 'app',
	initialState,
	reducers: {
		triggerConfirmation(state, { payload }: {payload: Partial<typeof initialState.actionConfirmation>}) {
			return {
				...state,
				actionConfirmation: {
					...state.actionConfirmation,
					...payload
				}
			}
		},
		appStateChange: (state, { payload }) => {
			const { key, value }: {key: keyof AppState, value: any} = payload;
			(state as Record<keyof AppState, any>)[key] = value;
		}
	},
})

export const { triggerConfirmation, appStateChange } = authSlice.actions;

export const fetchAppsAction = () => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(appStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/app-types');
				if (response.status === 200) {
					dispatch(appStateChange({ key: 'apps', value: response.data.rows }))
					dispatch(appStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: appStateChange
		},
		dispatch
	);
}

export const uploadImageAction = (formData: FormData) => async () => {
	try {
		const response = await request.post('/api/upload', formData, {
			headers: {
				'Content-Type': 'multipart/form-data'
			}
		});
		return response;
	} catch (error: any) {
		console.log(error);
		return Promise.reject(error.response.data.message);
	}
}

export default authSlice.reducer;
