/* eslint-disable @typescript-eslint/no-explicit-any */
import { ActionQueryPayload, PaginationMeta, ResponseErrorType, ServerStatus } from "@/types/index.types";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import request from "@/utils/request.util";
import { actionWrapper } from "@/utils/redux.util";
import { constructQuery } from "@/utils/general.util";
import { TopicModel } from "@/types/models/topic.model";
import { TopicQasModel } from "@/types/models/topic-qas.model";

type TopicState = {
	status: ServerStatus;
	rows: TopicModel[];
	topicRecord?: TopicModel | null;
	topicQaRecord?: TopicQasModel | null;
	meta: PaginationMeta | null;
	errors?: ResponseErrorType | null;
}

const initialState: TopicState = {
	status: 'idle',
	rows: [],
	topicRecord: null,
	topicQaRecord: null,
	meta: null,
	errors: null
};

const topicSlice = createSlice({
	name: 'topic',
	initialState,
	reducers: {
		topicStateChange: (state, { payload }) => {
			const { key, value }: {key: keyof TopicState, value: any} = payload;
			(state as Record<keyof TopicState, any>)[key] = value;
		}
	}
})

export const { topicStateChange } = topicSlice.actions;

export const fetchAllTopicsAction = (payload?: ActionQueryPayload) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/topics', { params: constructQuery(payload) });
				if (response.status === 200) {
					dispatch(topicStateChange({ key: 'rows', value: response.data.rows }))
					dispatch(topicStateChange({ key: 'meta', value: response.data.meta }))
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
}

export const fetchTopicAction = (payload: ActionQueryPayload & { id: number }) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get(`/api/topics/${payload.id}`, { params: constructQuery(payload) });
				if (response.status === 200) {
					dispatch(topicStateChange({ key: 'topicRecord', value: response.data }))
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export const createTopicAction = <T>(payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'saving' }));
				const response = await request.post('/api/topics', payload);
				if (response.status === 201) {
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export const updateTopicAction = <T>(id: number, payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'saving' }));
				const response = await request.put(`/api/topics/${id}`, payload);
				if (response.status === 200) {
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export const createTopicQaAction = <T>(payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'saving' }));
				const response = await request.post('/api/topics/qa', payload);
				if (response.status === 201) {
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export const updateTopicQaAction = <T>(id: number, payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'saving' }));
				const response = await request.put(`/api/topics/qa/${id}`, payload);
				if (response.status === 200) {
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export const deleteTopicAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'deleting' }));
				const response = await request.delete(`/api/topics/${id}`);
				if (response.status === 200) {
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export const deleteTopicQaAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'deleting' }));
				const response = await request.delete(`/api/topics/qa/${id}`);
				if (response.status === 200) {
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export const toggleTopicStatusAction = (id: number, status: boolean) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/topics/${id}`, { status });
				if (response.status === 200) {
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export const toggleTopicQaStatusAction = (id: number, status: boolean) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/topics/qa/${id}`, { status });
				if (response.status === 200) {
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export const fetchTopicQaAction = (slug: string) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get(`/api/topics/qa/${slug}`);
				if (response.status === 200) {
					dispatch(topicStateChange({ key: 'topicQaRecord', value: response.data }))
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export const updateTopicQaViewAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(topicStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/topics/qa/view/${id}`);
				if (response.status === 200) {
					dispatch(topicStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: topicStateChange
		},
		dispatch
	);
};

export default topicSlice.reducer;
