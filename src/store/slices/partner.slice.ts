/* eslint-disable @typescript-eslint/no-explicit-any */
import { PartnerStats, ResponseErrorType, ServerStatus } from "@/types/index.types";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import request from "@/utils/request.util";
import { actionWrapper } from "@/utils/redux.util";
import { UserModel } from "@/types/models/user.model";
import { getEncryptedCookie, setEncryptedCookie } from "@/utils/cookie.util";

type PartnerState = {
	status: ServerStatus;
	registerSuccess: boolean;
	errors?: ResponseErrorType | null;
	stats: PartnerStats | null;
}

const initialState: PartnerState = {
	status: 'idle',
	registerSuccess: false,
	errors: null,
	stats: null,
};

const partnerSlice = createSlice({
	name: 'partner',
	initialState,
	reducers: {
		partnerStateChange: (state, { payload }) => {
			const { key, value }: {key: keyof PartnerState, value: any} = payload;
			(state as Record<keyof PartnerState, any>)[key] = value;
		}
	}
})

export const { partnerStateChange } = partnerSlice.actions;

export const registerPartnerAction = <T>(payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(partnerStateChange({ key: 'status', value: 'saving' }));
				const response = await request.post('/api/register/partner', payload);
				if (response.status === 201) {
					dispatch(partnerStateChange({ key: 'registerSuccess', value: true }));
					dispatch(partnerStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: partnerStateChange
		},
		dispatch
	);
}

export const updateCompanyLogoAction = (logo: string, companyId: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(partnerStateChange({ key: 'status', value: 'saving' }));
				const response = await request.post('/api/company-logo', { logo, companyId });
				if (response.status === 200) {
					dispatch(partnerStateChange({ key: 'errors', value: null }));
					const user = getEncryptedCookie('_eticket_user_') as UserModel;

					if (user && user.companies?.length) {
						user.companies[0].logo = logo;

						const isProduction = process.env.NODE_ENV === 'production';
						// Save the updated user data back into the cookie
						setEncryptedCookie('_eticket_user_', JSON.stringify(user), {
							secure: isProduction,
							sameSite: isProduction ? 'None' : 'Lax',
							domain: isProduction ? '.eticketnepal.com' : undefined,
							path: '/',
							expires: 365,
						});
					}
				}
				return response;
			},
			stateChangeAction: partnerStateChange
		},
		dispatch
	);
}

export const updateCompanyInfoAction = <T>(payload: T, companyId: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(partnerStateChange({ key: 'status', value: 'saving' }));
				const response = await request.put(`/api/company/${companyId}`, payload);
				if (response.status === 200) {
					dispatch(partnerStateChange({ key: 'errors', value: null }));
					const user = getEncryptedCookie('_eticket_user_') as UserModel;

					if (user && user.companies?.length) {
						user.companies[0] = response.data.data;

						const isProduction = process.env.NODE_ENV === 'production';

						setEncryptedCookie('_eticket_user_', JSON.stringify(user), {
							secure: isProduction,
							sameSite: isProduction ? 'None' : 'Lax',
							domain: isProduction ? '.eticketnepal.com' : undefined,
							path: '/',
							expires: 365,
						});
					}
				}
				return response;
			},
			stateChangeAction: partnerStateChange
		},
		dispatch
	);
}

export const fetchPartnerStatsAction = () => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(partnerStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/partner/stats');
				if (response.status === 200) {
					dispatch(partnerStateChange({ key: 'stats', value: response.data }));
					dispatch(partnerStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: partnerStateChange
		},
		dispatch
	);
}

export default partnerSlice.reducer;
