/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
	ActionQueryPayload,
	PaginationMeta,
	ResponseErrorType,
	ServerStatus,
} from "@/types/index.types";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import request from "@/utils/request.util";
import {
	getEncryptedCookie,
	setCookie,
	setEncryptedCookie,
} from "@/utils/cookie.util";
import { actionWrapper } from "@/utils/redux.util";
import { UserModel } from "@/types/models/user.model";
import { UpdatePersonalInfoType } from "@/types/forms/update-user-info.type";
import { constructQuery } from "@/utils/general.util";
import { UserLogModel } from "@/types/models/user-log.model";
import { EventSubscriptionModel } from "@/types/models/event-subscription.model";
import { PurchaseInfoModel } from "@/types/models/purchase-info.model";

type UserState = {
  status: ServerStatus;
  registerSuccess: boolean;
  errors?: ResponseErrorType | null;
  userLogs?: UserLogModel[];
	subscribedEvents?: EventSubscriptionModel[];
	purchaseInfos?: PurchaseInfoModel[];
	preferences?: Pick<UserModel, "user_preference" | "user_category_preferences"> | null;
  meta?: PaginationMeta | null;
};

const initialState: UserState = {
	status: "idle",
	registerSuccess: false,
	errors: null,
	userLogs: [],
	subscribedEvents: [],
	purchaseInfos: [],
	preferences: null,
	meta: null,
};

const userSlice = createSlice({
	name: "user",
	initialState,
	reducers: {
		userStateChange: (state, { payload }) => {
			const { key, value }: { key: keyof UserState; value: any } = payload;
			(state as Record<keyof UserState, any>)[key] = value;
		},
	},
});

export const { userStateChange } = userSlice.actions;

export const registerUserAction =
  <T>(payload: T) =>
		async (dispatch: AppDispatch) => {
			return actionWrapper(
				{
					tryBlock: async () => {
						dispatch(userStateChange({ key: "status", value: "saving" }));
						const response = await request.post("/api/register/user", payload);
						if (response.status === 201) {
							dispatch(userStateChange({ key: "registerSuccess", value: true }));
							dispatch(userStateChange({ key: "errors", value: null }));
						}
						return response;
					},
					stateChangeAction: userStateChange,
				},
				dispatch
			);
		};

export const loginUserAction =
  <T>(payload: T) =>
		async (dispatch: AppDispatch) => {
			return actionWrapper(
				{
					tryBlock: async () => {
						dispatch(userStateChange({ key: "status", value: "waiting" }));
						const response = await request.post("/api/login/user", payload);
						if (response.status === 200) {
							dispatch(userStateChange({ key: "errors", value: null }));
							const { tokens, userInfo } = response.data.data;
							const isProduction = process.env.NODE_ENV === "production";
							setCookie("_eticket_at_", tokens.accessToken, {
								secure: isProduction, // HTTPS only in production
								sameSite: isProduction ? "None" : "Lax", // Cross-origin in production
								domain: isProduction ? ".eticketnepal.com" : undefined, // Domain for production
								path: "/", // Accessible across the entire domain
								expires: 365, // Expires in days
							});
							setCookie("_eticket_rt_", tokens.refreshToken, {
								secure: isProduction,
								sameSite: isProduction ? "None" : "Lax",
								domain: isProduction ? ".eticketnepal.com" : undefined,
								path: "/",
								expires: 365,
							});
							setEncryptedCookie("_eticket_user_", JSON.stringify(userInfo), {
								secure: isProduction,
								sameSite: isProduction ? "None" : "Lax",
								domain: isProduction ? ".eticketnepal.com" : undefined,
								path: "/",
								expires: 365,
							});
							return response;
						}
					},
					stateChangeAction: userStateChange,
				},
				dispatch
			);
		};

export const verifyUserAction =
  <T>(payload: T) =>
		async (dispatch: AppDispatch) => {
			return actionWrapper(
				{
					tryBlock: async () => {
						dispatch(userStateChange({ key: "status", value: "saving" }));
						const response = await request.post("/api/verify/user", payload);
						if (response.status === 200) {
							dispatch(userStateChange({ key: "errors", value: null }));
						}
						return response;
					},
					stateChangeAction: userStateChange,
				},
				dispatch
			);
		};

export const resendVerificationEmailAction =
  <T>(payload: T) =>
		async (dispatch: AppDispatch) => {
			return actionWrapper(
				{
					tryBlock: async () => {
						dispatch(userStateChange({ key: "status", value: "waiting" }));
						const response = await request.post(
							"/api/resend/verification-email",
							payload
						);
						if (response.status === 200) {
							dispatch(userStateChange({ key: "errors", value: null }));
						}
						return response;
					},
					stateChangeAction: userStateChange,
				},
				dispatch
			);
		};

export const sendTokenAction =
  <T>(payload: T) =>
		async (dispatch: AppDispatch) => {
			return actionWrapper(
				{
					tryBlock: async () => {
						dispatch(userStateChange({ key: "status", value: "waiting" }));
						const response = await request.post("/api/token", payload);
						if (response.status === 200) {
							dispatch(userStateChange({ key: "errors", value: null }));
						}
						return response;
					},
					stateChangeAction: userStateChange,
				},
				dispatch
			);
		};

export const changePasswordAction =
  <T>(payload: T) =>
		async (dispatch: AppDispatch) => {
			return actionWrapper(
				{
					tryBlock: async () => {
						dispatch(userStateChange({ key: "status", value: "waiting" }));
						const response = await request.post("/api/change-password", payload);

						if (response.status === 200) {
							dispatch(userStateChange({ key: "errors", value: null }));
							if (response?.data?.data) {
								const isProduction = process.env.NODE_ENV === "production";
								const userInfo = response.data.data;

								setEncryptedCookie("_eticket_user_", JSON.stringify(userInfo), {
									secure: isProduction,
									sameSite: isProduction ? "None" : "Lax",
									domain: isProduction ? ".eticketnepal.com" : undefined,
									path: "/",
									expires: 365,
								});
							}
						}
						return response;
					},
					stateChangeAction: userStateChange,
				},
				dispatch
			);
		};

export const changeEmailAction =
  <T>(payload: T) =>
		async (dispatch: AppDispatch) => {
			return actionWrapper(
				{
					tryBlock: async () => {
						dispatch(userStateChange({ key: "status", value: "waiting" }));
						const response = await request.post("/api/change-email", payload);
						if (response.status === 200) {
							dispatch(userStateChange({ key: "errors", value: null }));
							const { tokens, userInfo } = response.data.data;
							const isProduction = process.env.NODE_ENV === "production";
							setCookie("_eticket_at_", tokens.accessToken, {
								secure: isProduction, // HTTPS only in production
								sameSite: isProduction ? "None" : "Lax", // Cross-origin in production
								domain: isProduction ? ".eticketnepal.com" : undefined, // Domain for production
								path: "/", // Accessible across the entire domain
								expires: 365, // Expires in days
							});
							setCookie("_eticket_rt_", tokens.refreshToken, {
								secure: isProduction,
								sameSite: isProduction ? "None" : "Lax",
								domain: isProduction ? ".eticketnepal.com" : undefined,
								path: "/",
								expires: 365,
							});
							setEncryptedCookie("_eticket_user_", JSON.stringify(userInfo), {
								secure: isProduction,
								sameSite: isProduction ? "None" : "Lax",
								domain: isProduction ? ".eticketnepal.com" : undefined,
								path: "/",
								expires: 365,
							});
							return response;
						}
						return response;
					},
					stateChangeAction: userStateChange,
				},
				dispatch
			);
		};

export const forgotPasswordAction =
	(email: string) => async (dispatch: AppDispatch) => {
		return actionWrapper(
			{
				tryBlock: async () => {
					dispatch(userStateChange({ key: "status", value: "waiting" }));
					const response = await request.post("/api/forgot-password", {
						email,
					});
					if (response.status === 200) {
						dispatch(userStateChange({ key: "errors", value: null }));
					}
					return response;
				},
				stateChangeAction: userStateChange,
			},
			dispatch
		);
	};

export const resetPasswordAction =
	(
		email: string,
		code: string,
		password: string,
		password_confirmation: string
	) =>
		async (dispatch: AppDispatch) => {
			return actionWrapper(
				{
					tryBlock: async () => {
						dispatch(userStateChange({ key: "status", value: "saving" }));
						const response = await request.post("/api/reset-password", {
							email,
							code,
							password,
							password_confirmation,
						});
						if (response.status === 200) {
							dispatch(userStateChange({ key: "errors", value: null }));
						}
						return response;
					},
					stateChangeAction: userStateChange,
				},
				dispatch
			);
		};

export const subscribeToNewsletterAction =
	(email: string) => async (dispatch: AppDispatch) => {
		return actionWrapper(
			{
				tryBlock: async () => {
					dispatch(userStateChange({ key: "status", value: "saving" }));
					const response = await request.post("/api/subscribe", { email });
					if (response.status === 200) {
						dispatch(userStateChange({ key: "errors", value: null }));
					}
					return response;
				},
				stateChangeAction: userStateChange,
			},
			dispatch
		);
	};

export const updateProfileImageAction =
	(image: string) => async (dispatch: AppDispatch) => {
		return actionWrapper(
			{
				tryBlock: async () => {
					dispatch(userStateChange({ key: "status", value: "waiting" }));
					const response = await request.post("/api/profile-image", { image });
					if (response.status === 200) {
						dispatch(userStateChange({ key: "errors", value: null }));
						const user = getEncryptedCookie("_eticket_user_") as UserModel;
						if (user) {
							user.image = image;
							const isProduction = process.env.NODE_ENV === "production";
							// Save the updated user data back into the cookie
							setEncryptedCookie("_eticket_user_", JSON.stringify(user), {
								secure: isProduction,
								sameSite: isProduction ? "None" : "Lax",
								domain: isProduction ? ".eticketnepal.com" : undefined,
								path: "/",
								expires: 365,
							});
						}
					}
					return response;
				},
				stateChangeAction: userStateChange,
			},
			dispatch
		);
	};

export const updatePersonalInfoAction =
	(data: UpdatePersonalInfoType) => async (dispatch: AppDispatch) => {
		return actionWrapper(
			{
				tryBlock: async () => {
					dispatch(userStateChange({ key: "status", value: "saving" }));
					const response = await request.put("/api/user", data);
					if (response.status === 200) {
						dispatch(userStateChange({ key: "errors", value: null }));
						if (response?.data?.data) {
							const isProduction = process.env.NODE_ENV === "production";
							const userInfo = response.data.data;

							setEncryptedCookie("_eticket_user_", JSON.stringify(userInfo), {
								secure: isProduction,
								sameSite: isProduction ? "None" : "Lax",
								domain: isProduction ? ".eticketnepal.com" : undefined,
								path: "/",
								expires: 365,
							});
						}
					}
					return response;
				},
				stateChangeAction: userStateChange,
			},
			dispatch
		);
	};

export const fetchAllUserLogs =
	(
		payload: ActionQueryPayload & {
			name?: string | null;
			log_action: string | null;
		}
	) =>
		async (dispatch: AppDispatch) => {
			try {
				dispatch(userStateChange({ key: "status", value: "fetching" }));
				// await new Promise(resolve => setTimeout(resolve, 5000));
				const response = await request.get("/api/users-log", {
					params: {
						...constructQuery(payload),
						name: payload.name,
						log_action: payload.log_action,
					},
				});
				if (response.status === 200) {
					dispatch(userStateChange({ key: "userLogs", value: response.data.rows }));
					dispatch(userStateChange({ key: "meta", value: response.data.meta }));
					dispatch(userStateChange({ key: "errors", value: null }));
				}
				return response;
			} catch (error) {
				console.error("Error fetching logs:", error);
				dispatch(userStateChange({ key: "errors", value: error }));
			} finally {
				dispatch(userStateChange({ key: "status", value: "idle" }));
			}
		};

export const fetchUserLogsAction = () => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(userStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/user/logs');
				if (response.status === 200) {
					dispatch(userStateChange({ key: 'userLogs', value: response.data }));
					dispatch(userStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: userStateChange
		},
		dispatch
	);
}

export const subscribeOrUnsubscribeEventAction = (id: number, subscribe: boolean) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(userStateChange({ key: 'status', value: 'saving' }));
				const response = await request.post(`/api/event-subscriptions/${id}`, { subscribe });
				if (response.status === 200) {
					dispatch(userStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: userStateChange
		},
		dispatch
	);
};

export const fetchSubscribedEventsAction = (payload: ActionQueryPayload) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(userStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/event-subscriptions', { params: constructQuery(payload) });
				if (response.status === 200) {
					dispatch(userStateChange({ key: 'subscribedEvents', value: response.data.rows }));
					dispatch(userStateChange({ key: 'meta', value: response.data.meta }));
					dispatch(userStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: userStateChange
		},
		dispatch
	);
};

export const fetchUserPreferencesAction = () => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(userStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/user/preferences');
				if (response.status === 200) {
					dispatch(userStateChange({ key: 'preferences', value: response.data }));
					dispatch(userStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: userStateChange
		},
		dispatch
	);
};

export const updateUserPreferencesAction = <T>(payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(userStateChange({ key: 'status', value: 'saving' }));
				const response = await request.put('/api/user/preferences', payload);
				if (response?.status === 200) {
					dispatch(fetchUserPreferencesAction());
					dispatch(userStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: userStateChange
		},
		dispatch
	);
};

export const fetchBookedEventsAction = (payload: ActionQueryPayload) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(userStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/user/purchase-infos', { params: constructQuery(payload) });
				if (response.status === 200) {
					dispatch(userStateChange({ key: 'purchaseInfos', value: response.data.rows }));
					dispatch(userStateChange({ key: 'meta', value: response.data.meta }));
					dispatch(userStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: userStateChange
		},
		dispatch
	);
};

export const downloadEventTicketAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(userStateChange({ key: 'status', value: 'downloading' }));
				const response = await request.get(`/api/ticket-pdf/${id}`, { responseType: 'blob' });
				if (response.status === 200) {
					dispatch(userStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: userStateChange
		},
		dispatch
	);
};
export default userSlice.reducer;
