/* eslint-disable @typescript-eslint/no-explicit-any */
import { ServerStatus, PaginationMeta, ResponseErrorType, ActionQueryPayload } from "@/types/index.types";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import { actionWrapper } from "@/utils/redux.util";
import request from "@/utils/request.util";
import { constructQuery } from "@/utils/general.util";
import { TicketBuyerModel } from "@/types/models/ticket-buyer.model";
import { ActiveTicketSessionModel } from "@/types/models/active-ticket-session.model";

type PurchasesState = {
	status: ServerStatus;
	rows: TicketBuyerModel[];
	meta: PaginationMeta | null;
	ticketSession: ActiveTicketSessionModel | null;
	errors?: ResponseErrorType | null;
}

const initialState: PurchasesState = {
	status: 'idle',
	rows: [],
	meta: null,
	ticketSession: null,
}

const purchasesSlice = createSlice({
	name: 'purchases',
	initialState,
	reducers: {
		purchasesStateChange: (state, { payload }) => {
			const { key, value }: {key: keyof PurchasesState, value: any} = payload;
			(state as Record<keyof PurchasesState, any>)[key] = value;
		}
	}
})

export const { purchasesStateChange } = purchasesSlice.actions;

export const fetchEventPurchasesAction = (payload: ActionQueryPayload & { id: number }) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(purchasesStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get(`/api/event-purchases/${payload.id}`, { params: constructQuery(payload) });
				if (response.status === 200) {
					dispatch(purchasesStateChange({ key: 'rows', value: response.data.rows }));
					dispatch(purchasesStateChange({ key: 'meta', value: response.data.meta }));
					dispatch(purchasesStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: purchasesStateChange
		},
		dispatch
	);
}

export const downloadPurchasesExcelAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(purchasesStateChange({ key: 'status', value: 'downloading' }));
				const response = await request.get(`/api/event-purchases/excel/${id}`, { responseType: 'blob' });
				if (response.status === 200) {
					dispatch(purchasesStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: purchasesStateChange
		},
		dispatch
	);
};

export const checkTicketAvailabilityAction = (event_id: number, ticket_id: number, ticket_count: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(purchasesStateChange({ key: 'status', value: 'waiting' }));
				const payload = { event_id, ticket_id, ticket_count };
				const response = await request.post('/api/check-ticket-availability', payload);

				if (response.status === 200) {
					dispatch(purchasesStateChange({ key: 'ticketSession', value: response.data }));
					dispatch(purchasesStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: purchasesStateChange
		},
		dispatch
	);
};

export const fetchActiveTicketSessionAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(purchasesStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get(`/api/active-ticket-session/${id}`);
				if (response.status === 200) {
					dispatch(purchasesStateChange({ key: 'ticketSession', value: response.data }));
					dispatch(purchasesStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: purchasesStateChange
		},
		dispatch
	);
}

export default purchasesSlice.reducer;
