/* eslint-disable @typescript-eslint/no-explicit-any */
import { ActionQueryPayload, PaginationMeta, ResponseErrorType, ServerStatus } from "@/types/index.types";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import request from "@/utils/request.util";
import { CategoryModel } from "@/types/models/category.model";
import { actionWrapper } from "@/utils/redux.util";
import { constructQuery } from "@/utils/general.util";

type CategoryState = {
	status: ServerStatus;
	rows: CategoryModel[];
	record: CategoryModel | null;
	navigationList: CategoryModel[];
	meta: PaginationMeta | null;
	errors?: ResponseErrorType | null;
}

const initialState: CategoryState = {
	status: 'idle',
	rows: [],
	record: null,
	navigationList: [],
	meta: null,
	errors: null
};

const categorySlice = createSlice({
	name: 'category',
	initialState,
	reducers: {
		categoryStateChange: (state, { payload }) => {
			const { key, value }: {key: keyof CategoryState, value: any} = payload;
			(state as Record<keyof CategoryState, any>)[key] = value;
		}
	}
})

export const { categoryStateChange } = categorySlice.actions;

export const fetchAllCategoriesAction = (payload?: ActionQueryPayload) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(categoryStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/categories', { params: constructQuery(payload) });
				if (response.status === 200) {
					if (payload?.getNavCategories) dispatch(categoryStateChange({ key: 'navigationList', value: response.data.rows }))
					else dispatch(categoryStateChange({ key: 'rows', value: response.data.rows }))
					dispatch(categoryStateChange({ key: 'meta', value: response.data.meta }))
					dispatch(categoryStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: categoryStateChange
		},
		dispatch
	);
}

export const createCategoryAction = <T>(payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(categoryStateChange({ key: 'status', value: 'saving' }));
				const response = await request.post('/api/categories', payload);
				if (response.status === 201) {
					dispatch(categoryStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: categoryStateChange
		},
		dispatch
	);
};

export const fetchCategoryAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(categoryStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get(`/api/categories/${id}`);
				if (response.status === 200) {
					dispatch(categoryStateChange({ key: 'record', value: response.data }))
					dispatch(categoryStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: categoryStateChange
		},
		dispatch
	);
};

export const updateCategoryAction = <T>(id: number, payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(categoryStateChange({ key: 'status', value: 'saving' }));
				const response = await request.put(`/api/categories/${id}`, payload);
				if (response.status === 200) {
					dispatch(categoryStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: categoryStateChange
		},
		dispatch
	);
};

export const deleteCategoryAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(categoryStateChange({ key: 'status', value: 'deleting' }));
				const response = await request.delete(`/api/categories/${id}`);
				if (response.status === 200) {
					dispatch(categoryStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: categoryStateChange
		},
		dispatch
	);
};

export const updateCatStatusOrNavListAction = (id: number, { status, navigation_list }: { status?: boolean, navigation_list?: boolean }) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(categoryStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/categories/${id}`, { status, navigation_list });
				if (response.status === 200) {
					dispatch(categoryStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: categoryStateChange
		},
		dispatch
	);
};

export default categorySlice.reducer;
