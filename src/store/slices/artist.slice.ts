/* eslint-disable @typescript-eslint/no-explicit-any */
import { ActionQueryPayload, PaginationMeta, ResponseErrorType, ServerStatus } from "@/types/index.types";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import request from "@/utils/request.util";
import { ArtistModel } from "@/types/models/artist.model";
import { actionWrapper } from "@/utils/redux.util";
import { constructQuery } from "@/utils/general.util";

type ArtistState = {
	status: ServerStatus;
	rows: ArtistModel[];
	record?: ArtistModel | null;
	meta: PaginationMeta | null;
	errors?: ResponseErrorType | null;
}

const initialState: ArtistState = {
	status: 'idle',
	rows: [],
	record: null,
	meta: null,
	errors: null
};

const artistSlice = createSlice({
	name: 'artist',
	initialState,
	reducers: {
		artistStateChange: (state, { payload }) => {
			const { key, value }: {key: keyof ArtistState, value: any} = payload;
			(state as Record<keyof ArtistState, any>)[key] = value;
		}
	}
})

export const { artistStateChange } = artistSlice.actions;

export const fetchAllArtistsAction = (payload?: ActionQueryPayload) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(artistStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/artists', { params: constructQuery(payload) });
				if (response.status === 200) {
					dispatch(artistStateChange({ key: 'rows', value: response.data.rows }))
					dispatch(artistStateChange({ key: 'meta', value: response.data.meta }))
					dispatch(artistStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: artistStateChange
		},
		dispatch
	);
}

export const fetchArtistAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(artistStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get(`/api/artists/${id}`);
				if (response.status === 200) {
					dispatch(artistStateChange({ key: 'record', value: response.data }))
					dispatch(artistStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: artistStateChange
		},
		dispatch
	);
};

export const createArtistAction = <T>(payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(artistStateChange({ key: 'status', value: 'saving' }));
				const response = await request.post('/api/artists', payload);
				if (response.status === 201) {
					dispatch(artistStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: artistStateChange
		},
		dispatch
	);
};

export const updateArtistAction = <T>(id: number, payload: T) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(artistStateChange({ key: 'status', value: 'saving' }));
				const response = await request.put(`/api/artists/${id}`, payload);
				if (response.status === 200) {
					dispatch(artistStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: artistStateChange
		},
		dispatch
	);
};

export const deleteArtistAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(artistStateChange({ key: 'status', value: 'deleting' }));
				const response = await request.delete(`/api/artists/${id}`);
				if (response.status === 200) {
					dispatch(artistStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: artistStateChange
		},
		dispatch
	);
};

export const toggleArtistStatusAction = (id: number, status: boolean) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(artistStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/artists/${id}`, { status });
				if (response.status === 200) {
					dispatch(artistStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: artistStateChange
		},
		dispatch
	);
};

export default artistSlice.reducer;
