/* eslint-disable @typescript-eslint/no-explicit-any */
import { ActionQueryPayload, PaginationMeta, ResponseErrorType, ServerStatus } from "@/types/index.types";
import { createSlice } from "@reduxjs/toolkit";
import { AppDispatch } from "..";
import request from "@/utils/request.util";
import { EventModel } from "@/types/models/event.model";
import { constructQuery } from "@/utils/general.util";
import { actionWrapper } from "@/utils/redux.util";
import { EventTicketModel } from "@/types/models/event-ticket.model";

type EventState = {
	status: ServerStatus;
	success: boolean;
	rows: EventModel[];
	searchResult: EventModel[];
	featuredEvents: {
		data: EventModel[],
		count: number
	};
	sliderEvents: {
		data: EventModel[],
		count: number
	};
	tickets: {
		data: EventTicketModel[],
		count: number
	}
	record: EventModel | null;
	processingId: number | null;
	meta: PaginationMeta | null;
	errors?: ResponseErrorType | null;
}

const initialState: EventState = {
	status: 'idle',
	success: false,
	rows: [],
	searchResult: [],
	featuredEvents: {
		data: [],
		count: 0,
	},
	sliderEvents: {
		data: [],
		count: 0,
	},
	tickets: {
		data: [],
		count: 0,
	},
	record: null,
	processingId: null,
	meta: null,
	errors: null
};

const eventSlice = createSlice({
	name: 'event',
	initialState,
	reducers: {
		eventStateChange: (state, { payload }) => {
			const { key, value }: {key: keyof EventState, value: any} = payload;
			(state as Record<keyof EventState, any>)[key] = value;
		}
	}
})

export const { eventStateChange } = eventSlice.actions;

export const addEventAction = <T>(payload: T, id?: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'saving' }));
				const response = id ? await request.put(`/api/events/${id}`, payload) : await request.post('/api/events', payload);
				if (response.status === 201) {
					dispatch(eventStateChange({ key: 'success', value: true }));
					dispatch(eventStateChange({ key: 'errors', value: null }));
					dispatch(eventStateChange({ key: 'processingId', value: response.data.data.id }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
}

export const fetchEventAction = (idOrSlug: number | string) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get(`/api/events/${idOrSlug}`);
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'success', value: true }));
					dispatch(eventStateChange({ key: 'errors', value: null }));
					dispatch(eventStateChange({ key: 'record', value: response.data }));
					dispatch(eventStateChange({ key: 'processingId', value: response.data.id }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
}

export const fetchAllEventsAction = (
	payload?: ActionQueryPayload & { searching?: boolean, infiniteScroll?: boolean }
) => async (dispatch: AppDispatch, getState: () => { event: EventState }) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				if (payload?.searching && !payload?.search) {
					dispatch(eventStateChange({ key: 'searchResult', value: []}))
					return;
				}
				dispatch(eventStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/events', { params: constructQuery(payload) });
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'success', value: true }));
					dispatch(eventStateChange({ key: 'errors', value: null }));
					if (payload?.searching) dispatch(eventStateChange({ key: 'searchResult', value: response.data.rows }))
					else	{
						if (payload?.infiniteScroll) {
							const { rows: currentRows } = getState().event;
							const updatedRows = [...currentRows, ...response.data.rows];
							dispatch(eventStateChange({ key: 'rows', value: updatedRows }));
						} else {
							dispatch(eventStateChange({ key: 'rows', value: response.data.rows }));
						}
						dispatch(eventStateChange({ key: 'meta', value: response.data.meta }));
					}
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
};

export const fetchAllProtectedEventsAction = (payload?: ActionQueryPayload) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/protected/events', { params: constructQuery(payload) });
				if (response && response.status === 200) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
					dispatch(eventStateChange({ key: 'rows', value: response.data.rows }));
					dispatch(eventStateChange({ key: 'meta', value: response.data.meta }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
};

export const deleteEventAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'deleting' }));
				const response = await request.delete(`/api/events/${id}`);
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
};

export const setEventAsSliderAction = (id: number, fromDate: string, toDate: string) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/events-slider/${id}`, { fromDate, toDate });
				if (response.status === 201) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
}

export const setEventAsFeaturedAction = (id: number, fromDate: string, toDate: string) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/events-featured/${id}`, { fromDate, toDate });
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
}

export const fetchFeaturedEventsAction = (payload?: ActionQueryPayload) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/events-featured', { params: constructQuery(payload) });
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'success', value: true }));
					dispatch(eventStateChange({ key: 'errors', value: null }));

					if (payload?.getCount) {
						dispatch(eventStateChange({ key: 'featuredEvents', value: {
							data: [],
							count: response.data.count,
						}}))
					} else {
						dispatch(eventStateChange({ key: 'featuredEvents', value: {
							data: response.data.rows,
							count: response.data.meta.total,
						}}))
						dispatch(eventStateChange({ key: 'meta', value: response.data.meta }))
					}
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
}

export const fetchSliderEventsAction = (payload?: ActionQueryPayload) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'fetching' }));
				const response = await request.get('/api/events-slider', { params: constructQuery(payload) });
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'success', value: true }));
					dispatch(eventStateChange({ key: 'errors', value: null }));

					if (payload?.getCount) {
						dispatch(eventStateChange({ key: 'sliderEvents', value: {
							data: [],
							count: response.data.count,
						}}))
					} else {
						dispatch(eventStateChange({ key: 'sliderEvents', value: {
							data: response.data.rows,
							count: response.data.count,
						}}))
					}
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
}

export const toggleEventStatusAction = (id: number, status: boolean) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/events/${id}`, { status });
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
};

export const likeOrUnlikeEventAction = (id: number, like: boolean) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/events-likes/${id}`, { like });
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
};

export const verifyEsewaPayment = <T>(payload: T) => async (dispatch: AppDispatch) => {
	try {
		dispatch(eventStateChange({ key: 'status', value: 'saving' }));
		const response = await request.post('/api/verify-esewa-status', payload);
		if (response.status === 201) {
			dispatch(eventStateChange({ key: 'success', value: true }));
			dispatch(eventStateChange({ key: 'errors', value: null }));
		}
		return response;
	} catch (error: any) {
		dispatch(eventStateChange({ key: 'errors', value: { status: error.response.status, message: error.response.data.message }}));
		return Promise.reject(error.response.data.message);
	} finally {
		dispatch(eventStateChange({ key: 'status', value: 'idle' }));
	}
}

export const khaltiPayment = <T>(payload: T) => async (dispatch: AppDispatch) => {
	try {
		dispatch(eventStateChange({ key: 'status', value: 'saving' }));
		const response = await request.post('/api/khalti-payment', payload);
		if (response.status === 201) {
			dispatch(eventStateChange({ key: 'success', value: true }));
			dispatch(eventStateChange({ key: 'errors', value: null }));
		}
		return response;
	} catch (error: any) {
		dispatch(eventStateChange({ key: 'errors', value: { status: error.response.status, message: error.response.data.message }}));
		return Promise.reject(error.response.data.message);
	} finally {
		dispatch(eventStateChange({ key: 'status', value: 'idle' }));
	}
}
export const verifyKhaltiPayment = <T>(payload: T) => async (dispatch: AppDispatch) => {
	try {
		dispatch(eventStateChange({ key: 'status', value: 'saving' }));
		const response = await request.post('/api/verify-khalti-payment', payload);
		if (response.status === 201) {
			dispatch(eventStateChange({ key: 'success', value: true }));
			dispatch(eventStateChange({ key: 'errors', value: null }));
		}
		return response;
	} catch (error: any) {
		dispatch(eventStateChange({ key: 'errors', value: { status: error.response.status, message: error.response.data.message }}));
		return Promise.reject(error.response.data.message);
	} finally {
		dispatch(eventStateChange({ key: 'status', value: 'idle' }));
	}
}

export const resendEventTicketEmailAction = (buyerId: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'waiting' }));
				const response = await request.post(`/api/resend-ticket-email/${buyerId}`);
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
}

export const approveEventAction = (id: number) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/events-approve/${id}`);
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
};

export const rejectEventAction = (id: number, reason: string) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/events-reject/${id}`, { reason });
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
};

export const cancelEventAction = (id: number, reason: string) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/events-cancel/${id}`, { reason });
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
};

export const delayEventAction = (id: number, startDate: string, endDate: string) => async (dispatch: AppDispatch) => {
	return actionWrapper(
		{
			tryBlock: async () => {
				dispatch(eventStateChange({ key: 'status', value: 'saving' }));
				const response = await request.patch(`/api/events-delay/${id}`, { startDate, endDate });
				if (response.status === 200) {
					dispatch(eventStateChange({ key: 'errors', value: null }));
				}
				return response;
			},
			stateChangeAction: eventStateChange
		},
		dispatch
	);
};

export default eventSlice.reducer;
