import { Action, combineReducers, configureStore, ThunkAction } from "@reduxjs/toolkit";
import partnerReducer from "@/store/slices/partner.slice";
import userReducer from "@/store/slices/user.slice";
import eventReducer from "@/store/slices/event.slice";
import categoryReducer from "@/store/slices/category.slice";
import artistReducer from "@/store/slices/artist.slice";
import venueReducer from "@/store/slices/venue.slice";
import appReducer from "@/store/slices/app.slice";
import topicReducer from "@/store/slices/topic.slice";
import generalInfoReducer from "@/store/slices/general-info.slice";
import purchasesReducer from "@/store/slices/purchases.slice";

export function makeStore() {
	return configureStore({
		reducer: combineReducers({
			app: appReducer,
			partner: partnerReducer,
			user: userReducer,
			event: eventReducer,
			category: categoryReducer,
			artist: artistReducer,
			venue: venueReducer,
			topic: topicReducer,
			generalInfo: generalInfoReducer,
			purchases: purchasesReducer,
		}),
	})
}

export const store = makeStore();
export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
export type AppThunk<ReturnType = void> = ThunkAction<ReturnType, RootState, null, Action<string>>;
