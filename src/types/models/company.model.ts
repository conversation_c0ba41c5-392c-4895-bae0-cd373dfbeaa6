import { AddressData } from "../index.types";

export type CompanyModel = {
	id: number;
	code: string;
	name: string;
	domain?: string | null;
	socials?: JSON | null;
	logo?: string | null;
	email?: string | null;
	phone?: string | null;
	secondary_contacts?: JSON | null;
	address?: AddressData | null;
	other_information?: JSON | null;
	about?: string | null;
	status: boolean;
	created_at: string;
	updated_at: string;
}
