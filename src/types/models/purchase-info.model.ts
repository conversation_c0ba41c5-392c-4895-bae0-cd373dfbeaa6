import { EventModel } from "./event.model";
import { TicketBuyerModel } from "./ticket-buyer.model";
import { VenueModel } from "./venue.model";

export type PurchaseInfoModel = {
	id: number;
	event_id: number;
	venue_id: number;
	ticket_id: number;
	ticket_buyer_id: number;
	date_id: number | null;
	user_id: number;
	date_time_id: number | null;
	is_scan_success?: boolean;
	paid_amount?: number | null;
	created_at: string;
	updated_at: string;
	event?: EventModel;
	venue?: VenueModel;
	ticket_buyer?: TicketBuyerModel;
}
