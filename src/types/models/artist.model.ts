import { EventModel } from "./event.model";

export type ArtistModel = {
	id: number;
	name: string;
	title: string;
	stage_name?: string | null;
	band_name?: string | null;
	image?: string | null;
	dob?: string | null;
	home_town?: string | null;
	about: string;
	status: boolean;
	meta_keywords?: string | null;
	meta_desc?: string | null;
	created_at: string;
	updated_at: string;
	events?: EventModel[];
}
