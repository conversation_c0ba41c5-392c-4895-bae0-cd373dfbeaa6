import { CompanyModel } from "./company.model";
import { UserCategoryPreference } from "./user-category-preference.model";
import { UserPreference } from "./user-preference.model";

export type UserModel = {
	id: number;
	f_name?: string | null;
	l_name?: string | null;
	email?: string | null;
	phone?: string | null;
	image?: string | null;
	ip_address?: string | null;
	dob?: string | null;
	gender?: string | null;
	password?: string;
	verification_code?: string | null;
	action_link_sent_at?: string | null;
	email_verified_at?: string | null;
	phone_verified_at?: string | null;
	last_logged_in_at?: string | null;
	email_updated_at?: string | null;
	password_updated_at?: string | null;
	is_partner?: boolean;
	user_type: string;
	companies?: CompanyModel[];
	user_preference?: UserPreference;
	user_category_preferences?: UserCategoryPreference[];
}
