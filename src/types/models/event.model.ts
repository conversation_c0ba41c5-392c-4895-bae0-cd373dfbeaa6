import { ArtistModel } from "./artist.model";
import { CategoryModel } from "./category.model";
import { EventActivityModel } from "./event-activity.model";
import { EventDateModel } from "./event-date.model";
import { EventFaqModel } from "./event-faq.model";
import { EventGalleryModel } from "./event-gallery.model";
// import { EventLikeModel } from "./event-like.model";
import { EventSliderModel } from "./event-slider.model";
import { EventTermModel } from "./event-term.model";
import { EventTicketModel } from "./event-ticket.model";
import { VenueModel } from "./venue.model";

export type EventModel = {
  id: number;
	company_id?: number | null;
  title?: string | null;
  slug?: string | null;
  duration?: string | null;
	main_attraction?: string | null;
  image?: string | null;
	about?: string | null;
  status: boolean;
  feature_from?: string | null;
  feature_expiry?: string | null;
  meta_keywords?: string | null;
  meta_desc?: string | null;
  created_at: string;
  published_at?: string | null;
  updated_at: string;
  deleted_at?: string | null;
  start_date_time?: string | null;
  end_date_time?: string | null;
  approved_at?: string | null;
  categories?: CategoryModel[];
  artists?: ArtistModel[];
  venues?: VenueModel[];
  terms?: EventTermModel[];
  tickets?: EventTicketModel[];
  dates?: EventDateModel[];
  faqs?: EventFaqModel[];
  sliders?: EventSliderModel[];
  galleries?: EventGalleryModel[];
  activities?: EventActivityModel[];
  // likes?: EventLikeModel[];
  is_liked?: boolean;
  likes_count?: number;
  is_subscribed?: boolean;
  rejected_at?: string | null;
  reject_reason?: string | null;
  cancelled_at?: string | null;
  cancel_reason?: string | null;
  is_delayed?: boolean | null;
  external_ticket_link?: string | null;
};
