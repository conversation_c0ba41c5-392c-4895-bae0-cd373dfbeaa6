import { AddressData } from "../index.types";
import { EventModel } from "./event.model";

export type VenueModel = {
	id: number;
	name: string;
	slug: string;
	logo?: string | null;
	image?: string | null;
	city_id?: number | null;
	facebook?: string | null;
	youtube?: string | null;
	twitter?: string | null;
	instagram?: string | null;
	map_link?: string | null;
	phone_number?: string | null;
	mobile_number?: string | null;
	email?: string | null;
	website?: string | null;
	contact_info?: JSON | null;
	status: boolean;
	about?: string | null;
	attractions?: string | null;
	coordinates?: Record<string, any> | null;
	address?: AddressData | null;
	meta_keywords?: string | null;
	meta_desc?: string | null;
	created_at: string;
	updated_at: string;
	deleted_at?: string | null;
	distance_meters?: number;
	total_events_hosted?: number;
	events?: EventModel[];
};
