export type UserLogModel = {
    id?: number,
    user_id: number | null,
    company_id: number | null;
    event_type: string,
    type: boolean,

    action: string,
    details: string | null,
    ip_address: string,
    payloads: {[key: string]:any} | null,
    user_agent: string | null;
    referrer: string | null;
    method: string;
    api_endpoint: string;
    read_access: boolean,
    result: boolean,
    user: {
        id: number,
        f_name: string,
        l_name: string
    } | null,
    created_at: string
}


