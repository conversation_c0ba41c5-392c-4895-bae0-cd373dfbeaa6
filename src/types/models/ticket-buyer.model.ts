export type TicketBuyerModel = {
	id: number;
	user_id?: number | null;
	event_id?: number | null;
	full_name?: string;
	phone_number?: string;
	email?: string;
	has_processed?: boolean;
	was_payment_successful?: boolean;
	total_amount?: number | null;
	ticket_count?: number | null;
	payment_method?: string | null;
	payment_method_response?: JSON | null;
	download_link?: string | null;
	download_count?: string | null;
	created_at?: string;
	updated_at?: string;
}
