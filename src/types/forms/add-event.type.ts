import { FormikProps } from "formik";
import { CategoryModel } from "../models/category.model";

export type TicketInfoType = {
  count: string;
  amount: string;
}

export type VenueDataType = {
  ticket_type: 'free' | 'paid';
  // date_time: string;
  start_date_time: string;
  end_date_time: string;
  tickets?: {
    name: string;
    count: string;
    amount: string;
  }[];
}

export type FaqDataType = {
  question: string,
  answer: string,
}

export type ActivityDataType = {
  title: string;
  details: string;
}

export type AddEventType = {
  step_1_info: {
    title: string;
    // duration: string;
    categories: number[];
    about: string;
    cover_image: string;
  },
  step_2_info: {
    artists: number[];
    terms: string;
    galleries?: string[];
    faqs?: FaqDataType[];
    activities?: ActivityDataType[];
  },
  step_3_info: {
    use_external_ticket_link: boolean;
    external_ticket_link?: string;
    venues: number[];
    venue_data: Record<number | string, VenueDataType>;
  },
  is_published: boolean
};

export type AddEventProps = {
	formikProps: FormikProps<AddEventType>;
  categories?: CategoryModel[];
}
