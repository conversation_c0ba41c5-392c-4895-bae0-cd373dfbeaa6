export type ServerStatus = 'saving' | 'fetching' | 'idle' | 'deleting' | 'waiting' | 'downloading';

export type ResponseErrorType = {
	status?: number;
	message: string;
};

export type Errors<T> = {
  [K in keyof T]?: T[K] extends string | number | boolean | null | undefined
    ? string
    : T[K] extends Array<infer U>
    ? U extends string | number
      ? string // Array of strings or numbers maps to a string error
      : { [index: number]: Errors<U> } // Array of objects maps to an indexed error
    : Errors<T[K]>;
};

export type ImageInfo =  {
  name: string;
  size: number;
  type: string;
  src: string;
}

export type ImageCropperProps = {
  aspectRatio?: number;
  minDimension?: number;
  value?: string;
  onCropComplete?: (croppedImage: string, imageInfo?: ImageInfo) => void;
  // Render props for customization
  renderUploadTrigger: (openUpload: () => void) => React.ReactNode;
  renderPreview?: (
    // croppedImage: string,
    // imageInfo: ImageInfo | null,
    resetImage: () => void
  ) => React.ReactNode;
  acceptedFileTypes?: string;
  errorMessage?: string;
  isCircular?: boolean;
}

export type ActionQueryPayload = {
	page?: number,
	perPage?: number,
  type?: string,
  category?: string,
  search?: string,
  price?: number,
  date?: string,
  getMainCategories?: boolean,
  getCount?: boolean,
  getNavCategories?: boolean,
  getQas?: boolean,
  latitude?: number,
  longitude?: number,
}

export type PaginationMeta = {
  currentPage: number;
  perPage: number;
  total: number;
  lastPage: number;
  nextPage: number | null;
  prevPage: number | null;
}

export type BreadcrumbItem = {
  name: string;
  link: string;
};

export type AddressData = {
  street: string;
  city: string;
  country: string;
  full_address: string;
  description: string;
  zipCode: string;
  lat: number;
  lng: number;
  place_id: string;
  zone: string;
};

export type PartnerStats = {
  totalEvents: number;
  totalTicketsSold: number;
}
