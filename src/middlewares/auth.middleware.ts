import { Company, User } from '@/models';
import { getDecodedUser } from '@/utils/general.util';
import { type Request, type Response, NextFunction } from 'express';

const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
	try {
		// const authToken = req.headers.cookie && req.headers.cookie.split('; ').find((cookie) => cookie.startsWith('_eticket_at_='))?.split('=')[1];

		// if (!authToken) return res.unauthorized('Unauthorized Request');

		// const decodedUser = jwt.verify(authToken, envUtil.JWT_SALT) as Partial<User>;
		const decodedUser = getDecodedUser(req);

		if (!decodedUser) return res.unauthorized('Unauthorized Request');
		if (decodedUser.is_partner && (!decodedUser.companies || decodedUser?.companies?.length === 0))
			return res.unauthorized('Unauthorized Request. Partner must have a company.');
		if (decodedUser.user_type !== 'normal' && decodedUser.user_type !== 'admin') {
			return res.unauthorized('Unauthorized Request. Invalid user type.');
		}
		const companyId = (decodedUser.is_partner || decodedUser.user_type === 'admin') ? decodedUser.companies![0]?.id : null;

		// Fetch the user and associated company only if needed
		const user = await User.findOne({
			where: { id: decodedUser.id, email: decodedUser.email },
			...(companyId
				? {
					include: [
						{
							model: Company,
							where: { id: companyId },
							required: false,
						},
					],
				}
				: {}),
		});

		if (!user) return res.unauthorized('Unauthorized Request. User not found');

		if (decodedUser.is_partner && (!user.companies || user.companies.length === 0))
			return res.unauthorized('Unauthorized Request. Company not associated with user or not available');

		const { companies, ...userData } = user.toJSON();

		req.user = userData;
		if (decodedUser.is_partner || decodedUser.user_type === 'admin') req.company = companies[0];
		next();

	} catch (error) {
		console.error(error);
		return res.unauthorized('Unauthorized Request');
	}
};

export default authMiddleware;
