import { Request, Response, NextFunction } from 'express';
import winston from 'winston';
import env from '@/utils/env.util';
const { combine, timestamp, label, printf } = winston.format;

const colors = {
	error: 'red',
	warn: 'yellow',
	info: 'green',
	http: 'magenta',
	debug: 'white',
};

const levels = {
	error: 0,
	warn: 1,
	info: 2,
	http: 3,
	debug: 4,
};

winston.addColors(colors);

const fileFormat = combine(
	label({ label: 'application' }),
	timestamp(),
	printf(({ level, message, label, timestamp }) => {
		return `${timestamp} [${label}] ${level}: ${message}`;
	})
);

const logger = winston.createLogger({
	level: 'http',
	levels,
	format: fileFormat,
	transports: [
		new winston.transports.Console({
			format: winston.format.combine(winston.format.colorize(), fileFormat),
		}),
	],
});

/**
 * Logging middleware for express to log all requests
 */
export const loggingMiddleware = (req: Request, res: Response, next: NextFunction) => {
	const { method, url, headers, protocol } = req;
	const { statusCode } = res;
	const ip = headers['x-forwarded-for'] || 'unknown address';
	const log = `${ip} - - "${method} ${url} ${protocol}" ${statusCode}`;
	if (env.NODE_ENV === 'local' || env.NODE_ENV === 'development') logger.http(log);
	next();
};
