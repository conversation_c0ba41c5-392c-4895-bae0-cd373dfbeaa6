import React, { useEffect, useRef, useState } from "react";
import ReactCrop, {
	centerCrop,
	makeAspectCrop,
	Crop,
	PixelCrop,
} from "react-image-crop";
import { Modal, Button } from "antd";
import { ImageInfo } from "@/types/index.types";

import 'react-image-crop/src/ReactCrop.scss';
import { ImageCropperProps } from "@/types/index.types";
import Compressor from "compressorjs";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { uploadImageAction } from "@/store/slices/app.slice";

// const ASPECT_RATIO = 16/9;
const ASPECT_RATIO = 2/1;
const MIN_DIMENSION = 300;

const setCanvasPreview = (
	image: HTMLImageElement,
	canvas: HTMLCanvasElement,
	crop: PixelCrop
) => {
	const ctx = canvas.getContext("2d");
	if (!ctx) {
		throw new Error("No 2d context");
	}

	const pixelRatio = window.devicePixelRatio;
	const scaleX = image.naturalWidth / image.width;
	const scaleY = image.naturalHeight / image.height;

	canvas.width = Math.floor(crop.width * scaleX * pixelRatio);
	canvas.height = Math.floor(crop.height * scaleY * pixelRatio);

	ctx.scale(pixelRatio, pixelRatio);
	ctx.imageSmoothingQuality = "high";
	ctx.save();

	const cropX = crop.x * scaleX;
	const cropY = crop.y * scaleY;

	ctx.translate(-cropX, -cropY);
	ctx.drawImage(
		image,
		0,
		0,
		image.naturalWidth,
		image.naturalHeight,
		0,
		0,
		image.naturalWidth,
		image.naturalHeight
	);

	ctx.restore();
};

const ImageCropper: React.FC<ImageCropperProps> = ({
	value,
	aspectRatio = ASPECT_RATIO,
	isCircular = false,
	minDimension = MIN_DIMENSION,
	onCropComplete,
	renderUploadTrigger,
	renderPreview,
	acceptedFileTypes = 'image/x-png,image/gif,image/jpeg',
	errorMessage
}) => {
	const dispatch = useDispatch<AppDispatch>();
	const imgRef = useRef<HTMLImageElement>(null);
	const uploadInput = useRef<HTMLInputElement>(null);
	const previewCanvasRef = useRef<HTMLCanvasElement>(null);
	const [imageData, setImageData] = useState<ImageInfo | null>(null);
	const [crop, setCrop] = useState<Crop>();
	const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
	const [error, setError] = useState<string>("");
	const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
	const [loading, setLoading] = useState<boolean>(false);

	useEffect(() => {
		if (errorMessage) setError(errorMessage);
		else setError("");
	}, [errorMessage]);

	const openUpload = () => {
		if (uploadInput.current) {
			uploadInput.current.click();
		}
	};

	const resetImage = () => {
		setImageData(null);
		if (uploadInput.current) {
			uploadInput.current.value = "";
		}
		onCropComplete?.("");
	};

	const onSelectFile = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (!file) return;

		const reader = new FileReader();
		reader.addEventListener("load", () => {
			const imageElement = new Image();
			const imageUrl = reader.result?.toString() || "";
			imageElement.src = imageUrl;

			imageElement.addEventListener("load", (e) => {
				if (error) setError("");
				const { naturalWidth, naturalHeight } = e.currentTarget as HTMLImageElement;
				if (naturalWidth < minDimension || naturalHeight < minDimension) {
					setError(`Image must be at least ${minDimension} x ${minDimension} pixels.`);
					return setImageData(null);
				}

				setImageData({
					name: file.name,
					size: file.size,
					type: file.type,
					src: imageUrl,
				});

				setIsModalOpen(true);
			});
		});
		reader.readAsDataURL(file);
	};

	const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
		const { width, height } = e.currentTarget;
		const cropWidthInPercent = (minDimension / width) * 100;

		const crop = makeAspectCrop(
			{
				unit: "%",
				width: cropWidthInPercent,
			},
			aspectRatio,
			width,
			height
		);
		const centeredCrop = centerCrop(crop, width, height);
		setCrop(centeredCrop);
		setCompletedCrop({
			unit: 'px',
			x: (centeredCrop.x / 100) * width,
			y: (centeredCrop.y / 100) * height,
			width: (centeredCrop.width / 100) * width,
			height: (centeredCrop.height / 100) * height,
		});
	};

	const cropImage = () => {
		if (previewCanvasRef.current && imageData) {
			previewCanvasRef.current.toBlob(
				(blob) => {
					if (blob) {
						new Compressor(blob, {
							quality: 0.4,
							success(result) {
								const fileSize = result.size;
								if (fileSize > 5 * 1024 * 1024) {
									setError('Cropped Image must be less than 5 MB');
									resetImage();
									setIsModalOpen(false);
								} else {
									const formData = new FormData();
									formData.append("file", result);
									setLoading(true);
									dispatch(uploadImageAction(formData))
										.then((res) => {
											if (res && res.status === 200) {
												onCropComplete?.(res.data?.Location)
												setIsModalOpen(false);
												setLoading(false);
											}
										})
										.catch((err) => {
											console.log(err)
											setError('Something went wrong. Please try again.');
											resetImage();
											setIsModalOpen(false);
											setLoading(false);
										})
										.finally(() => {
											if (uploadInput.current) {
												uploadInput.current.value = "";
											}
										})
								}
							}
						})
					}
				},
				imageData.type,
				0.5 // quality
			);
		}
	};

	useEffect(() => {
		if (
			completedCrop?.width &&
			completedCrop?.height
		) {
			const t = setTimeout(() => {
				if (imgRef.current && previewCanvasRef.current) {
					setCanvasPreview(
						imgRef.current,
						previewCanvasRef.current,
						completedCrop
					);
				}
			}, 100);

			return () => {
				clearTimeout(t);
			};
		}
	}, [completedCrop]);

	return (
		<>
			<Modal
				className='image-crop-modal'
				width={650}
				closable={false}
				maskClosable={false}
				title={null}
				open={isModalOpen}
				footer={[
					<Button size='middle' key="crop" type="primary" loading={loading} onClick={cropImage}>
            Crop Image
					</Button>,
					<Button style={{ marginLeft: 10 }} size='middle' key="cancel" disabled={loading}
						onClick={() => {
							if (uploadInput.current) {
								uploadInput.current.value = "";
							}
							setIsModalOpen(false);
						}}>
            Cancel
					</Button>
				]}
			>
				{imageData && (
					<div className="image-container">
						<ReactCrop
							crop={crop}
							onChange={(_, percentCrop) => setCrop(percentCrop)}
							onComplete={(c) => setCompletedCrop(c)}
							aspect={aspectRatio}
							minWidth={minDimension}
							circularCrop={isCircular}
						>
							<img
								ref={imgRef}
								src={imageData.src}
								alt="Upload"
								style={{ width: '100%' }}
								onLoad={onImageLoad}
								className=""
							/>
						</ReactCrop>
					</div>
				)}
			</Modal>

			{value && renderPreview ? (
				renderPreview(resetImage)
			) : (
				<>
					{renderUploadTrigger(openUpload)}
					<input
						ref={uploadInput}
						id='upload-image'
						onChange={onSelectFile}
						type="file"
						accept={acceptedFileTypes}
						style={{ display: 'none' }}
					/>
				</>
			)}

			{error && <span className="text-danger mb-4">{error}</span>}

			<canvas
				ref={previewCanvasRef}
				style={{
					display: "none",
				}}
			/>
		</>
	);
};

export default ImageCropper;
