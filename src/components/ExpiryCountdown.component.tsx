'use client'

import { useState, useEffect } from 'react'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

dayjs.extend(duration)

interface ExpiryCountdownProps {
  expiryTime: number,
	onExpiry?: () => void
}

export function ExpiryCountdown({ expiryTime, onExpiry }: ExpiryCountdownProps) {
	const [timeLeft, setTimeLeft] = useState(calculateTimeLeft())

	useEffect(() => {
		const timer = setInterval(() => {
			const updatedTimeLeft = calculateTimeLeft()
			setTimeLeft(updatedTimeLeft)

			if (updatedTimeLeft.asMilliseconds() <= 0) {
				clearInterval(timer)
				onExpiry?.()
			}
		}, 1000)

		return () => clearInterval(timer)
	}, [expiryTime])

	function calculateTimeLeft() {
		const now = dayjs()
		const expiry = dayjs(expiryTime)
		const diff = expiry.diff(now)
		return dayjs.duration(Math.max(0, diff))
	}

	if (timeLeft.asMilliseconds() <= 0) {
		<h4>Session Expired</h4>
	}

	return (
		<h4>
      Expires in : <span>{timeLeft.minutes().toString().padStart(2, '0')}:{timeLeft.seconds().toString().padStart(2, '0')}</span>
		</h4>
	)
}
