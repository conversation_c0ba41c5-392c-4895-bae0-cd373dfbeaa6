'use client';

import { AppDispatch, RootState } from "@/store";
import { registerPartnerAction } from "@/store/slices/partner.slice";
import { PartnerRegistrationType } from "@/types/forms/partner-registration.type";
import { CrownFilled, BankFilled, CameraFilled, CarFilled, HomeFilled, MailFilled, PhoneFilled, LockFilled } from "@ant-design/icons";
import { Button, Checkbox, Input } from "antd";
import { Formik, FormikErrors } from "formik";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import UserRegistrationSuccess from "./UserRegistrationSuccess";
import { partnerRegistrationValidation } from "@/utils/validation.util";
import AddressAutocomplete from "./AddressAutocomplete.component";

const PartnerRegistrationFlow = () => {
	const dispatch = useDispatch<AppDispatch>();
	const { registerSuccess, status } = useSelector((state: RootState) => state.partner);
	const [regCredential, setRegCredential] = useState<string>('');

	const [steps, setSteps] = useState<Record<number, string>>({
		1: 'active',
		2: 'inactive',
		3: 'inactive'
	});

	const [completedSteps, setCompletedSteps] = useState<Record<number, boolean>>({
		1: false,
		2: false,
		3: false
	});

	const categories = [
		{ key: 'event', title: 'Events', description: 'Organise / Host multiple events', icon: <CrownFilled /> },
		{ key: 'venue', title: 'Venues', description: 'Feature your awesome place', icon: <BankFilled /> },
		{ key: 'travel', title: 'Travels', description: 'Gather all travelers to you', icon: <CameraFilled /> },
		{ key: 'vehicle', title: 'Vehicles', description: 'Rent your vehicle to others', icon: <CarFilled /> },
		{ key: 'hotel', title: 'Hotels', description: 'Feature your hotels and stay.', icon: <HomeFilled /> },
	];

	const isStep2Valid = (errors: FormikErrors<PartnerRegistrationType>, values: PartnerRegistrationType) => {
		return (
			!errors.company?.name &&
			!errors.company?.phone &&
			!errors.company?.email &&
			!errors.company?.address &&
			values.company.name &&
			values.company.phone &&
			values.company.email &&
			values.company.address
		);
	};

	return (
		<>
			{registerSuccess ? <UserRegistrationSuccess regCredential={regCredential} /> : (
				<>
					<Formik<PartnerRegistrationType>
						enableReinitialize={true}
						initialValues={{
							partnerCategory: 'event',
							agreement: false,
							company: {
								name: '',
								phone: '',
								email: '',
								address: null,
							},
							auth: {
								email: '',
								password: '',
								password_confirmation: '',
							}
						}}
						validate={partnerRegistrationValidation}
						onSubmit={async (values, actions) => {
							setCompletedSteps(prev => ({ ...prev, 3: true }));
							const res = await dispatch(registerPartnerAction<PartnerRegistrationType>(values));
							if (res && res.status === 201) {
								setSteps({
									1: 'active',
									2: 'inactive',
									3: 'inactive',
								});
								setCompletedSteps({
									1: false,
									2: false,
									3: false,
								});
								setRegCredential(values.auth.email!);
								actions.resetForm();
							}
						}}
					>
						{({ values, setFieldValue, handleSubmit, errors, isValid }) => (
							<form onSubmit={handleSubmit}>
								<div className="login-input-box partner_registration_box">
									{steps[1] === 'active' && (
										<>
											<div>
												<h1>Become a Partner!</h1>
												<p className={'mb-4'}>
													Start your partner registration process by selecting the partner category type.
													Choose one from the given categories <i className="fa fa-info-circle"/>
												</p>
											</div>
											<div>
												<ul className={'partner_category_selector'}>
													{categories.map((category) => (
														<li key={category.key} className={category.key}>
															<a
																href=""
																className={values.partnerCategory === category.key ? 'active' : ''}
																onClick={(e) => {
																	e.preventDefault();
																	setFieldValue('partnerCategory', category.key)
																}}
															>
																<div className="img-holder">
																	{/* <img src="/public/static/img/foot_ball.jpg" alt=""/> */}
																</div>
																<div className="content-wrapper">
																	<h3>{category.title}</h3>
																	<p>{category.description}</p>
																	<span className={'footer_dec'}>
																		<span>{category.icon}</span>
																		<span className={'decorator'}>&nbsp;</span>
																	</span>
																</div>
															</a>
														</li>
													))}
												</ul>

												<br />
												<br />
												<hr />
												<br />
												<Button
													type={'primary'}
													onClick={() => {
														setCompletedSteps(prev => ({ ...prev, 1: true }));
														setSteps({
															1: 'inactive',
															2: 'active',
															3: 'inactive',
														});
													}}
													disabled={!values.partnerCategory}
													size="large"
												>
													Select Category & Proceed
												</Button>
											</div>
										</>
									)}

									{steps[2] === 'active' && (
										<>
											<div>
												<h1>Your Business Basic Info!</h1>
												<p className={'mb-4'}>
													As a valid registration process let us know few information about your business.
													More information will be required once you register your account <i className="fa fa-info-circle"/>
												</p>
											</div>
											<div className="partner_info_form">
												<div className="row">
													<div className="col-md-12 form-group">
														<Input
															placeholder="Your company name (Abc Pvt. Ltd.)"
															prefix={<HomeFilled style={{ color: 'rgba(0,0,0,.25)' }} />}
															value={values.company.name}
															onChange={(e) => {
																setFieldValue('company.name', e.target.value);
															}}
														/>
														{errors.company?.name && <div className="text-danger mt-2">{errors.company.name}</div>}
													</div>
													<div className="col-md-6 form-group">
														<Input
															placeholder="Phone number (10 Digit 98########)"
															prefix={<PhoneFilled style={{ color: 'rgba(0,0,0,.25)' }} />}
															value={values.company.phone || ''}
															onChange={(e) => {
																setFieldValue('company.phone', e.target.value);
															}}
														/>
														{errors.company?.phone && <div className="text-danger mt-2">{errors.company.phone}</div>}
													</div>
													<div className="col-md-6 form-group">
														<Input
															placeholder="Email address (<EMAIL>)"
															prefix={<MailFilled style={{ color: 'rgba(0,0,0,.25)' }} />}
															value={values.company.email || ''}
															onChange={(e) => {
																setFieldValue('company.email', e.target.value);
															}}
														/>
														{errors.company?.email && <div className="text-danger mt-2">{errors.company.email}</div>}
													</div>
													<div className="col-md-12 mb-4">
														<AddressAutocomplete
															placeholder="Select Company Address"
															value={values.company.address ? {
																value: {
																	place_id: values.company.address.place_id,
																	description: values.company.address.description
																},
																label: values.company.address.description
															} : null}
															onAddressSelect={(addressData) => {
																setFieldValue('company.address', addressData);
															}}
															error={errors.company?.address}
															customStyles={{
																control: (base) => ({
																	...base,
																	height: '48px',
																	padding: '0 16px',
																	borderRadius: '4px',
																	border: '1px solid #e2e8f0',
																	boxShadow: 'none',
																	backgroundColor: 'white',
																	'&:hover': {
																		border: '.5px solid #c71782',
																	},
																	'&:focus-within': {
																		border: '.5px solid #c71782',
																	},
																}),
																placeholder: (base) => ({
																	...base,
																	color: '#c0c7d0',
																	marginLeft: '5px',
																	fontWeight: 700,
																}),
																input: (base) => ({
																	...base,
																	fontWeight: 700,
																}),
																singleValue: (base) => ({
																	...base,
																	marginLeft: '5px',
																	fontWeight: 700,
																}),
																clearIndicator: (base) => ({
																	...base,
																	cursor: "pointer",
																	transition: "color 0.2s ease-in-out",
																	"&:hover": {
																		color: "red",
																	},
																}),
															}}
														/>
													</div>
													<div className="col-md-12 form-group mt-4">
														<Button
															type={'primary'}
															onClick={() => {
																if (isStep2Valid(errors, values)) {
																	setCompletedSteps(prev => ({ ...prev, 2: true }));
																}
																setSteps({
																	1: 'inactive',
																	2: 'inactive',
																	3: 'active',
																})
															}}
															size="large"
														>
															Save & Proceed
														</Button>
													</div>
												</div>
											</div>
										</>
									)}

									{steps[3] === 'active' && (
										<>
											<div>
												<h1>Authentication Info!</h1>
												<p className={'mb-0'}>
													Provide and verify your authentication information. Use this email / password
													every time you login to your account <i className="fa fa-info-circle"/>
												</p>
											</div>
											<div className="partner_info_form">
												<div className="row">
													<div className="col-md-12 form-group">
														<Input
															placeholder="Email address (<EMAIL>)"
															prefix={<MailFilled style={{ color: 'rgba(0,0,0,.25)' }} />}
															value={values.auth.email || ''}
															onChange={(e) => {
																setFieldValue('auth.email', e.target.value);
															}}
														/>
														{errors.auth?.email && <div className="text-danger mt-2">{errors.auth.email}</div>}
													</div>

													<div className="col-md-6 form-group">
														<Input.Password
															type={'password'}
															placeholder="Password (min 6 character)"
															prefix={<LockFilled style={{ color: 'rgba(0,0,0,.25)' }}/>}
															value={values.auth.password}
															onChange={(e) => {
																setFieldValue('auth.password', e.target.value);
															}}
														/>
														{errors.auth?.password && <div className="text-danger mt-2">{errors.auth.password}</div>}
													</div>

													<div className="col-md-6 form-group">
														<Input.Password
															type={'password'}
															placeholder="Confirm your password"
															prefix={<LockFilled style={{ color: 'rgba(0,0,0,.25)' }}/>}
															value={values.auth.password_confirmation}
															onChange={(e) => {
																setFieldValue('auth.password_confirmation', e.target.value);
															}}
														/>
														{errors.auth?.password_confirmation && <div className="text-danger mt-2">{errors.auth.password_confirmation}</div>}
													</div>

													<div className="col-md-12">
														<p>
															<Checkbox
																checked={values.agreement}
																onChange={(e) => {
																	setFieldValue('agreement', e.target.checked);
																}}
															>
																I have read and agree with all the mentioned <a href="/terms" target={'_blank'}><u>Terms and conditions.</u></a>
															</Checkbox>
														</p>

													</div>
													<div className="col-md-12 form-group mt-4">
														<Button
															loading={status === 'saving'}
															disabled={!isValid || !values.agreement}
															type={'primary'}
															htmlType={'submit'}
															block
															size="large"
														>
															Register Now
														</Button>
													</div>
												</div>
											</div>
										</>
									)}
								</div>
							</form>
						)}
					</Formik>
					<div className="steps_progress partner_registration">
						<ul>
							<li>
								<a
									href=""
									onClick={(e) => {
										e.preventDefault()
										setSteps({
											1: 'active',
											2: 'inactive',
											3: 'inactive',
										});
									}}
									className={steps[1]}
								>
									{/* {steps[1] === 'done' ? <i className="fa-solid fa-check"></i> : 1} */}
									{completedSteps[1] ? <i className="fa-solid fa-check"></i> : 1}
								</a>
								{steps[1] === 'active' ? <div className="info_step">
									<h4>Partner Type</h4>
									<p>Select your partnership category.</p>
								</div> : null}
							</li>
							<li>
								<a
									href=""
									onClick={(e) => {
										e.preventDefault()
										setSteps({
											1: 'inactive',
											2: 'active',
											3: 'inactive'
										});
									}}
									className={steps[2]}
								>
									{completedSteps[2] ? <i className="fa-solid fa-check"></i> : 2}
								</a>
								{steps[2] === 'active' ? <div className="info_step">
									<h4>Business Info</h4>
									<p>Let us know few things about you.</p>
								</div> : null}
							</li>
							<li>
								<a
									href=""
									onClick={(e) => {
										e.preventDefault()
										setSteps({
											1: 'inactive',
											2: 'inactive',
											3: 'active'
										});
									}}
									className={steps[3]}
								>
									{completedSteps[3] ? <i className="fa-solid fa-check"></i> : 3}
								</a>
								{steps[3] === 'active' ? <div className="info_step">
									<h4>Login info</h4>
									<p>Set your login information.</p>
								</div> : null}
							</li>
						</ul>
					</div>
				</>
			)}
		</>
	)
}

export default PartnerRegistrationFlow;
