'use client';

import { AppDispatch, RootState } from "@/store";
import { resendVerificationEmailAction } from "@/store/slices/user.slice";
import { UserModel } from "@/types/models/user.model";
import { isEmail, isPhone } from "@/utils/general.util"
import { LoadingOutlined } from "@ant-design/icons";
import Link from "next/link"
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const PartnerRegistrationSuccess = ({ regCredential } : { regCredential: string }) => {
	const dispatch = useDispatch<AppDispatch>();
	const { status } = useSelector((state: RootState) => state.user);
	const [message, setMessage] = useState<string>('');
	const [countdown, setCountdown] = useState<number>(0);

	useEffect(() => {
		let timer: NodeJS.Timeout;
		if (countdown > 0) {
			timer = setTimeout(() => setCountdown(countdown - 1), 1000);
		}
		return () => clearTimeout(timer);
	}, [countdown]);

	const handleResendEmail = async () => {
		setMessage('Resending verification email. Please wait...');
		setCountdown(60);
		const res = await dispatch(resendVerificationEmailAction<Pick<UserModel, 'email'>>({ email: regCredential }));
		if (res && res.status === 200) setMessage('Verification email was sent successfully. Please check your inbox for the verification email.');
		else setMessage('Something went wrong during sending verification email. Please try again.');
	};

	return <div className="login-input-box partner_registration_box">
		<h1>Welcome! Account created.</h1>
		<p style={{ fontSize: 14, fontWeight: 300 }}>
			<b style={{ fontWeight: 500 }}>Thank you for joining. We feel lucky to have you on board.
			</b>
			<br/>
			<br/> <br/>

			{message ? (
				<>
					<span>{message}</span>
					{status === 'saving' && <LoadingOutlined />}
					<br />
					<br />
				</>
			) : (
				<>
					{isEmail(regCredential) && (
						<>
							<span>
								A verification link has been sent to <b style={{ fontWeight: 500 }}>{regCredential}</b>.
								Please visit your email account and click the link to verify your account.
							</span>
							<br />
							<br />
						</>
					)}
					{isPhone(regCredential, 'mobile') && (
						<>
							<span>
								You can sign in now with your registered phone <b style={{ fontWeight: 500 }}>({regCredential})</b> to your account now.
							</span>
							<br />
							<br />
						</>
					)}
				</>
			)}

			{/* <Link href={'/user/signin'}><span >Sign in.</span></Link> */}
		</p>
		<div className="more-links">
			<p>Didn't received verification link?</p>
			{countdown > 0 ? (
				<span>Try again in {countdown}s</span>
			) : (
				<Link href={''} onClick={handleResendEmail}>Resend Verification Email</Link>
			)}
		</div>
	</div>
}

export default PartnerRegistrationSuccess
