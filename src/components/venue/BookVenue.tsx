'use client'

import { useEffect, useState } from 'react'
import AppModal from '@/components/common/AppModal'
import { VenueModel } from '@/types/models/venue.model'
import { useRouter } from 'next-nprogress-bar'
import { useDispatch } from 'react-redux'
import { AppDispatch } from '@/store'
import { getEncryptedCookie, isAuthenticatedUser } from '@/utils/cookie.util'
import { appStateChange } from '@/store/slices/app.slice'
import { useSearchParams } from 'next/navigation'
import { Formik } from 'formik'
import { formatNameInput, getFullName, isEmail, isPhone } from '@/utils/general.util'
import { Button } from 'antd'
import { AppInputField, AppInputTextField } from '../common/AppInputField.component'
import { UserModel } from '@/types/models/user.model'
import { Errors } from '@/types/index.types'
import { venueBookingInquiryAction } from '@/store/slices/venue.slice'

type BookVenueProps = {
  venue: VenueModel;
}

type BookVenueFormType = {
	name: string;
	email: string;
	phone: string;
	description: string;
}

const BookVenue: React.FC<BookVenueProps> = ({ venue }) => {
	const dispatch = useDispatch<AppDispatch>();
	const router = useRouter();
	const searchParams = useSearchParams();

	const [showModal, setShowModal] = useState<boolean>(false);
	const isLoggedIn = isAuthenticatedUser();
	const user = getEncryptedCookie('_eticket_user_') as UserModel;

	useEffect(() => {
		const showWizard = searchParams.get('showBuyWizard');
		if (showWizard === 'true') {
			setShowModal(true);
			// Clean the URL
			const params = new URLSearchParams(Array.from(searchParams.entries()));
			params.delete('showBuyWizard');
			router.replace(`${window.location.pathname}?${params.toString()}`, { scroll: false });
		}
	}, [searchParams, router]);

	return (
		<>
			<a
				href="#"
				className="ripple-eff btn btn-primary btn-lg"
				onClick={(e) => {
					e.preventDefault();
					if (!isLoggedIn) {
						dispatch(appStateChange({ key: 'showLoginModal', value: true }));
						return;
					}

					setShowModal(true);
				}}
			>
        Book Venue
			</a>

			{showModal && (
				<AppModal
					showModal={showModal}
					toggleAction={() => setShowModal(false)}
					title={'Book Venue'}
				>
					<div className="m-4">
						<Formik<BookVenueFormType>
							initialValues={{
								name: getFullName(user),
								email: user.email || '',
								phone: user.phone || '',
								description: '',
							}}
							validate={(values) => {
								const errors: Errors<BookVenueFormType> = {};

								if (!values.name) errors.name = 'Please enter your name';

								if (values.email && !isEmail(values.email)) errors.email = 'Please enter a valid email address';
								if (!values.email) errors.email = 'Please enter your email address';

								if (values.phone && !isPhone(values.phone, 'mobile')) errors.phone = 'Please enter a valid phone number';
								if (!values.phone) errors.phone = 'Please enter your phone number';

								if (!values.description) errors.description = 'Please enter a description';

								return errors;
							}}
							onSubmit={async (values, actions) => {
								const res = await dispatch(venueBookingInquiryAction<BookVenueFormType>(values));
								if (res && res.status === 201) {
									actions.resetForm();
									setShowModal(false);
								}
							}}
						>
							{({ values, errors, handleSubmit, setFieldValue, isValid, isValidating, isSubmitting }) => (
								<div className='section-box no-box'>
									<div className="header">
										{venue.name}
									</div>
									<form onSubmit={handleSubmit} className='section-form'>
										<AppInputField
											value={values.name}
											onChangeInput={(value: string) => {
												const formattedValue = formatNameInput(value);
												setFieldValue('name', formattedValue);
											}}
											id="name"
											required={true}
											label="Full Name"
											placeholder="First Name"
											errorMessage={errors.name}
										/>

										<AppInputField
											value={values.email}
											onChangeInput={(value: string) => {
												setFieldValue('email', value);
											}}
											id="email"
											required={true}
											label="Email"
											placeholder="Email"
											errorMessage={errors.email}
										/>

										<AppInputField
											value={values.phone}
											onChangeInput={(value: string) => {
												setFieldValue('phone', value)
											}}
											id="phone"
											required={true}
											label="Phone Number"
											placeholder="9845585858"
											errorMessage={errors.phone}
											type="number"
										/>

										<AppInputTextField
											id="description"
											value={values.description}
											onChangeInput={(value: string) => {
												setFieldValue('description', value);
											}}
											label={'Description'}
											placeholder={'Description about why you want to book this venue ....'}
											rows={4}
											errorMessage={errors.description}
											maxLength={500}
											required={true}
										/>

										<div className="form-actions">
											<Button
												type="primary"
												disabled={!isValid || isValidating}
												loading={isSubmitting}
												htmlType="submit"
											>
												Save
											</Button>
											<Button
												type="primary"
												disabled={isSubmitting || isValidating}
												onClick={() => {
													setShowModal(false);
												}}
											>
												Cancel
											</Button>
										</div>
									</form>
								</div>
							)}
						</Formik>
					</div>
				</AppModal>
			)}
		</>
	)
}

export default BookVenue
