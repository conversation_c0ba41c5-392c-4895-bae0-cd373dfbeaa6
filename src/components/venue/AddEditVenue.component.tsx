'use client';

import { <PERSON><PERSON>, Modal } from 'antd';
import FullScreenModal from '../FullScreenModal.component'
import { Formik } from 'formik';
import { AppDispatch, RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { ServerStatus } from '@/types/index.types';
import { AppInputField, AppInputTagField, AppInputTextField } from '../common/AppInputField.component';
import { useEffect, useState } from 'react';
import { VenueModel } from '@/types/models/venue.model';
import { AddVenueType } from '@/types/forms/add-venue.type';
import { venueStateChange, createVenueAction, updateVenueAction } from '@/store/slices/venue.slice';
import { FileImageFilled, CloseCircleFilled } from '@ant-design/icons';
import ImageCropper from '../ImageCropper.component';
import { addVenueValidation } from '@/utils/validation.util';
import AddressAutocomplete from '../AddressAutocomplete.component';
import { fetchGeneralInfoAction } from '@/store/slices/general-info.slice';
import { GeneralInfoModal } from '@/types/models/general-info.model';
import { getFullImageUrl } from '@/utils/general.util';
import ImageUpload from '../common/ImageUpload.component';

const AddEditVenue = ({ showModal, setShowModal } : {showModal: boolean, setShowModal: React.Dispatch<React.SetStateAction<boolean>>}) => {
	const dispatch = useDispatch<AppDispatch>();

	const [showCloseModal, setShowCloseModal] = useState<boolean>(false);

	const { record, status } = useSelector((state: RootState) => state.venue) as { record: VenueModel | null, status: ServerStatus };
	const { record: generalInfo } = useSelector((state: RootState) => state.generalInfo) as { record: GeneralInfoModal };

	useEffect(() => {
		if (showModal) {
			dispatch(fetchGeneralInfoAction());
		}
	}, [dispatch, showModal])

	return (
		<Formik<AddVenueType>
			key={record?.id}
			initialValues={{
				name: record?.name || '',
				image: record?.image || '',
				logo: record?.logo || '',
				facebook: record?.facebook || '',
				youtube: record?.youtube || '',
				twitter: record?.twitter || '',
				instagram: record?.instagram || '',
				phone_number: record?.phone_number || '',
				mobile_number: record?.mobile_number || '',
				email: record?.email || '',
				website: record?.website || '',
				about: record?.about || '',
				attractions: record?.attractions || '',
				address: record?.address || null,
				meta_desc: record?.meta_desc || '',
				meta_keywords: record?.meta_keywords ? record.meta_keywords.split(',') : [],
			}}
			validate={(data) => {
				return addVenueValidation(data);
			}}
			onSubmit={async (values, actions) => {
				const res = record ?
					await dispatch(updateVenueAction<AddVenueType>(record.id, values))
					: await dispatch(createVenueAction<AddVenueType>(values));
				if (res) {
					if (res.status === 201 || (record && res.status === 200)) {
						actions.resetForm();
						setShowModal(false)
					}
					if (record) {
						window.history.replaceState(
							null,
							'',
							'/user/dashboard/venues'
						);
						dispatch(venueStateChange({ key: 'record', value: null }));
					}
				}
			}}
		>
			{({ values, errors, handleSubmit, setFieldValue, resetForm, isValid, isValidating, dirty, setValues }) => (
				<>
					<FullScreenModal
						className={'dashboard_add_form'}
						onClose={() => {
							if (dirty) setShowCloseModal(true);
							else {
								resetForm();
								setShowModal(false);
								if (record) {
									window.history.replaceState(
										null,
										'',
										'/user/dashboard/venues'
									);
									dispatch(venueStateChange({ key: 'record', value: null }));
								}
							}
						}}
						show={showModal}
					>
						<div className="modal-body-container-form">
							<div className="add_form_header">
								<div className="container">
									<div className="d-flex justify-content-between align-items-center">
										<div>
											<h1>Add Venue</h1>
										</div>
									</div>
								</div>
							</div>

							<form>
								<div className="add_form_container">
									<div className="container">
										<div className="row">
											<div className="col-md-12">
												<div className='input-container'>
													<label htmlFor="address">
														Venue Address <span className="text-danger">*</span>
													</label>
													<AddressAutocomplete
														placeholder="Select Venue Address"
														value={values.address ? {
															value: {
																place_id: values.address.place_id,
																description: values.address.description
															},
															label: values.address.description
														} : null}
														onAddressSelect={(addressData) => {
															if (addressData) {
																setValues({
																	...values,
																	address: addressData,
																	name: addressData.description,
																});
															} else {
																setValues({
																	...values,
																	address: null,
																	name: '',
																});
															}
														}}
														error={errors?.address}
													/>
												</div>
											</div>

											<div className="col-md-12">
												<AppInputField
													id="venue_name"
													required={true}
													value={values.name}
													label={'Venue Name'}
													info={'Name of the venue'}
													onChangeInput={(value: string) => {
														setFieldValue('name', value);
													}}
													placeholder={'The Example Hall'}
													errorMessage={errors.name}
												/>
											</div>

											<ImageCropper
												value={values.image as string}
												renderUploadTrigger={(openUpload) => (
													<div className="col-md-12">
														<div onClick={openUpload} className="drag_drp_cover">
															<div>
																<h3><i className="fas fa-images" /></h3>
																<p>Click to upload venue's image.</p>
															</div>
														</div>
													</div>
												)}
												renderPreview={(resetImage) => (
													<div className="col-md-12">
														<div className="input-container">
															<div className="image-upload-preview">
																<div className="image-preview">
																	<img src={getFullImageUrl(values.image || '')} alt="" />
																</div>
																<div className="image-info">
																	<h1>Image of the venue</h1>
																</div>
																<div className="image-action">
																	<a href="" onClick={(e) => e.preventDefault()}>
																		<FileImageFilled />
																	</a>
																	<a href="" onClick={(e) => {
																		e.preventDefault();
																		resetImage();
																	}}>
																		<CloseCircleFilled />
																	</a>
																</div>
															</div>
														</div>
													</div>
												)}
												onCropComplete={(croppedImageUrl) => {
													setFieldValue('image', croppedImageUrl.split('/').at(-1))
												}}
												errorMessage={errors.image}
											/>

											<div className="col-md-12">
												<div className='input-container'>
													<label>
														Venue Logo <span className="text-danger">*</span>
													</label>
													{errors.logo && <span className='error-message'>({errors.logo})</span>}
													<ImageUpload
														maxCount={1}
														initialImages={values.logo ? [values.logo] : []}
														onImagesChange={(images) => {
															setFieldValue('logo', images[0] || '');
														}}
													/>

												</div>
											</div>

											<div className="col-md-12">
												<AppInputField
													id="venue_mobile_number"
													value={values.mobile_number}
													onChangeInput={(value: string) => {
														setFieldValue('mobile_number', value);
													}}
													type='number'
													maxLength={10}
													label={'Mobile Number'}
													info={'Mobile number for the venue'}
													placeholder={'9841111111'}
													errorMessage={errors.mobile_number}
												/>
											</div>

											<div className="col-md-12">
												<AppInputField
													id="venue_phone_number"
													value={values.phone_number}
													onChangeInput={(value: string) => {
														setFieldValue('phone_number', value);
													}}
													type='number'
													maxLength={10}
													label={'Phone Number'}
													info={'Landline number for the venue'}
													placeholder={'014444444'}
													errorMessage={errors.phone_number}
												/>
											</div>

											<div className="col-md-12">
												<AppInputField
													id="venue_email"
													value={values.email}
													onChangeInput={(value: string) => {
														setFieldValue('email', value);
													}}
													label={'Email Address'}
													info={'Official email address for the venue'}
													placeholder={'<EMAIL>'}
													errorMessage={errors.email}
												/>
											</div>

											<div className="col-md-12">
												<AppInputField
													id="venue_website"
													value={values.website}
													onChangeInput={(value: string) => {
														setFieldValue('website', value);
													}}
													label={'Website URL'}
													info={'Official website URL for the venue'}
													placeholder={'https://www.example.com'}
													errorMessage={errors.website}
												/>
											</div>

											<div className="col-md-12">
												<AppInputField
													id="venue_facebook_link"
													value={values.facebook}
													onChangeInput={(value: string) => {
														setFieldValue('facebook', value);
													}}
													label={'Facebook Link'}
													info={'Link to the facebook page of the venue'}
													placeholder={'https://www.facebook.com/venue'}
													errorMessage={errors.facebook}
												/>
											</div>

											<div className="col-md-12">
												<AppInputField
													id="venue_youtube_link"
													value={values.youtube}
													onChangeInput={(value: string) => {
														setFieldValue('youtube', value);
													}}
													label={'YouTube Link'}
													info={'Link to the YouTube channel of the venue'}
													placeholder={'https://www.youtube.com/channel/venue'}
													errorMessage={errors.youtube}
												/>
											</div>

											<div className="col-md-12">
												<AppInputField
													id="venue_twitter_link"
													value={values.twitter}
													onChangeInput={(value: string) => {
														setFieldValue('twitter', value);
													}}
													label={'Twitter Link'}
													info={'Link to the Twitter profile of the venue'}
													placeholder={'https://twitter.com/venue'}
													errorMessage={errors.twitter}
												/>
											</div>

											<div className="col-md-12">
												<AppInputField
													id="venue_instagram_link"
													value={values.instagram}
													onChangeInput={(value: string) => {
														setFieldValue('instagram', value);
													}}
													label={'Instagram Link'}
													info={'Link to the Instagram profile of the venue'}
													placeholder={'https://www.instagram.com/venue'}
													errorMessage={errors.instagram}
												/>
											</div>

											<div className="col-md-12">
												<AppInputTextField
													id="about_venue"
													value={values.about}
													onChangeInput={(value: string) => {
														setFieldValue('about', value);
													}}
													label={'About the Venue'}
													placeholder={'Description about the venue ....'}
													rows={6}
													info={'Short info about the venue (minimum 30 characters / max 500 characters)'}
													errorMessage={errors.about}
													maxLength={500}
												/>
											</div>

											<div className="col-md-12">
												<AppInputTextField
													id="venue_attractions"
													value={values.attractions}
													onChangeInput={(value: string) => {
														setFieldValue('attractions', value);
													}}
													label={'Attractions about the Venue'}
													placeholder={'Attractions about the venue ....'}
													rows={6}
													info={'Attractions about the venue (minimum 30 characters / max 500 characters)'}
													errorMessage={errors.attractions}
													maxLength={500}
												/>
											</div>

											<div className='col-md-12'>
												<AppInputTagField
													label='Meta Keywords'
													id='meta_keywords'
													value={values.meta_keywords}
													onChangeInput={(value: string[]) => {
														setFieldValue('meta_keywords', value);
													}}
													placeholder={'New Keyword'}
													info={'Keywords for the venue for SEO optimization (minimum 3 keywords)'}
													errorMessage={errors.meta_keywords as string}
												/>
											</div>

											<div className="col-md-12">
												<AppInputTextField
													id="meta_description"
													value={values.meta_desc}
													onChangeInput={(value: string) => {
														setFieldValue('meta_desc', value);
													}}
													label={'Meta Description'}
													placeholder={'Meta Description for the venue ....'}
													rows={6}
													info={'Short meta description about the venue for SEO optimization (minimum 50 characters / max 500 characters)'}
													errorMessage={errors.meta_desc}
													maxLength={500}
												/>
											</div>
										</div>
									</div>
								</div>
							</form>

							<div className="add_form_footer">
								<div className="container-fluid">
									<div className="row">
										<div className="col-md-12">
											<div className="d-flex justify-content-between">
												<p style={{ color: '#999' }}>
													<small>Need assistance? Contact us at <a
														href={"mailto:" + (generalInfo?.email_one || '<EMAIL>')}
														target="_blank"
														rel="noopener noreferrer">{generalInfo?.email_one || '<EMAIL>'}</a>
													</small>
												</p>
												<div>
													<a href=""
														onClick={(e) => {
															e.preventDefault();
															if (dirty) setShowCloseModal(true);
															else {
																resetForm();
																setShowModal(false);
																if (record) {
																	window.history.replaceState(
																		null,
																		'',
																		'/user/dashboard/venues'
																	);
																	dispatch(venueStateChange({ key: 'record', value: null }));
																}
															}
														}}
														className='me-3'
														style={{ pointerEvents: status === 'saving' ? 'none' : 'auto', opacity: status === 'saving' ? 0.5 : 1 }}
													>
														Cancel
													</a>
													<Button
														loading={status === 'saving'}
														size={'large'}
														type={'primary'}
														disabled={!isValid || status === 'saving' || isValidating || !dirty}
														onClick={() => handleSubmit()}
													>
														{record ? 'Update' : 'Save'} Venue
													</Button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</FullScreenModal>
					<Modal
						title={'Are you sure?'}
						open={showCloseModal}
						onOk={() => {
							resetForm();
							setShowCloseModal(false);
							setShowModal(false);
							if (record) {
								window.history.replaceState(
									null,
									'',
									'/user/dashboard/venues'
								);
								dispatch(venueStateChange({ key: 'record', value: null }));
							}
						}}
						onCancel={() => setShowCloseModal(false)}
						okText={'Quit'}
						closable={false}
					>
						<div>
							Are you sure you want to quit? Your data has not been saved.
						</div>
					</Modal>
				</>
			)}
		</Formik>
	)
}

export default AddEditVenue
