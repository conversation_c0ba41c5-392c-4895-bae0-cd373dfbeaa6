'use client'

import { AppDispatch, RootState } from "@/store";
import { subscribeToNewsletterAction } from "@/store/slices/user.slice";
import { ServerStatus } from "@/types/index.types";
import { isEmail } from "@/utils/general.util";
import { LoadingOutlined } from "@ant-design/icons";
import { message } from "antd";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux"

export default function NewsletterSubscription() {
	const dispatch = useDispatch<AppDispatch>();
	const [email, setEmail] = useState<string>('');
	const { status } = useSelector((state: RootState) => state.user) as {status: ServerStatus};

	return (
		<div className="subs-form" style={{ width: '100%' }}>
			<h3 style={{ display: 'none' }} className="subscribe-text-hidden">Subscribe to our newsletter.</h3>
			<div className="form-row">

				<div className="input-group" style={{ height: '50px' }}>
					<div className="input-group-text"><i className="fa fa-envelope-open" /></div>
					<input
						type="text"
						className="form-control"
						placeholder="Email address"
						aria-label="Example text with button addon"
						value={email}
						onChange={(e) => setEmail(e.target.value)}
					/>
					<button
						className="btn btn-primary"
						type="button"
						id="button-addon1"
						disabled={status === 'saving' || (!!email && !isEmail(email))}
						style={{
							pointerEvents: status === 'saving' ? 'none' : 'auto',
							cursor: status === 'saving' ? 'not-allowed' : 'pointer'
						}}
						onClick={async () => {
							if (!email) return;
							if (!isEmail(email)) {
								message.error('Please enter a valid email address');
								return;
							}
							const res = await dispatch(subscribeToNewsletterAction(email));
							if (res && res.status === 200) setEmail('');
						}}
					>
						{status === 'saving' ? <>Subscribing <LoadingOutlined /></> : 'Subscribe'}
					</button>
				</div>

			</div>
		</div>
	)
}
