'use client'

import { isAuthenticatedUser } from "@/utils/cookie.util";
import Link from "next/link";
import { usePathname } from 'next/navigation'
import AuthLink from "../common/AuthLink.component";

export default function HeaderRightNav() {
	const pathname = usePathname();
	const isAuthenticated = isAuthenticatedUser();

	return (
		<ul className='nav justify-content-end'>
			{isAuthenticated ? (
				<>
					<li className='nav-item'>
						<AuthLink className={`nav-link link-item-right ${pathname === '/user/dashboard' ? 'active' : ''}`} href={'/user/dashboard'}>
							Dashboard
						</AuthLink>
					</li>
				</>
			) : (
				<>
					<li className='nav-item'>
						<Link className='nav-link' href={'/user/signin'}>
							<button className='ripple-eff btn btn-sm btn-primary login-signup '>
								Log in
							</button>
						</Link>
					</li>

					<li className='nav-item'>
						<Link
							className={`nav-link link-item-right ${pathname === '/partner/register' ? 'active' : ''}`}
							href={'/partner/register'}
						>
							<i className='fas fa-hands-helping mx-1' />
							Partner
						</Link>
					</li>
				</>
			)}

			<li className='nav-item'>
				<Link
					className={`nav-link link-item-right ${pathname === '/about' ? 'active' : ''}`}
					href={'/about'}
				>
					<i className='fa fa-info-circle mx-1' />
					About Us
				</Link>
			</li>

			<li className='nav-item'>
				<Link
					className={`nav-link link-item-right ${pathname === '/support' ? 'active' : ''}`}
					href={'/support'}
				>
					<i className='fa fa-life-ring mx-1' />
					Help & Support
				</Link>
			</li>
		</ul>
	)
}
