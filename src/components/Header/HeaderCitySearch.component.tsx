'use client'

import { useEffect, useState } from "react";
import CityModal from "./CityModal.component";
import DrawerNav from "./DrawerNav.component";
import SearchModal from "./SearchModal.component";
import { getCookie } from "@/utils/cookie.util";

export function HeaderCitySearchComponent() {
	const [showCityModal, setShowCityModal] = useState<boolean>(false);
	const [selectedCity, setSelectedCity] = useState<string | null>(null);
	const [showDrawer, setShowDrawer] = useState<boolean>(false);
	const [showSearchModal, setShowSearchModal] = useState<boolean>(false);

	const drawerSwitcher = showDrawer ? 'open' : '';

	useEffect(() => {
		const cityFromCookie = getCookie('_eticket_city_');
		if (cityFromCookie) {
			setSelectedCity(cityFromCookie as typeof selectedCity);
		} else setShowCityModal(true);
	}, []);

	return (
		<>
			<div className='row'>
				<div className='col pick-your-city'>
					<a
						href=''
						onClick={(e) => {
							e.preventDefault();
							setShowCityModal(true);
						}}
						data-toggle='modal'
						data-target='#exampleModal'
						className='text-white align-self-center'
					>
						<i className='fa fa-university mx-1' />
						{selectedCity || 'Pick City'}
					</a>
				</div>

				<div className='col-9 search-form-column'>
					<div className='form-row align-items-center'>
						<div className='input-group search-box-input'>
							<div className='input-group-prepend'>
								<div className='input-group-text'>
									<i className='fa fa-search' />
								</div>
							</div>
							<input
								onClick={() => {
									if (selectedCity) setShowSearchModal(true)
									else setShowCityModal(true)
								}}
								type='text'
								className='form-control'
								id='inlineFormInputGroup1'
								autoComplete='off'
								placeholder='search your mood.'
							/>
						</div>
						<div
							style={{ display: 'none' }}
							className='small-device-option'
						>
							<a
								href=''
								data-toggle='modal'
								onClick={(e) => {
									e.preventDefault();
									setShowCityModal(true);
								}}
								data-target='#exampleModal'
								className='text-white'
							>
								<i className='fa fa-university mx-1' />
								{selectedCity || 'Pick City'}
							</a>

							<div className='drawer-nav-switcher'>
								<div
									className='drawer-nav-icon'
									onClick={() => {
										setShowDrawer(!showDrawer);
									}}
								>
									<span />
									<span />
									<span />
								</div>
							</div>
						</div>
					</div>
				</div>

				<div className='col drawer-nav-switcher hide-on-small'>
					<div
						className={'drawer-nav-icon ' + drawerSwitcher}
						onClick={() => {
							setShowDrawer(!showDrawer);
						}}
					>
						<span />
						<span />
						<span />
					</div>
				</div>
			</div>
			{selectedCity && <SearchModal showSearchModal={showSearchModal} setShowSearchModal={setShowSearchModal} city={selectedCity} />}
			<CityModal showCityModal={showCityModal} setSelectedCity={setSelectedCity} setShowCityModal={setShowCityModal} />
			<DrawerNav showDrawer={showDrawer} setShowDrawer={setShowDrawer} />
		</>
	)
}
