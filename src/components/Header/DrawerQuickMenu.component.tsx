'use client'

import { clearAllCookies, isAuthenticatedUser } from "@/utils/cookie.util";

export default function DrawerQuickMenu() {
	const isAuthenticated = isAuthenticatedUser();

	return (
		<div className="drawer-quick-menu">
			{isAuthenticated ? (
				<>
					{/* <AuthLink
						href={'/user/dashboard/profile'}
						className={`quick-menu-item ${pathname === '/user/dashboard/profile' ? 'active' : ''}`}
					>
						<i className="fa fa-address-card"/>
					</AuthLink> */}

					<a title={'Logout'} href=""
						onClick={(e) => {
							e.preventDefault();
							clearAllCookies();
							window.location.href = '/';
						}}
						className="quick-menu-item"
					>
						<i className="fas fa-sign-out-alt"/>
					</a>
				</>
			) : null}

		</div>
	)
}
