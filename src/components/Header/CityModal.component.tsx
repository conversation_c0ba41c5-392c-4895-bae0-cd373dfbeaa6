'use client'

import { Spin } from "antd";
import { sentenceCase } from "change-case";
import { debounce, lowerCase } from "lodash";
import { useMemo, useState } from "react";
import AppModal from "../common/AppModal";
import { setCookie } from "@/utils/cookie.util";

type CityModalProps = {
	showCityModal: boolean;
	setShowCityModal: (show: boolean) => void;
	setSelectedCity: (city: string) => void;
}

export const CityModal = ({ showCityModal, setShowCityModal, setSelectedCity }: CityModalProps) => {
	const [cityResult, setCityResult] = useState<any[] | null>(null);
	const [searchTerm, setSearchTerm] = useState<string>('');

	const mainCities = [
		{ "id": 1, "name": "Kathmandu", "province": 3 },
		{ "id": 2, "name": "Pokhara", "province": 4 },
		{ "id": 3, "name": "Lalitpur", "province": 3 },
		{ "id": 4, "name": "Biratnagar", "province": 1 },
		{ "id": 5, "name": "Birgunj", "province": 2 },
		{ "id": 6, "name": "Bharatpur", "province": 3 },
		{ "id": 7, "name": "Butwal", "province": 5 },
		{ "id": 8, "name": "Dharan", "province": 1 },
		{ "id": 9, "name": "Janakpur", "province": 2 },
		{ "id": 10, "name": "Nepalgunj", "province": 5 },
		{ "id": 11, "name": "Dhangadhi", "province": 7 },
	];

	const secondaryCities = [
		{ "id": 12, "name": "Bhaktapur", "province": 3 },
		{ "id": 13, "name": "Hetauda", "province": 3 },
		{ "id": 14, "name": "Itahari", "province": 1 },
		{ "id": 15, "name": "Gorkha", "province": 4 },
		{ "id": 16, "name": "Tansen", "province": 5 },
		{ "id": 17, "name": "Lamahi", "province": 5 },
		{ "id": 18, "name": "Tulsipur", "province": 5 },
		{ "id": 19, "name": "Kalaiya", "province": 2 },
		{ "id": 20, "name": "Rajbiraj", "province": 2 }
	];

	const handleCitySelect = (cityName: string) => {
		setSelectedCity(cityName);
		setCookie('_eticket_city_', cityName, { expires: 30 }); // Cookie expires in 30 days
		setShowCityModal(false);
	};

	const debouncedSearch = useMemo(
		() =>
			debounce((value: string) => {
				if (!value.trim()) {
					setCityResult(null); // Clear results if search term is empty
					return;
				}
				const filteredCities = [...mainCities, ...secondaryCities].filter((city) =>
					city.name.toLowerCase().includes(value.toLowerCase())
				);
				setCityResult(filteredCities.length ? filteredCities : []);
			}, 800),
		[]
	);

	return (
		<AppModal showModal={showCityModal} title={'Choose your City.'} toggleAction={() => setShowCityModal(false)}>
			<div className="city-search-bar">
				<form autoComplete="off">
					<div className="align-items-center">
						<div className="input-group search-box-input">
							<div className="input-group-prepend">
								<div className="input-group-text"><i className="fa fa-search"/>
								</div>
							</div>
							<input
								value={searchTerm}
								onChange={(e) => {
									setSearchTerm(e.target.value);
									debouncedSearch(e.target.value);
								}}
								type="text"
								className="form-control"
								placeholder="search your city."/>
						</div>
					</div>
				</form>
			</div>
			<Spin size={'large'} spinning={false} delay={0}>
				<div className="city-list-container">

					{!cityResult ? (
						<div>
							{/* Main Cities */}
							<div className="row">
								{mainCities.map((city, i) => {
									return (
										<div
											onClick={() => handleCitySelect(city.name)}
											key={i}
											className="col-md-3"
										>
											<div className={`top-cities ${lowerCase(city.name)}`}>
												<span>{sentenceCase(city.name)}</span>
											</div>
										</div>
									);
								})}
							</div>

							{/* Secondary Cities */}
							{secondaryCities.length > 0 ? (
								<div className={'more-cities-list'}>
									<ul>
										{secondaryCities.map((city, i) => (
											<li key={i}>
												<a
													href="#"
													onClick={(e) => {
														e.preventDefault();
														handleCitySelect(city.name)
													}}
												>
													{city.name}
												</a>
											</li>
										))}
									</ul>
								</div>
							) : (
								<div className="comment-section">
									<a href="" onClick={(e) => {
										e.preventDefault();
										// this.props.fetchAllCities();
									}} className="load-more-events" style={{ backgroundColor: 'white' }}>Load More Cities</a>
								</div>
							)}
						</div>
					) : (
						cityResult.length > 0 ? (
							<div className={'search-city-listing'}>
								<ul>
									{cityResult.map((city, i) => (
										<li key={i}>
											<a
												href="#"
												onClick={(e) => {
													e.preventDefault();
													handleCitySelect(city.name)
												}}
											>
												{city.name}
											</a>
										</li>
									))}
								</ul>
							</div>
						) : (
							<div className="search-city-listing">
								<ul>
									<li><a href="">No cities found....</a></li>
								</ul>
							</div>
						)
					)}

				</div>
			</Spin>

		</AppModal>
	)
}

export default CityModal
