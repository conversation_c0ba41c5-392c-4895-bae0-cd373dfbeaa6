'use client'

import DrawerUserInfo from "./DrawerUserInfo.component";
import DrawerQuickMenu from "./DrawerQuickMenu.component";
import DrawerSideNav from "./DrawerSideNav.component";
import { useEffect } from "react";

type DrawerNavProps = {
	showDrawer: boolean;
	setShowDrawer: (show: boolean) => void;
}

const DrawerNav = ({ showDrawer, setShowDrawer }: DrawerNavProps) => {
	const rightPixel = showDrawer ? '0px' : '-325px';

	useEffect(() => {
		if (showDrawer) {
			// Prevent body scroll
			document.body.style.overflow = "hidden";
		} else {
			// Re-enable scroll
			document.body.style.overflow = "";
		}
		// Clean up on unmount
		return () => {
			document.body.style.overflow = "";
		};
	}, [showDrawer]);

	return (
		<>
			<div id='mySidenav' className="sidenav app-side-drawer" style={{ right: rightPixel }}>
				<div className="drawer-banner">
					<div className="container-overlay">
						<DrawerQuickMenu />

						<DrawerUserInfo />
					</div>
				</div>

				<DrawerSideNav />
			</div>

			{showDrawer && <div className={"modal-backdrop show"} onClick={() => { setShowDrawer(false) }} />}
		</>
	)
}

export default DrawerNav
