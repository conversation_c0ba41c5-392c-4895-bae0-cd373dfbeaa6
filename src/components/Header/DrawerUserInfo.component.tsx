'use client'

import { getEncryptedCookie, isAuthenticatedUser } from "@/utils/cookie.util";
import Link from "next/link";
import AuthLink from "../common/AuthLink.component";
import { UserModel } from "@/types/models/user.model";
import { getFullImageUrl } from "@/utils/general.util";

export default function DrawerUserInfo() {
	const isAuthenticated = isAuthenticatedUser();
	const user = getEncryptedCookie('_eticket_user_') as UserModel;

	return (
		<div className="drawer-user-info">
			{isAuthenticated ? (
				<>
					{user.image ? (
						<img
							src={getFullImageUrl(user.image)}
							alt="Profile Image"
							className="user-profile-image"
						/>
					) : (
						<i className="fa fa-user-circle user-icon" />
					)}

					<p>Welcome <strong>{user.f_name ? `${user.f_name} ${user.l_name || ''}` : ''}</strong></p>
					<span>
						<AuthLink href={'/user/dashboard'}>
							{user.email} <i className="fa fa-check-circle"/>
						</AuthLink>
					</span>
				</>
			) : (
				<>
					<i className="fa fa-user-circle user-icon" />
					<span>
						<Link href={'/user/signin'}>
							<i className="fa fa-sign-in-alt"/> Sign in
						</Link>
						<Link href={'/user/signup'}>
							<i className="fa fa-user-alt"/> Sign up
						</Link>
					</span>
				</>
			)}
		</div>
	)
}
