'use client'

import { AppDispatch, RootState } from "@/store";
import { fetchAllCategoriesAction } from "@/store/slices/category.slice";
import { CategoryModel } from "@/types/models/category.model";
import Link from "next/link";
import { usePathname } from 'next/navigation'
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

export default function HeaderLeftNav() {
	const pathname = usePathname();
	const dispatch = useDispatch<AppDispatch>();
	const { navigationList } = useSelector((state: RootState) => state.category) as { navigationList: CategoryModel[] };

	useEffect(() => {
		dispatch(fetchAllCategoriesAction({ getNavCategories: true, type: 'active' }));
	}, [dispatch])

	return (
		<ul className='nav'>
			<li className='nav-item'>
				<Link className={`nav-link ${pathname === '/events' ? 'active' : ''}`} href={'/events'}>
					Events
				</Link>
			</li>
			{navigationList && navigationList.map((category) => (
				<li key={category.id} className="nav-item">
					<Link
						className={'nav-link'}
						href={`/events?categoryId=${category.id}`}
					>
						{category.name}
					</Link>
				</li>
			))}

			{false && <>
				<li className='nav-item'>
					<Link className={`nav-link ${pathname === '/venues' ? 'active' : ''}`} href={'/venues'}>
            Venues
					</Link>
				</li>

				<li className='nav-item'>
					<Link className={`nav-link ${pathname === '/movie' ? 'active' : ''}`} href={'/movie'}>
            Movies
					</Link>
				</li>
				<li className='nav-item'>
					<Link className={`nav-link ${pathname === '/travels' ? 'active' : ''}`} href={'/travels'}>
            Travels
					</Link>
				</li>
			</>
			}
		</ul>
	)
}
