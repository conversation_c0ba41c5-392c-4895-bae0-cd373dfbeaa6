'use client';

import { AppDispatch, RootState } from "@/store";
import { fetchAllCategoriesAction } from "@/store/slices/category.slice";
import { eventStateChange, fetchAllEventsAction } from "@/store/slices/event.slice";
import { ServerStatus } from "@/types/index.types";
import { CategoryModel } from "@/types/models/category.model";
import { EventModel } from "@/types/models/event.model";
import { CaretDownOutlined, CloseOutlined } from "@ant-design/icons";
import { Checkbox, Dropdown, Spin } from "antd";
import dayjs from "dayjs";
import { debounce } from "lodash";
import Link from "next/link";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import ExpandableContent from "../common/ExpandableContent.component";
import { NoResult } from "../common/NoResult";
import { getFullImageUrl } from "@/utils/general.util";

const SearchModal = ({ showSearchModal, setShowSearchModal, city }: { showSearchModal: boolean, setShowSearchModal: React.Dispatch<React.SetStateAction<boolean>>, city: string }) => {
	const dispatch = useDispatch<AppDispatch>();
	const { rows: categories } = useSelector((state: RootState) => state.category) as { rows: CategoryModel[] };
	const { searchResult: events, status } = useSelector((state: RootState) => state.event) as {
		searchResult: EventModel[];
		status: ServerStatus;
	};

	const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
	const [searchTerm, setSearchTerm] = useState<string>('');

	const searchInputRef = useRef<HTMLInputElement>(null);

	let className = ' close';
	// const body = document.querySelector('body');

	if (showSearchModal === true) {
		// if (body) {
		// 	body.classList.add('modal-open');
		// }
		className = ' open';
	} else if (showSearchModal === false) {
		// if (body) {
		// 	body.classList.remove('modal-open');
		// }
		className = ' close';
	}

	const debouncedFetchEvents = useMemo(
		() =>
			debounce((searchTerm: string, categories: number[]) => {
				const filters = {
					searching: true,
					...(categories.length > 0 && {
						category: categories.join(','),
					}),
					...(searchTerm && { search: searchTerm }),
				};
				dispatch(fetchAllEventsAction(filters));
			}, 500),
		[dispatch]
	);

	const handleSearchChange = useCallback((value: string) => {
		setSearchTerm(value);
		dispatch(eventStateChange({ key: 'status', value: 'fetching' }));
		debouncedFetchEvents(value, selectedCategories);
	}, [debouncedFetchEvents, selectedCategories, dispatch]);

	const handleCategoryChange = useCallback((categoryId: number) => {
		const newCategories = selectedCategories.includes(categoryId)
			? selectedCategories.filter(id => id !== categoryId)
			: [...selectedCategories, categoryId];
		setSelectedCategories(newCategories);

		if (searchTerm) {
			dispatch(eventStateChange({ key: 'status', value: 'fetching' }));
			debouncedFetchEvents(searchTerm, newCategories);
		}
	}, [debouncedFetchEvents, selectedCategories, searchTerm, dispatch]);

	useEffect(() => {
		if (showSearchModal) dispatch(fetchAllCategoriesAction({ getMainCategories: true, type: 'active' }));
	}, [dispatch, showSearchModal]);

	useEffect(() => {
		if (showSearchModal && searchInputRef.current) {
			searchInputRef.current.focus();
		}
	}, [showSearchModal]);

	return (
		<>
			<div className={"search-modal" + className}>
				<div style={{ zIndex: 9999999 }} className="search-close" onClick={() => {
					setShowSearchModal(false)
					dispatch(eventStateChange({ key: 'searchResult', value: []}))
					setSearchTerm('')
					setSelectedCategories([])
				}}>
					<CloseOutlined />
				</div>

				<div className="fixed_filter_option">
					<div className="container">
						<div className="search-contents for_search_filter">
							<div className="app-filter-options">
								<ul>
									{false && <>
										<li>
											<a href="#" className="active">
												<i className="fa-solid fa-user"></i> Events
											</a>
										</li>
										<li>
											<a href="#">
												<i className="fa-solid fa-home"></i> Venues
											</a>
										</li>
										<li>
											<a href="#">
												<i className="fa-solid fa-compass"></i> Travel
											</a>
										</li>
										<li>
											<a href="#">
												<i className="fa-solid fa-film"></i> Movies
											</a>
										</li>
									</>
									}
								</ul>
							</div>
							<div className="search-input">
								<form action="">
									<div className="input-group search-box-input">
										<div className="input-group-prepend">
											<div className="input-group-text"><i className="fa fa-search" /></div>
										</div>
										<input
											ref={searchInputRef}
											value={searchTerm}
											onChange={(e) => {
												const inputValue = e.target.value;

												// Check for spaces at the beginning or consecutive spaces at the end
												const isInvalid = /^\s|\s{2,}$/.test(inputValue);
												if (!isInvalid) handleSearchChange(inputValue);
											}}
											type="text"
											className="form-control"
											id="inlineFormInputGroup2"
											autoComplete="off"
											placeholder="search your mood."
										/>
									</div>
									<small className={'search-info'}>
									Searched results will be based on
									the &nbsp;
										<span>events</span>
									&nbsp;
									for <strong>{city}.</strong>
									</small>
								</form>

								<div className="category-filter">
									<a
										href=""
										onClick={(e) => {
											e.preventDefault();
											setSelectedCategories([]);
											if (searchTerm) debouncedFetchEvents(searchTerm, []);
										}}
										className={selectedCategories.length < 1 ? 'active' : ''}
									>
										All
									</a>

									{categories.slice(0, 7).map((cat, i) => (
										<a
											key={i}
											href=""
											onClick={(e) => {
												e.preventDefault();
												handleCategoryChange(cat.id);
											}}
											className={selectedCategories.includes(cat.id) ? 'active' : ''}
										>
											{cat.name}
										</a>
									))}

									{/* Optional Dropdown for additional categories */}
									{categories.length > 7 && (
										<Dropdown
											menu={{
												items: categories.slice(7).map((cat) => ({
													key: cat.id.toString(),
													label: (
														<Checkbox
															checked={selectedCategories.includes(cat.id)}
															onChange={() => handleCategoryChange(cat.id)}
														>
															<i className={cat.icon_name || ""} /> {cat.name}
														</Checkbox>
													),
												}))
											}}
										>
											<a
												className={
													"ant-dropdown-link " +
													(categories.slice(7).some((cat) => selectedCategories.includes(cat.id)) ? "active" : "")
												}
												href=""
												onClick={(e) => e.preventDefault()}
											>
												{categories.length - 7} More <CaretDownOutlined />
											</a>
										</Dropdown>
									)}
								</div>

							</div>
							<div className="filter-options">
							</div>
						</div>
					</div>
				</div>

				<div className="search-result-display">
					<div className="container">
						<div className="search-contents">
							<div className="app-filter-options">
							</div>
							<div className="search-input">
								<div className="result-display">
									{status === 'fetching' ? (
										<div className="d-flex justify-content-center align-items-center" style={{ height: '100px' }}>
											<Spin size={'large'} />
										</div>
									) : (
										events.length > 0 ? (
											<div className="filter-event-section">
												<div className="filter-content">
													<div className="filter-by-category">
														<div className="filtered-event-lists">
															{events.map((event) => (
																<div key={event.id} className="filtered-item">
																	<div className="row">
																		<div className="col-sm-3 filter-evt-info-img">
																			<div className="event-image">
																				<img
																					src={event?.image ? getFullImageUrl(event.image) : "https://placehold.co/200x200/png?text=Event+Image"}
																					alt={event?.title || ""}
																				/>
																			</div>
																		</div>
																		<div className="col-sm-8 filter-evt-info">
																			<div className="event-details">
																				<div className="row">
																					<div className="col-9">
																						<p className="time-location">
																							{dayjs(event.start_date_time).format('D MMM YYYY h:mm A')} onwards
																						</p>
																						<h2>{event?.title}</h2>
																					</div>
																					<div className="col-3">
																						<Link
																							href={`/events/${event.slug}-${event.id}`}
																							className="buy-ticket-button"
																							title="Buy Ticket"
																							onClick={() => {
																								// dispatch(eventStateChange({ key: 'record', value: event }))
																								setShowSearchModal(false)
																							}}
																						>
																							<i className="fas fa-ticket" />
																						</Link>
																					</div>
																				</div>
																				<hr />
																				{event?.about && <div className="event-about">
																					<ExpandableContent content={event.about} initialLimit={40} />
																				</div>}
																				<h3 className="event-cost">
																					<span>
																						{event.tickets && event.tickets.length > 0
																							? event.tickets.some(ticket => ticket.ticket_type === 'free')
																								? 'Free Event'
																								: `NPR ${Math.min(...event.tickets.map(ticket => ticket.price ?? Infinity))}`
																							: 'Tickets N/A'}
																					</span>
																				</h3>
																			</div>
																		</div>
																	</div>
																</div>
															))}
														</div>
													</div>
												</div>
											</div>
										) : (
											searchTerm ? (
												<NoResult message="No events found for your search."/>
											) : (
												<div style={{ color: 'black' }}>Search for events to see the results.</div>
											)
										)
									)}
								</div>
							</div>
							<div className="filter-options">
							</div>
						</div>
					</div>
				</div>
			</div>
		</>
	)
}

export default SearchModal
