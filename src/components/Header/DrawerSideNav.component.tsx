'use client'

import { getEncryptedCookie, isAuthenticatedUser } from "@/utils/cookie.util";
import AuthLink from "../common/AuthLink.component";
import { usePathname } from "next/navigation";
import { UserModel } from "@/types/models/user.model";

export default function DrawerSideNav() {
	const isAuthenticated = isAuthenticatedUser();
	const user = getEncryptedCookie('_eticket_user_') as UserModel;
	const pathname = usePathname();

	// if (!isAuthenticated) {
	// 	return (
	// 		<div className="side-nav-menu-items">
	// 			<AuthLink
	// 				href="/events"
	// 				className={pathname === '/events' ? 'active' : ''}
	// 			>
	// 				Events
	// 			</AuthLink>
	// 		</div>
	// 	)
	// }

	return (
		<div className="side-nav-menu-items">
			<AuthLink
				href={'/user/dashboard'}
				className={`${pathname === '/user/dashboard' ? 'active' : ''}`}
			>
				Dashboard
			</AuthLink>
			<AuthLink
				href={'/user/dashboard/settings'}
				className={`${pathname === '/user/dashboard/settings' ? 'active' : ''}`}
			>
				Settings
			</AuthLink>
			{/* <AuthLink
				href={'/user/dashboard/notifications'}
				className={`${pathname === '/user/dashboard/notifications' ? 'active' : ''}`}
			>
				Notifications
				<i className="fa fa-chevron-right" style={{ float: 'right', marginRight: '20px', paddingTop: '5px' }}/>
			</AuthLink> */}

			{(!isAuthenticated || (user?.user_type === 'normal' && !user?.is_partner)) && (
				<>
					<AuthLink
						href={'/user/dashboard/booked-events'}
						className={`${pathname === '/user/dashboard/booked-events' ? 'active' : ''}`}
					>
						Booked Events
					</AuthLink>
					<AuthLink
						href={'/user/dashboard/subscriptions'}
						className={`${pathname === '/user/dashboard/subscriptions' ? 'active' : ''}`}
					>
						Subscriptions
					</AuthLink>
					<AuthLink
						href={'/user/dashboard/preferences'}
						className={`${pathname === '/user/dashboard/preferences' ? 'active' : ''}`}
					>
						Preferences
					</AuthLink>
				</>
			)}

			{user?.user_type === 'normal' && user?.is_partner && (
				<>
					<AuthLink
						href={'/user/dashboard/events'}
						className={`${pathname === '/user/dashboard/events' ? 'active' : ''}`}
					>
						Events
					</AuthLink>
					<AuthLink
						href={'/user/dashboard/company'}
						className={`${pathname === '/user/dashboard/company' ? 'active' : ''}`}
					>
						Company
					</AuthLink>
				</>
			)}

			{user?.user_type === 'admin' && (
				<>
					<AuthLink
						href={'/user/dashboard/categories'}
						className={`${pathname === '/user/dashboard/categories' ? 'active' : ''}`}
					>
						Categories
					</AuthLink>
					<AuthLink
						href={'/user/dashboard/artists'}
						className={`${pathname === '/user/dashboard/artists' ? 'active' : ''}`}
					>
						Artists
					</AuthLink>
					<AuthLink
						href={'/user/dashboard/venues'}
						className={`${pathname === '/user/dashboard/venues' ? 'active' : ''}`}
					>
						Venues
					</AuthLink>
					<AuthLink
						href={'/user/dashboard/topics'}
						className={`${pathname === '/user/dashboard/topics' ? 'active' : ''}`}
					>
						Topics
					</AuthLink>
					<AuthLink
						href={'/user/dashboard/userlog'}
						className={`${pathname === '/user/dashboard/userlog' ? 'active' : ''}`}
					>
						User Logs
					</AuthLink>
					<AuthLink
						href={'/user/dashboard/general-info'}
						className={`${pathname === '/user/dashboard/general-info' ? 'active' : ''}`}
					>
						General Infos
					</AuthLink>
				</>
			)}
		</div>
	)
}
