import Link from 'next/link';
import HeaderLeftNav from './HeaderLeftNav.component';
import HeaderRightNav from './HeaderRightNav.component';
import { HeaderCitySearchComponent } from './HeaderCitySearch.component';

const HeaderComponent = () => {
	return (
		<>
			<header>
				<div className='app-navigation'>
					<div className='search-bar'>
						<div className='container'>
							<HeaderCitySearchComponent />
						</div>
					</div>

					<div className='navigator'>
						<div className='container-fluid-navigation'>
							<div className='row align-items-center'>
								<div className='col-sm-2'>
									<Link href={'/'}>
										<img
											className='logo-image'
											src='/images/logo-full.png'
											style={{ height: 45 }}
										/>
									</Link>
								</div>
								<div className='col-sm-10'>
									<div className='row'>
										<div className='col-sm-7'>
											<nav className='left-nav-items'>
												<HeaderLeftNav />
											</nav>
										</div>
										<div className='col-sm-5'>
											<nav className='right-nav-items'>
												<HeaderRightNav />
											</nav>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</header>
		</>
	);
};

export default HeaderComponent;
