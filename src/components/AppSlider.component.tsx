'use client'

import { AppDispatch, RootState } from '@/store';
import { eventStateChange, fetchSliderEventsAction } from '@/store/slices/event.slice';
import { EventModel } from '@/types/models/event.model';
import { getFullImageUrl } from '@/utils/general.util';
import { Skeleton } from 'antd';
import Link from 'next/link';
import { Carousel } from 'nuka-carousel';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const AppSlider = () => {
	const dispatch = useDispatch<AppDispatch>();
	const { sliderEvents: events } = useSelector((state: RootState) => state.event) as {
		sliderEvents: {
			data: EventModel[];
			count: number;
		};
	};
	const [loading, setLoading] = useState<boolean>(true);

	useEffect(() => {
		setLoading(true);
		dispatch(fetchSliderEventsAction()).then(() => setLoading(false));
	}, [dispatch]);

	return (
		<div className="app-main-slider">
			{loading ? (
				<div className="m-4" style={{ display: 'flex', justifyContent: 'space-between' }}>
					<Skeleton.Image active style={{ height: 250, width: 350 }} />
					<Skeleton.Image active style={{ height: 250, width: 350 }} />
					<Skeleton.Image active style={{ height: 250, width: 350 }} />
					<Skeleton.Image active style={{ height: 250, width: 350 }} />
				</div>
			) : events.data && events.data.length > 0 ? (
				events.data.length <= 3 ? (
					<div
						className="d-flex justify-content-center"
					>
						{events.data.map((event, index) => (
							<div key={index} className="slider-image">
								<Link
									href={`/events/${event.slug}-${event.id}`}
									onClick={() => {
										dispatch(eventStateChange({ key: 'record', value: null }));
									}}
								>
									<img
										src={getFullImageUrl(event.image || '')}
										alt={event.title || ''}
									/>
								</Link>
							</div>
						))}
					</div>
				) : (
					<Carousel
						showDots
						showArrows
						autoplay={true}
						autoplayInterval={2000}
						wrapMode="wrap"
						scrollDistance="slide"
					>
						{events.data.map((event, index) => (
							<div key={index} className="slider-image">
								<Link
									href={`/events/${event.slug}-${event.id}`}
									onClick={() => {
										dispatch(eventStateChange({ key: 'record', value: null }));
									}}
								>
									<img
										src={getFullImageUrl(event.image || '')}
										alt={event.title || ''}
									/>
								</Link>
							</div>
						))}
					</Carousel>
				)
			) : null}
		</div>
	)
}

export default AppSlider
