'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, Space } from 'antd';
import FullScreenModal from '../FullScreenModal.component'
import { Formik } from 'formik';
import { AppDispatch, RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { CategoryModel } from '@/types/models/category.model';
import { AddCategoryType } from '@/types/forms/add-category.type';
import { ServerStatus } from '@/types/index.types';
import { AppInputField, AppInputSelectField, AppInputTagField, AppInputTextField } from '../common/AppInputField.component';
import { categoryStateChange, createCategoryAction, fetchAllCategoriesAction, updateCategoryAction } from '@/store/slices/category.slice';
import { addCategoryValidation } from '@/utils/validation.util';
import { useEffect, useState } from 'react';
import { fetchAppsAction } from '@/store/slices/app.slice';
import { AppModel } from '@/types/models/app.model';
import { fetchGeneralInfoAction } from '@/store/slices/general-info.slice';
import { GeneralInfoModal } from '@/types/models/general-info.model';

const AddCategory = ({ showModal, setShowModal } : {showModal: boolean, setShowModal: React.Dispatch<React.SetStateAction<boolean>>}) => {
	const dispatch = useDispatch<AppDispatch>();

	const [showCloseModal, setShowCloseModal] = useState<boolean>(false);

	const { rows, record, status } = useSelector((state: RootState) => state.category) as { rows: CategoryModel[], record: CategoryModel | null, status: ServerStatus };
	const { apps } = useSelector((state: RootState) => state.app) as { apps: AppModel[] };
	const { record: generalInfo } = useSelector((state: RootState) => state.generalInfo) as { record: GeneralInfoModal };

	useEffect(() => {
		if (showModal) {
			dispatch(fetchAllCategoriesAction({ getMainCategories: true, type: 'active' }));
			dispatch(fetchAppsAction());
			dispatch(fetchGeneralInfoAction());
		}
	}, [dispatch, showModal])

	return (
		<Formik<AddCategoryType>
			key={record?.id}
			initialValues={{
				name: record?.name || '',
				icon_name: record?.icon_name || '',
				sub_id: record?.sub_id || null,
				app_type: record?.app_type || null,
				info: record?.info || '',
				meta_description: record?.meta_description || '',
				meta_keywords: record?.meta_keywords ? record.meta_keywords.split(',') : [],
			}}
			validate={(data) => {
				return addCategoryValidation(data);
			}}
			onSubmit={async (values, actions) => {
				const res = record ?
					await dispatch(updateCategoryAction<AddCategoryType>(record.id, values))
					: await dispatch(createCategoryAction<AddCategoryType>(values));
				if (res) {
					if (res.status === 201 || (record && res.status === 200)) {
						actions.resetForm();
						setShowModal(false)
					}
					if (record) {
						window.history.replaceState(
							null,
							'',
							'/user/dashboard/categories'
						);
						dispatch(categoryStateChange({ key: 'record', value: null }));
					}
				}
			}}
		>
			{({ values, errors, handleSubmit, setFieldValue, resetForm, isValid, isValidating, dirty }) => (
				<>
					<FullScreenModal
						className={'dashboard_add_form'}
						onClose={() => {
							if (dirty) setShowCloseModal(true);
							else {
								resetForm();
								setShowModal(false);
								if (record) {
									window.history.replaceState(
										null,
										'',
										'/user/dashboard/categories'
									);
									dispatch(categoryStateChange({ key: 'record', value: null }));
								}
							}
						}}
						show={showModal}
					>
						<div className="modal-body-container-form">
							<div className="add_form_header">
								<div className="container">
									<div className="d-flex justify-content-between align-items-center">
										<div>
											<h1>Add Category</h1>
										</div>
									</div>
								</div>
							</div>

							<form>
								<div className="add_form_container">
									<div className="container">
										<div className="row">

											<div className="col-md-6">
												<AppInputField
													id="category_name"
													required={true}
													value={values.name}
													label={'Category Name'}
													info={'Name of your category (minimum 6 characters)'}
													onChangeInput={(value: string) => {
														setFieldValue('name', value);
													}}
													placeholder={'New Category'}
													errorMessage={errors.name}
												/>
											</div>

											<div className="col-md-6">
												<AppInputField
													id="category_icon_name"
													value={values.icon_name}
													onChangeInput={(value: string) => {
														setFieldValue('icon_name', value);
													}}
													label={'Icon Name'}
													info={'Font Awesome icon name (className)'}
													placeholder={'fas fa-users'}
													errorMessage={errors.icon_name}
												/>
											</div>

											<div className="col-md-6">
												<AppInputSelectField
													id="main_category_id"
													value={values.sub_id}
													// maxCount={1}
													onChangeInput={(value: number) => {
														setFieldValue('sub_id', value);
													}}
													label={'Main Category'}
													info={'Select one main category if this category is a sub category'}
													options={rows.map((item) => ({
														label: item.name,
														value: item.id,
														icon: item.icon_name,
													}))
													}
													optionRender={(option) => (
														<Space>
															<i className={option?.data.icon || "fas fa-circle"} />
															{option?.label}
														</Space>
													)}
													errorMessage={errors.sub_id}
												/>
											</div>

											<div className="col-md-6">
												<AppInputSelectField
													id="category_app_type"
													required={true}
													value={values.app_type}
													// maxCount={1}
													onChangeInput={(value: number) => {
														setFieldValue('app_type', value);
													}}
													label={'Category App Type'}
													info={'Select the app type of your category'}
													options={apps.map((item) => ({
														label: item.name,
														value: item.id,
													}))}
													errorMessage={errors.app_type}
												/>
											</div>

											<div className="col-md-12">
												<AppInputTextField
													id="category_info"
													required={true}
													value={values.info}
													onChangeInput={(value: string) => {
														setFieldValue('info', value);
													}}
													label={'Category Info'}
													placeholder={'Description about the category ....'}
													rows={6}
													info={'Short info about your category (minimum 30 characters / max 500 characters)'}
													errorMessage={errors.info}
													maxLength={500}
												/>
											</div>

											<div className='col-md-12'>
												<AppInputTagField
													label='Meta Keywords'
													id='meta_keywords'
													value={values.meta_keywords}
													onChangeInput={(value: string[]) => {
														setFieldValue('meta_keywords', value);
													}}
													placeholder={'New Keyword'}
													info={'Keywords for the category for SEO optimization (minimum 3 keywords)'}
													errorMessage={errors.meta_keywords as string}
												/>
											</div>

											<div className="col-md-12">
												<AppInputTextField
													id="meta_description"
													value={values.meta_description}
													onChangeInput={(value: string) => {
														setFieldValue('meta_description', value);
													}}
													label={'Meta Description'}
													placeholder={'Meta Description for the category ....'}
													rows={6}
													info={'Short meta description about your category for SEO optimization (minimum 50 characters / max 500 characters)'}
													errorMessage={errors.meta_description}
													maxLength={500}
												/>
											</div>
										</div>
									</div>
								</div>
							</form>

							<div className="add_form_footer">
								<div className="container-fluid">
									<div className="row">
										<div className="col-md-12">
											<div className="d-flex justify-content-between">
												<p style={{ color: '#999' }}>
													<small>Need assistance? Contact us at <a
														href={"mailto:" + (generalInfo?.email_one || '<EMAIL>')}
														target="_blank"
														rel="noopener noreferrer">{generalInfo?.email_one || '<EMAIL>'}</a>
													</small>
												</p>
												<div>
													<a href=""
														onClick={(e) => {
															e.preventDefault();
															if (dirty) setShowCloseModal(true);
															else {
																resetForm();
																setShowModal(false);
																if (record) {
																	window.history.replaceState(
																		null,
																		'',
																		'/user/dashboard/categories'
																	);
																	dispatch(categoryStateChange({ key: 'record', value: null }));
																}
															}
														}}
														className='me-3'
													>
														Cancel
													</a>
													<Button
														loading={status === 'saving'}
														size={'large'}
														type={'primary'}
														disabled={!isValid || status === 'saving' || isValidating || !dirty}
														onClick={() => handleSubmit()}
													>
														{record ? 'Update' : 'Save'} Category
													</Button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</FullScreenModal>
					<Modal
						title={'Are you sure?'}
						open={showCloseModal}
						onOk={() => {
							resetForm();
							setShowCloseModal(false);
							setShowModal(false);
							if (record) {
								window.history.replaceState(
									null,
									'',
									'/user/dashboard/categories'
								);
								dispatch(categoryStateChange({ key: 'record', value: null }));
							}
						}}
						onCancel={() => setShowCloseModal(false)}
						okText={'Quit'}
						closable={false}
					>
						<div>
							Are you sure you want to quit? Your data has not been saved.
						</div>
					</Modal>
				</>
			)}
		</Formik>
	)
}

export default AddCategory
