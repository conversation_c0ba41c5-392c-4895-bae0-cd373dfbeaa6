import Link from "next/link";
import { GeneralInfoModal } from '@/types/models/general-info.model';
import NewsletterSubscription from "./NewsletterSubscription.component";
import FooterLinks from "./FooterLinks.component";

const FooterComponent = ({ copyright, address_one, mobile_one, mobile_two, phone_one, phone_two, email_one, email_two, app_info }: GeneralInfoModal) => {

	return (
		<footer>
			<div className="subscribe-section">
				<div className="container">
					<div className="footer-suscribe-content">
						<div className="subscribe-us-text">
							<h2>Subscribe to newsletter.</h2>
						</div>
						<div className="social-container">
							<NewsletterSubscription />
							<div className="social-button-container">
								<div className="social-us">
									<ul>
										<li>
											<a target='_blank' href="https://www.facebook.com/etiketnp" rel="noopener noreferrer">
												<i className="fa-brands fa-facebook-f"></i>
											</a>
										</li>
										<li>
											<a target='_blank' href="https://www.instagram.com/eticketnepal/?hl=en" rel="noopener noreferrer">
												<i className="fa-brands fa-instagram"></i>
											</a>
										</li>
										<li>
											<a target='_blank' href="https://x.com/eticket_nepal" rel="noopener noreferrer">
												<i className="fa-brands fa-x-twitter"></i>
											</a>
										</li>
										<li>
											<a target='_blank' href="https://www.youtube.com/channel/UC8qkrgLLMzBXWQJNTNli7EA" rel="noopener noreferrer">
												<i className="fa-brands fa-youtube"></i>
											</a>
										</li>
										<li>
											<a target='_blank' href="https://www.tiktok.com/@eticketnepal.com?lang=en" rel="noopener noreferrer">
												<i className="fa-brands fa-tiktok"></i>
											</a>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div className="footer-body">
				<div className="container">
					<div className="row">
						<div className="col-lg-4 col-md-4 col-sm-4 contact-footer">
							<h2>Contact</h2>
							<p>
								{address_one}<br />
								{[phone_one, phone_two, mobile_one, mobile_two].filter(Boolean).join(', ')}<br />
								{[email_one, email_two].filter(Boolean).join(', ')}
							</p>
						</div>

						<div className="col-lg-8 col-md-8 col-sm-8">
							<div className="row">
								<div className="col-lg-5 col-md-5 col-sm-5">
									<h2>About</h2>
									<p style={{
										fontSize: 12,
										lineHeight: 1.6
									}}>
										{app_info || ''}
									</p>
									<br />
									<a href="/">
										<img className="logo-image" src="/images/logo-full.png"
											style={{ height: 30 }} />
									</a>
								</div>

								<FooterLinks />
								<div className="col-lg-3 col-md-3 col-sm-3">
									<h2>Legals</h2>
									<p><Link href={'/terms'}>
										Terms & Conditions
									</Link></p>
									<p>
										<Link href={'/privacy'}>
											Privacy
										</Link>
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div className="footer-copy-right">
				<p>{copyright}</p>
			</div>
		</footer>
	)
}

export default FooterComponent;
