import { AddressData } from '@/types/index.types';
import React from 'react';
import GooglePlacesAutocomplete from 'react-google-places-autocomplete';
import { geocodeByPlaceId } from 'react-google-places-autocomplete';

interface CSSObject {
  [key: string]: any;
}

interface GooglePlacesProps {
  placeholder?: string;
  // defaultValue?: {
  //   value: {
  //     place_id: string;
  //     description: string;
  //   };
  //   label: string;
  // } | null;
	value?: {
    value: {
      place_id: string;
      description: string;
    };
    label: string;
  } | null;
  isClearable?: boolean;
  onAddressSelect: (addressData: AddressData | null) => void;
  error?: string;
  customStyles?: Record<string, (base: CSSObject) => CSSObject>;
}

const AddressAutocomplete: React.FC<GooglePlacesProps> = ({
	placeholder = 'Select Address',
	value = null,
	isClearable = true,
	onAddressSelect,
	error,
	customStyles,
}) => {
	const defaultStyles: Record<string, (base: CSSObject) => CSSObject> = {
		clearIndicator: (base) => ({
			...base,
			cursor: 'pointer',
			transition: 'color 0.2s ease-in-out',
			'&:hover': { color: 'red' },
		}),
		control: (base) => ({
			...base,
			boxShadow: 'none',
			'&:hover': { border: '.5px solid #c71782' },
			'&:focus-within': { border: '.5px solid #c71782' },
		}),
		placeholder: (base) => ({ ...base, marginLeft: '5px' }),
		singleValue: (base) => ({ ...base, marginLeft: '5px' }),
	};

	const handleSelect = (data: any) => {
		if (data?.value) {
			geocodeByPlaceId(data.value.place_id).then((results) => {
				const { address_components, formatted_address, geometry, place_id } = results[0];

				const getAddressComponent = (type: string) =>
					address_components.find((component) => component.types.includes(type))?.long_name || '';

				const street = `${getAddressComponent('street_number')} ${getAddressComponent('route')}`.trim();

				const addressData: AddressData = {
					street,
					city: getAddressComponent('locality'),
					country: getAddressComponent('country'),
					full_address: formatted_address,
					description: data.value.description,
					zipCode: getAddressComponent('postal_code'),
					lat: geometry.location.lat(),
					lng: geometry.location.lng(),
					place_id,
					zone: getAddressComponent('administrative_area_level_1'),
				};

				onAddressSelect(addressData);
			});
		} else {
			onAddressSelect(null);
		}
	};

	return (
		<div>
			{error && <span className="text-danger">({error})</span>}
			<GooglePlacesAutocomplete
				selectProps={{
					placeholder,
					value,
					defaultInputValue: value?.label,
					isClearable,
					onChange: handleSelect,
					styles: customStyles || defaultStyles,
				}}
			/>
		</div>
	);
};

export default AddressAutocomplete;
