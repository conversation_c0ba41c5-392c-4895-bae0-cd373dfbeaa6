'use client';

import { Dropdown, <PERSON><PERSON>, Modal } from 'antd';
import FullScreenModal from '../FullScreenModal.component'
import { ArrowLeftOutlined, CaretDownFilled, ExportOutlined, WarningOutlined } from '@ant-design/icons';
import AddEventStep1 from './steps/AddEventStep1';
import StepCompleteSuccess from '../StepCompleteSuccess.component';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Formik, FormikProps } from 'formik';
import AddEventStep2 from './steps/AddEventStep2';
import AddEventStep3 from './steps/AddEventStep3';
import { AddEventType, VenueDataType } from '@/types/forms/add-event.type';
import { addEventsValidation } from '@/utils/validation.util';
import { AppDispatch, RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { addEventAction, eventStateChange } from '@/store/slices/event.slice';
import { fetchAllCategoriesAction } from '@/store/slices/category.slice';
import { fetchAllArtistsAction } from '@/store/slices/artist.slice';
import { fetchAllVenuesAction } from '@/store/slices/venue.slice';
import { debounce } from 'lodash';
import { CategoryModel } from '@/types/models/category.model';
import { ArtistModel } from '@/types/models/artist.model';
import { VenueModel } from '@/types/models/venue.model';
import { EventModel } from '@/types/models/event.model';
import dayjs from 'dayjs';
import TimeTicker from '../common/TimeTicker';
import { EventFaqModel } from '@/types/models/event-faq.model';
import { EventGalleryModel } from '@/types/models/event-gallery.model';
import { EventActivityModel } from '@/types/models/event-activity.model';
import { generateSlug } from '@/utils/general.util';
import { getEncryptedCookie } from '@/utils/cookie.util';
import { UserModel } from '@/types/models/user.model';
import { fetchGeneralInfoAction } from '@/store/slices/general-info.slice';
import { GeneralInfoModal } from '@/types/models/general-info.model';

const AddEvent = ({ showModal, setShowModal } : {showModal: boolean, setShowModal: React.Dispatch<React.SetStateAction<boolean>>}) => {
	const dispatch = useDispatch<AppDispatch>();
	const user = getEncryptedCookie('_eticket_user_') as UserModel;

	const [addEvents, setAddEvents] = useState({
		addEventComplete: false,
		step: 1,
	});
	const [showCloseModal, setShowCloseModal] = useState<boolean>(false);
	const [draftSavedTime, setDraftSavedTime] = useState<dayjs.Dayjs>();
	// const [shouldDebounce, setShouldDebounce] = useState<boolean>(true);

	const saveDraftRef = useRef(true);
	const isCreatingEventRef = useRef(false);

	const { status } = useSelector((state: RootState) => state.event);
	const { record: eventRecord, processingId: processingEventId } = useSelector((state: RootState) => state.event);
	const { rows: categories } = useSelector((state: RootState) => state.category);
	const { record: generalInfo } = useSelector((state: RootState) => state.generalInfo) as { record: GeneralInfoModal };

	const resetValuesAfterClose = () => {
		setShowModal(false);
		dispatch(eventStateChange({ key: 'record', value: null }));
		dispatch(eventStateChange({ key: 'processingId', value: null }));
		setDraftSavedTime(undefined);
		setAddEvents({ addEventComplete: false, step: 1 });
		saveDraftRef.current = true;
		isCreatingEventRef.current = false;
		window.history.replaceState(
			null,
			'',
			user.is_partner ? '/user/dashboard/events' : '/user/dashboard'
		);
	}

	useEffect(() => {
		if (showModal) {
			dispatch(fetchAllCategoriesAction({ getMainCategories: true, type: 'active' }));
			dispatch(fetchAllArtistsAction({ type: 'active', perPage: 10 }));
			dispatch(fetchAllVenuesAction({ type: 'active', perPage: 10 }));
			dispatch(fetchGeneralInfoAction());
		}
	}, [dispatch, showModal, processingEventId])

	function processEventData(eventRecord: EventModel): Record<number, VenueDataType> {
		const venueData: Record<number, VenueDataType> = {}

		if (eventRecord?.venues?.length === 0 || !eventRecord.venues) return venueData

		eventRecord?.venues?.forEach(venue => {
			const venueId = venue.id
			const venueTickets = eventRecord?.tickets?.filter(ticket => ticket.venue_id === venueId) || []
			// const venueDate = eventRecord?.dates?.find(date => date.venue_id === venueId)

			let tickets: VenueDataType['tickets'] = [];
			if (venueTickets.length === 0) {
				tickets = [{
					name: '',
					count: '',
					amount: '',
				}];
			} else {
				tickets = venueTickets.map(ticket => ({
					name: ticket.name || '',
					count: ticket.number_of_tickets ? ticket.number_of_tickets.toString() : '',
					amount: ticket.price ? ticket.price.toString() : '',
				}));
			}

			venueData[venueId] = {
				ticket_type: (venueTickets?.[0]?.ticket_type ?? 'paid') as 'paid' | 'free',
				// date_time: venueDate ? venueDate.date.toString() : '',
				start_date_time: eventRecord?.start_date_time || '',
				end_date_time: eventRecord?.end_date_time || '',
				tickets: tickets,
			}
		})

		return venueData
	}

	const debouncedRequest = useMemo(
		() => debounce(async (values) => {
			const res = processingEventId ? await dispatch(addEventAction(values, processingEventId)) : await dispatch(addEventAction(values));
			// setShouldDebounce(true);
			if (isCreatingEventRef.current) isCreatingEventRef.current = false;
			if (res && res.status) {
				setDraftSavedTime(dayjs());
				if (res.status === 201) {
					window.history.replaceState(
						null,
						'',
						`/user/dashboard/events/${res.data.data.id}`
					);
				}
			}
		}, 1000),
		[dispatch, processingEventId]
	);

	const handleDraftSave = (data: AddEventType) => {
		if (!saveDraftRef.current || !data.step_1_info.title || isCreatingEventRef.current) return;

		if (!processingEventId) {
			isCreatingEventRef.current = true;
		}
		debouncedRequest(data);
	}

	return (
		<Formik<AddEventType>
			key={eventRecord?.id}
			initialValues={{
				step_1_info: {
					title: eventRecord?.title || '',
					// duration: eventRecord?.duration || '',
					categories: eventRecord?.categories?.map((category: CategoryModel) => (category.id)) || [],
					about: eventRecord?.about || '',
					cover_image: eventRecord?.image || '',
				},
				step_2_info: {
					artists: eventRecord?.artists?.map((artist: ArtistModel) => (artist.id)) || [],
					terms: eventRecord?.terms && eventRecord?.terms[0] ? eventRecord?.terms[0].terms : '',
					galleries: eventRecord?.galleries?.map((gallery: EventGalleryModel) => (gallery.name)) || [],
					faqs: eventRecord?.faqs?.map((faq: EventFaqModel) => ({ question: faq.question, answer: faq.answer! })) || [],
					activities: eventRecord?.activities?.map((activity: EventActivityModel) => ({ title: activity.title, details: activity.details })) || [],
				},
				step_3_info: {
					use_external_ticket_link: eventRecord?.external_ticket_link ? true : false,
					external_ticket_link: eventRecord?.external_ticket_link || '',
					venues: eventRecord?.venues?.map((venue: VenueModel) => (venue.id)) || [],
					venue_data: eventRecord ? processEventData(eventRecord) : {},
				},
				is_published: false
			}}
			validate={(data) => {
				// if (!data.is_published && data.step_1_info?.title && shouldDebounce) debouncedRequest(data);
				// if (!processingEventId && data.step_1_info?.title) setShouldDebounce(false); // prevent adding new event when initial request is being processed
				handleDraftSave(data);
				return addEventsValidation(data);
			}}
			onSubmit={async (values) => {
				if (!processingEventId) return;
				const res = await dispatch(addEventAction(values, processingEventId));
				if (res && res.status === 200) {
					setDraftSavedTime(undefined);
					setAddEvents({ ...addEvents, addEventComplete: true });
				}
			}}
		>
			{(formikProps: FormikProps<AddEventType>) => (
				<>
					<FullScreenModal
						className={'dashboard_add_form'}
						onClose={() => {
							if (!addEvents.addEventComplete && draftSavedTime) setShowCloseModal(true);
							else {
								formikProps.resetForm();
								resetValuesAfterClose();
							}
						}}
						show={showModal}
					>
						{!addEvents.addEventComplete ? (
							<div className="modal-body-container-form">
								<div className="add_form_header">
									<div className="container">
										<div className="d-flex justify-content-between align-items-center">
											<div>
												<h1>{'Add Event'}</h1>
												<p>
													Step
													<Dropdown
														trigger={['click']}
														menu={{
															items: [
																{
																	key: '1',
																	label: (
																		<a
																			onClick={(e) => {
																				e.preventDefault();
																				setAddEvents({
																					...addEvents,
																					step: 1,
																				})
																			}}
																		>
																			1) Basic Information
																		</a>
																	),
																},
																{
																	key: '2',
																	label: (
																		<a
																			onClick={(e) => {
																				e.preventDefault();
																				setAddEvents({
																					...addEvents,
																					step: 2,
																				})
																			}}
																		>
																			2) Artist, FAQ etc
																		</a>
																	),
																	disabled: !!formikProps.errors.step_1_info,
																},
																{
																	key: '3',
																	label: (
																		<a
																			onClick={(e) => {
																				e.preventDefault();
																				setAddEvents({
																					...addEvents,
																					step: 3,
																				})
																			}}
																		>
																			3) City / Location / Price
																		</a>
																	),
																	disabled: !!formikProps.errors.step_1_info || !!formikProps.errors.step_2_info,
																},
															]}
														}
													>
														<a onClick={async () => {
															// setShouldDebounce(false);
															saveDraftRef.current = false;
															await formikProps.validateForm()
															// setShouldDebounce(true);
															saveDraftRef.current = true;
														}}>
															{addEvents.step === 1 ? ' one' : (addEvents.step === 2 ? ' two' : ' three')} <CaretDownFilled />
														</a>
													</Dropdown>
														out of <span>three</span>.
												</p>
											</div>
											<div className='d-flex gap-4'>
												{draftSavedTime && <p style={{ color: '#777', fontWeight: 400 }}>
													{<TimeTicker initialTime={draftSavedTime} showDraftSaved={true} />}
												</p>}
												{(draftSavedTime || eventRecord) && (
													<p
														style={{ color: '#777', fontSize: 16, fontWeight: 400, cursor: 'pointer' }}
														onClick={async () => {
															if (!processingEventId) return;
															// const res = await dispatch(fetchEventAction(processingEventId));
															// if (res && res.status === 200) window.open(`/events/${res.data.slug}-${res.data.id}`, '_blank', 'noopener,noreferrer');
															window.open(`/events/${generateSlug(formikProps.values.step_1_info.title)}-${processingEventId}`, '_blank', 'noopener,noreferrer');
														}}
													>
														Preview <ExportOutlined style={{ fontSize: 12 }} />
													</p>
												)}
											</div>
										</div>
									</div>
								</div>

								<form>
									<div className="add_form_container">
										<div className="container">
											{addEvents.step === 1 && <AddEventStep1 formikProps={formikProps} categories={categories} />}
											{addEvents.step === 2 && <AddEventStep2 formikProps={formikProps} />}
											{addEvents.step === 3 && <AddEventStep3 formikProps={formikProps} />}
										</div>
									</div>
								</form>

								{(eventRecord?.published_at && eventRecord?.approved_at && !eventRecord?.cancelled_at && eventRecord.status) && (
									<div className="sticky-bottom-alert">
										<p><WarningOutlined /> Caution: You are editing a live event.</p>
									</div>
								)}

								<div className="add_form_footer">
									<div className="container-fluid">
										<div className="row">
											<div className="col-md-12">
												<div className="d-flex justify-content-between">
													<p style={{ color: '#999' }}>
														<small>Need assistance? Contact us at <a
															href={"mailto:" + (generalInfo?.email_one || '<EMAIL>')}
															target="_blank"
															rel="noopener noreferrer">{generalInfo?.email_one || '<EMAIL>'}</a>
														</small>
													</p>
													<div>
														{addEvents.step === 1 ? <a href=""
															onClick={(e) => {
																e.preventDefault();
																if (draftSavedTime) setShowCloseModal(true);
																else {
																	formikProps.resetForm();
																	resetValuesAfterClose();
																}
															}}
														>Cancel</a> : <a href=""
															onClick={(e) => {
																e.preventDefault();
																setAddEvents({
																	...addEvents,
																	step: addEvents.step - 1,
																})
															}}
														><ArrowLeftOutlined /> Back</a>}&nbsp;&nbsp;&nbsp;
														<Button
															loading={status === 'saving' && addEvents.step === 3}
															disabled={
																(addEvents.step === 1 && !!formikProps.errors.step_1_info) ||
																(addEvents.step === 2 && (!!formikProps.errors.step_1_info || !!formikProps.errors.step_2_info)) ||
																(addEvents.step === 3 && !formikProps.isValid) ||
																(!processingEventId && !formikProps.dirty) ||
																(status === 'saving')
															}
															onClick={async () => {
																saveDraftRef.current = false;

																if (addEvents.step !== 3) {
																	// setShouldDebounce(false);
																	const errors = await formikProps.validateForm();
																	// setShouldDebounce(true);
																	if (errors) {
																		if (
																			(addEvents.step === 1 && errors.step_1_info) ||
																			(addEvents.step === 2 && (errors.step_1_info || errors.step_2_info))
																		) return;
																	}
																}
																if (addEvents.step < 3) {
																	setAddEvents({
																		...addEvents,
																		step: addEvents.step + 1,
																	})
																} else if (addEvents.step === 3) {
																	await formikProps.setFieldValue('is_published', true);
																	await formikProps.submitForm();
																}

																saveDraftRef.current = true;
															}}
															size={'large'}
															type={'primary'}>
															{(addEvents.step === 3) ?
																'Publish Event Now' : 'Proceed to Next Step'}
														</Button>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						) : (
							<StepCompleteSuccess
								actionText={user?.user_type === 'admin' ? 'View Published Event' : 'Preview Published Event'}
								supportEmail={generalInfo?.email_one}
								supportPhone={generalInfo?.phone_one}
								onAction={async () => {
									if (!processingEventId) return;
									window.open(`/events/${generateSlug(formikProps.values.step_1_info.title)}-${processingEventId}`, '_blank', 'noopener,noreferrer');
								}}
								title={'Hurray, your event has been added successfully!'}
								message={
									user?.user_type === 'admin' ? (
										'Your event has been published successfully. Users can now view your event.'
									) : (
										<span>
											The event you just created is <u style={{ color: '#555', fontWeight: 'bold' }}>under admin review</u>. This will take a while. You will receive an email notification about the acceptance/rejection.
										</span>
									)
								}
							/>
						)}
					</FullScreenModal>
					<Modal
						title={'Are you sure?'}
						open={showCloseModal}
						onOk={() => {
							setShowCloseModal(false);
							formikProps.resetForm();
							resetValuesAfterClose();
						}}
						onCancel={() => setShowCloseModal(false)}
						okText={'Quit'}
						closable={false}
					>
						<div>
							Click the Quit button to close the add event window. Your Progress has been saved. You can continue the process later.
						</div>
					</Modal>
				</>
			)}
		</Formik>
	)
}

export default AddEvent
