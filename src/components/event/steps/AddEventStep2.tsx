import { AppInputDebouncedSelectField, AppInputField, AppInputTextField } from "@/components/common/AppInputField.component";
import { <PERSON><PERSON>, Tabs } from "antd";
import { ActivityDataType, AddEventProps, FaqDataType } from "@/types/forms/add-event.type";
import { MinusOutlined, PlusOutlined } from "@ant-design/icons";
import { Errors } from "@/types/index.types";
import ImageUpload from "@/components/common/ImageUpload.component";
import { debounce } from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { fetchAllArtistsAction } from "@/store/slices/artist.slice";

const AddEvent: React.FC<AddEventProps> = ({ formikProps }) => {
	const dispatch = useDispatch<AppDispatch>();
	const { values, setFieldValue, errors } = formikProps;
	const { rows: artists, status } = useSelector((state: RootState) => state.artist);
	const { record: event } = useSelector((state: RootState) => state.event);

	const addFAQ = () => {
		const faqs = [...(values.step_2_info.faqs || []), { question: '', answer: '' }];
		setFieldValue('step_2_info.faqs', faqs);
	};

	const removeFAQ = (index: number) => {
		const faqs = [...(values.step_2_info.faqs || [])];
		faqs.splice(index, 1);
		setFieldValue('step_2_info.faqs', faqs);
	};

	const addActivity = () => {
		const activities = [...(values.step_2_info.activities || []), { title: '', details: '' }];
		setFieldValue('step_2_info.activities', activities);
	};

	const removeActivity = (index: number) => {
		const activities = [...(values.step_2_info.activities || [])];
		activities.splice(index, 1);
		setFieldValue('step_2_info.activities', activities);
	};

	const debouncedArtistSearch = debounce(async (searchKey: string) => {
		dispatch(fetchAllArtistsAction({ type: 'active', perPage: 10, search: searchKey.trim() }));
	}, 500);

	return (
		<div className="row">
			<div className="col-md-12">
				<AppInputDebouncedSelectField
					id="artists"
					value={values.step_2_info.artists}
					label="Select Artists"
					info={'Search artist by their name. Didn\'t find artist you are looking for?'}
					options={[...(event?.artists ?? []), ...(artists ?? [])].map((item) => ({
						label: item.stage_name ? `${item.name} (${item.stage_name})` : item.name,
						value: item.id,
					}))}
					onChangeInput={(value: number[]) => {
						setFieldValue('step_2_info.artists', value);
					}}
					errorMessage={errors.step_2_info?.artists as string}
					onSearch={debouncedArtistSearch}
					searching={status === 'fetching'}
				/>
			</div>

			<div className="col-md-12">
				<Tabs type="card" defaultActiveKey="1"
					items={[
						{
							key: '1',
							label: 'Term and Conditions',
							children: <AppInputTextField
								id="terms_and_conditions"
								required={true}
								value={values.step_2_info.terms}
								onChangeInput={(value: string) => {
									setFieldValue('step_2_info.terms', value);
								}}
								label="Terms & Conditions" info="Describe what are the terms and conditions for the event (minimum 50 characters / max 500 characters)"
								placeholder="My Terms and conditions..."
								errorMessage={errors.step_2_info?.terms}
								maxLength={500}
							/>
						},
						{
							key: '2',
							label: 'Gallery (optional)',
							children: (
								<ImageUpload
									initialImages={values.step_2_info.galleries || []}
									onImagesChange={(images) => {
										setFieldValue('step_2_info.galleries', images)
									}}
								/>
							)
						},
						{
							key: '3',
							label: 'FAQs (optional)',
							children: (
								<>
									{(values.step_2_info.faqs || []).map((faq, index) => (
										<div key={index} className="faq-input-container">
											<AppInputField
												id={`faq_question_${index}`}
												value={faq.question}
												label={`Question ${index + 1}`}
												info={'Question about your event'}
												onChangeInput={(value: string) => {
													setFieldValue(`step_2_info.faqs[${index}].question`, value);
												}}
												placeholder={'eg. What is your event about?'}
												errorMessage={(errors.step_2_info?.faqs?.[index] as Errors<FaqDataType>)?.question}
											/>
											<AppInputTextField
												id={`faq_answer_${index}`}
												value={faq.answer}
												onChangeInput={(value: string) => {
													setFieldValue(`step_2_info.faqs[${index}].answer`, value);
												}}
												label={`Answer ${index + 1}`}
												placeholder={'eg. This event is about...'}
												rows={2}
												info={'Answer to the above question (max 500 characters)'}
												errorMessage={(errors.step_2_info?.faqs?.[index] as Errors<FaqDataType>)?.answer}
											/>
											<Button
												type="dashed"
												icon={<MinusOutlined />}
												onClick={() => removeFAQ(index)}
												style={{ marginTop: '10px', marginBottom: '10px' }}
												danger
											>
                        Remove FAQ
											</Button>
										</div>
									))}
									{values.step_2_info?.faqs && values.step_2_info.faqs.length > 0 && (
										<p>Fill above question & answer input field to add more faqs</p>
									)}
									<Button
										type="dashed"
										onClick={addFAQ}
										icon={<PlusOutlined />}
										style={{ marginTop: '15px' }}
										disabled={
											Array.isArray(errors.step_2_info?.faqs) &&
												errors.step_2_info.faqs.length > 0
										}
									>
                    Add FAQ
									</Button>
								</>
							),
						},
						{
							key: '4',
							label: 'Activities (optional)',
							children: (
								<>
									{(values.step_2_info.activities || []).map((activity, index) => (
										<div key={index} className="faq-input-container">
											<AppInputField
												id={`activity_title_${index}`}
												value={activity.title}
												label={`Activity Title ${index + 1}`}
												info={'Title of your activity'}
												onChangeInput={(value: string) => {
													setFieldValue(`step_2_info.activities[${index}].title`, value);
												}}
												placeholder={'eg. Opening Ceremony'}
												errorMessage={(errors.step_2_info?.activities?.[index] as Errors<ActivityDataType>)?.title}
											/>
											<AppInputTextField
												id={`activity_details_${index}`}
												value={activity.details}
												onChangeInput={(value: string) => {
													setFieldValue(`step_2_info.activities[${index}].details`, value);
												}}
												label={`Activity Details ${index + 1}`}
												placeholder={'eg. This is the opening ceremony of the event'}
												rows={2}
												info={'Details about the above activity (max 500 characters)'}
												errorMessage={(errors.step_2_info?.activities?.[index] as Errors<ActivityDataType>)?.details}
											/>
											<Button
												type="dashed"
												icon={<MinusOutlined />}
												onClick={() => removeActivity(index)}
												style={{ marginTop: '10px', marginBottom: '10px' }}
												danger
											>
                        Remove Activity
											</Button>
										</div>
									))}
									{values.step_2_info?.activities && values.step_2_info.activities.length > 0 && (
										<p>Fill above input fields to add more activity</p>
									)}
									<Button
										type="dashed"
										onClick={addActivity}
										icon={<PlusOutlined />}
										style={{ marginTop: '15px' }}
										disabled={
											Array.isArray(errors.step_2_info?.activities) &&
												errors.step_2_info.activities.length > 0
										}
									>
                    Add Activity
									</Button>
								</>
							)
						}
					]}
				/>
			</div>
		</div>
	)
}

export default AddEvent
