import { AppInputField, AppInputSelectField, AppInputTextField } from "@/components/common/AppInputField.component";
import { AddEventProps } from "@/types/forms/add-event.type";
import ImageCropper from "@/components/ImageCropper.component";
import { Space } from "antd";
import { CloseCircleFilled } from "@ant-design/icons";
import { getFullImageUrl } from "@/utils/general.util";

const AddEvent: React.FC<AddEventProps> = ({ formikProps, categories }) => {
	const { values, setFieldValue, errors } = formikProps;

	return (
		<div className="row">
			<ImageCropper
				value={values.step_1_info.cover_image}
				renderUploadTrigger={(openUpload) => (
					<div className="col-md-12">
						<div onClick={openUpload} className="drag_drp_cover">
							<div>
								<h3><i className="fas fa-images" /></h3>
								<p>Click to upload event's cover picture.</p>
							</div>
						</div>
					</div>
				)}
				renderPreview={(resetImage) => (
					<div className="col-md-12">
						<div className="input-container">
							<div className="image-upload-preview">
								<div className="image-preview">
									<img src={getFullImageUrl(values.step_1_info.cover_image || '')} alt="" />
								</div>
								<div className="image-info">
									<h1>Cover Image of the event</h1>
								</div>
								<div className="image-action">
									{/* <a href="" onClick={(e) => e.preventDefault()}>
										<EyeFilled />
									</a> */}
									<a href="" onClick={(e) => {
										e.preventDefault();
										resetImage();
									}}>
										<CloseCircleFilled />
									</a>
								</div>
							</div>
						</div>
					</div>
				)}
				onCropComplete={(croppedImageUrl) => {
					setFieldValue('step_1_info.cover_image', croppedImageUrl.split('/').at(-1));
				}}
				errorMessage={errors.step_1_info?.cover_image}
			/>

			<div className="col-md-12">
				<AppInputField
					id="event_title"
					required={true}
					value={values.step_1_info.title}
					label={'Event Title'}
					info={'Title of your event'}
					onChangeInput={(value: string) => {
						setFieldValue('step_1_info.title', value);
					}}
					placeholder={'My awesome SEO friendly event title'}
					errorMessage={errors.step_1_info?.title}
					maxLength={150}
				/>
			</div>

			{/* <div className="col-md-6">
				<AppInputField
					id="event_duration"
					value={values.step_1_info.duration}
					onChangeInput={(value: string) => {
						setFieldValue('step_1_info.duration', value);
					}}
					label={'Duration'}
					mask={'00:00'}
					info={'Please specify the duration of your event in hours.'}
					placeholder={'hh:mm'}
					errorMessage={errors.step_1_info?.duration}
				/>
			</div> */}

			<div className="col-md-12">
				<AppInputSelectField
					id="event_categories"
					required={true}
					value={values.step_1_info.categories}
					maxCount={3}
					mode={'multiple'}
					onChangeInput={(value: string) => {
						setFieldValue('step_1_info.categories', value);
					}}
					label={'Event Categories'} info={'Select max 3 categories'}
					options={categories?.map((item, index) => ({
						label: (
							<div style={{ fontWeight: 'bold', color: '#c71782' }}>
								{index > 0 && <hr/>}
								{item?.icon_name && <i className={item?.icon_name} style={{ marginRight: '5px' }} />}
								{item.name}
							</div>
						),
						value: item.id,
						icon: item.icon_name,
						options: item.sub_categories?.map(subItem => ({
							label: subItem.name,
							value: subItem.id,
							icon: subItem.icon_name
						}))
					}))}
					optionRender={(option) => (
						<Space>
							<i className={option?.data.icon} />
							{option?.label}
						</Space>
					)}
					errorMessage={errors.step_1_info?.categories as string}
				/>
			</div>

			<div className="col-md-12">
				<AppInputTextField
					id="event_about"
					required={true}
					value={values.step_1_info.about}
					onChangeInput={(value: string) => {
						setFieldValue('step_1_info.about', value);
					}}
					label={'About Event'}
					placeholder={'Description about my event ....'}
					rows={8}
					info={'Short info about your event (minimum 50 characters / max 500 characters)'}
					errorMessage={errors.step_1_info?.about}
				/>
			</div>
		</div>
	)
}

export default AddEvent
