import { AppInputDateField, AppInputDebouncedSelectField, AppInputField, AppInputRadioField } from "@/components/common/AppInputField.component";
import { AddEventProps, VenueDataType } from "@/types/forms/add-event.type";
import { Errors } from "@/types/index.types";
import { Tabs, Button, Checkbox } from "antd";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { debounce } from "lodash";
import { fetchAllVenuesAction } from "@/store/slices/venue.slice";

const AddEvent: React.FC<AddEventProps> = ({ formikProps }) => {
	const dispatch = useDispatch<AppDispatch>();
	const { values, setFieldValue, errors, setValues } = formikProps;
	const { rows: venues, status } = useSelector((state: RootState) => state.venue);
	const { record: event } = useSelector((state: RootState) => state.event);

	const addTicket = (venueId: number) => {
		const venueData = { ...values.step_3_info.venue_data };
		const newTicket = {
			name: `Ticket ${(venueData[venueId].tickets || []).length + 1}`,
			count: '',
			amount: ''
		};
		venueData[venueId].tickets = [...(venueData[venueId].tickets || []), newTicket];
		setFieldValue('step_3_info.venue_data', venueData);
	};

	const removeTicket = (venueId: number, index: number) => {
		const venueData = { ...values.step_3_info.venue_data };
		venueData[venueId].tickets = venueData[venueId].tickets?.filter((_, i) => i !== index) || [];
		setFieldValue('step_3_info.venue_data', venueData);
	};

	const updateTicket = (venueId: number, index: number, field: string, value: string) => {
		const venueData = { ...values.step_3_info.venue_data };
		if (venueData[venueId].tickets && venueData[venueId].tickets[index]) {
			venueData[venueId].tickets[index] = {
				...venueData[venueId].tickets[index],
				[field]: value
			};
			setFieldValue('step_3_info.venue_data', venueData);
		}
	};

	const debouncedVenuesSearch = debounce(async (searchKey: string) => {
		dispatch(fetchAllVenuesAction({ type: 'active', perPage: 10, search: searchKey.trim() }));
	}, 500);

	const handleToggleExternalLink = (checked: boolean) => {
		if (checked) {
			const venueData = { ...values.step_3_info.venue_data };
			(values.step_3_info.venues || []).forEach((venueId: number) => {
				if (!venueData[venueId]) return;
				venueData[venueId].tickets = [];
			});

			setValues({
				...values,
				step_3_info: {
					...values.step_3_info,
					use_external_ticket_link: checked,
					venue_data: venueData,
				}
			});
		} else {
			const venueData = { ...values.step_3_info.venue_data };
			(values.step_3_info.venues || []).forEach((venueId: number) => {
				if (!venueData[venueId]) return;
				venueData[venueId].tickets = [
					{ name: 'Silver', count: '', amount: '' },
				];
			});

			setValues({
				...values,
				step_3_info: {
					...values.step_3_info,
					use_external_ticket_link: checked,
					external_ticket_link: '',
					venue_data: venueData,
				}
			});
		}
	};

	return (
		<div className="row">
			<div className="col-md-12">
				<AppInputDebouncedSelectField
					id="venue_id"
					required={true}
					value={values.step_3_info.venues}
					maxCount={1}
					onChangeInput={(value: number[]) => {
						const venueData = { ...values.step_3_info.venue_data };
						value.forEach((venueId) => {
							if (!venueData[venueId]) venueData[venueId] = {
								ticket_type: 'paid',
								start_date_time: '',
								end_date_time: '',
								tickets: [
									{ name: 'Silver', count: '', amount: '' },
									{ name: 'Gold', count: '', amount: '' },
								],
							};
						});
						values.step_3_info.venues.forEach((venueId) => {
							if (!value.includes(venueId)) delete venueData[venueId];
						});
						setFieldValue('step_3_info', {
							venues: value,
							venue_data: venueData,
						});
					}}
					options={[...(event?.venues ?? []), ...(venues ?? [])].map((item) => ({
						label: item.name,
						value: item.id,
					}))}
					label={'Select Venue'}
					info={'Place where your event gets organized.'}
					errorMessage={errors.step_3_info?.venues as string}
					onSearch={debouncedVenuesSearch}
					searching={status === 'fetching'}
				/>
			</div>

			{values.step_3_info.venues && values.step_3_info.venues.length > 0 ?
				<div className="col-md-12">
					<Tabs type="card" onChange={() => {}}
						items={values.step_3_info.venues.map((venueId: number) => {
							const venue = venues?.find((venue) => venue.id === venueId);
							return {
								key: venueId.toString(),
								label: venue?.name,
								children: (
									<div className="row venue-info-container">
										<div className="col-md-12">
											<h3>Event's Date and Time</h3>
											<p style={{ maxWidth: 1000 }}>
												Set up your event start and end date and time.
											</p>
											<hr />
											<div className="row">
												<div className="col-md-6">
													<AppInputDateField
														id={`venue_${venueId}_start_date_time`}
														required={true}
														value={values.step_3_info.venue_data[venueId]?.start_date_time}
														onChangeInput={(date: dayjs.Dayjs) => {
															const venueData = { ...values.step_3_info.venue_data };
															venueData[venueId].start_date_time = date.toISOString();
															setFieldValue('step_3_info.venue_data', venueData);
														}}
														showTime={true}
														placeholder={'Select Date Time'}
														label="Start Date Time"
														info="Select the start date and time of your event"
														disabledDate={(current: dayjs.Dayjs) => {
															const endDate = values.step_3_info.venue_data[venueId]?.end_date_time;
															if (endDate) {
																return (
																	current &&
																	(current > dayjs(endDate).endOf('day') || current < dayjs().startOf('day'))
																);
															}
															return current && current < dayjs().startOf('day');
														}}
														disabledTime={(current) => {
															const endDateTime = values.step_3_info.venue_data[venueId]?.end_date_time;
															if (!current || !endDateTime) return {};

															const endDate = dayjs(endDateTime);
															if (current.isSame(endDate, 'day')) {
																// Disable hours >= end time hour
																return {
																	disabledHours: () => Array.from({ length: 24 }, (_, i) => i).filter(hour => hour > endDate.hour()),
																	disabledMinutes: (selectedHour) => {
																		if (selectedHour === endDate.hour()) {
																			return Array.from({ length: 60 }, (_, i) => i).filter(min => min >= endDate.minute());
																		}
																		return [];
																	},
																};
															}
															return {};
														}}
														errorMessage={errors.step_3_info?.venue_data?.[venueId]?.start_date_time}
													/>
												</div>

												<div className="col-md-6">
													<AppInputDateField
														id={`venue_${venueId}_end_date_time`}
														required={true}
														value={values.step_3_info.venue_data[venueId]?.end_date_time}
														onChangeInput={(date: dayjs.Dayjs) => {
															const venueData = { ...values.step_3_info.venue_data };
															venueData[venueId].end_date_time = date.toISOString();
															setFieldValue('step_3_info.venue_data', venueData);
														}}
														showTime={true}
														placeholder={'Select Date Time'}
														label="End Date Time"
														info="Select the end date and time of your event"
														disabledDate={(current: dayjs.Dayjs) => {
															const startDate = values.step_3_info.venue_data[venueId]?.start_date_time;
															if (startDate) {
																return (
																	current &&
																	(current < dayjs(startDate).startOf('day') || current < dayjs().startOf('day'))
																);
															}
															return current && current < dayjs().startOf('day');
														}}
														disabledTime={(current) => {
															const startDateTime = values.step_3_info.venue_data[venueId]?.start_date_time;
															if (!current || !startDateTime) return {};

															const startDate = dayjs(startDateTime);
															if (current.isSame(startDate, 'day')) {
																// Disable hours < start time hour
																return {
																	disabledHours: () => Array.from({ length: 24 }, (_, i) => i).filter(hour => hour < startDate.hour()),
																	disabledMinutes: (selectedHour) => {
																		if (selectedHour === startDate.hour()) {
																			return Array.from({ length: 60 }, (_, i) => i).filter(min => min <= startDate.minute());
																		}
																		return [];
																	},
																};
															}
															return {};
														}}
														errorMessage={errors.step_3_info?.venue_data?.[venueId]?.end_date_time}
													/>
												</div>
											</div>
										</div>
										<div className="col-md-12">
											<div>
												<h3>Event's Ticket and Pricing</h3>
												<p style={{ maxWidth: 1000 }}>
                          Set up your event tickets. You can add multiple ticket types for paid events.
												</p>
												<hr />

												<AppInputRadioField
													id="ticket_type"
													value={values.step_3_info.venue_data[venueId]?.ticket_type}
													onChangeInput={(value: 'free' | 'paid') => {
														const venueData = { ...values.step_3_info.venue_data };
														venueData[venueId].ticket_type = value;
														if (value === 'paid') {
															venueData[venueId].tickets = [
																{ name: 'Silver', count: '', amount: '' },
																{ name: 'Gold', count: '', amount: '' },
															];
														} else if (value === 'free') {
															venueData[venueId].tickets = [
																{ name: 'Free', count: '', amount: '0' },
															];
														} else {
															delete venueData[venueId].tickets;
														}
														setFieldValue('step_3_info.venue_data', venueData);
													}}
													radioOptions={[
														{ label: 'Paid', value: 'paid' },
														{ label: 'Free', value: 'free' },
													]}
													radioOptionType={'button'}
													errorMessage={errors.step_3_info?.venue_data?.[venueId]?.ticket_type}
												/>

												<br />

												{values.step_3_info.venue_data[venueId]?.ticket_type === 'paid' && (
													<>
														<Checkbox
															checked={values.step_3_info.use_external_ticket_link}
															onChange={(e) => handleToggleExternalLink(e.target.checked)}
															style={{ marginBottom: 20,  }}
														>
															Use external ticket link
														</Checkbox>

														{values.step_3_info.use_external_ticket_link ? (
															<AppInputField
																id="external_ticket_link"
																required={true}
																label="External Ticket Link"
																placeholder="https://your-ticketing-url.com/event/xyz"
																value={values.step_3_info.external_ticket_link}
																onChangeInput={(val: string) => {
																	setFieldValue("step_3_info.external_ticket_link", val)
																}}
																errorMessage={errors.step_3_info?.external_ticket_link as string}
																maxLength={200}
															/>
														) : (
															<div>
																{values.step_3_info.venue_data[venueId]?.tickets?.map((ticket, index) => (
																	<div key={index}>
																		<div className="row">
																			<div className="col-md-3">
																				<h4 style={{
																					fontWeight: 'bold',
																					fontSize: 20,
																				}}>
																					<AppInputField
																						id={`ticket_name_${venueId}_${index}`}
																						required={true}
																						style={{
																							display: 'flex',
																							flexDirection: 'column',
																						}}
																						label="Ticket Name"
																						value={ticket.name}
																						placeholder="eg: VIP"
																						onChangeInput={(value: string) => updateTicket(venueId, index, 'name', value)}
																						errorMessage={(errors.step_3_info?.venue_data?.[venueId]?.tickets?.[index] as Errors<NonNullable<VenueDataType['tickets']>[0]>)?.name}
																					/>
																				</h4>
																			</div>
																			<div className="col-md-4">
																				<AppInputField
																					id={`ticket_count_${venueId}_${index}`}
																					required={true}
																					type="number"
																					onChangeInput={(value: string) => updateTicket(venueId, index, 'count', value)}
																					value={ticket.count}
																					label="No. of tickets"
																					placeholder="eg: 100"
																					errorMessage={(errors.step_3_info?.venue_data?.[venueId]?.tickets?.[index] as Errors<NonNullable<VenueDataType['tickets']>[0]>)?.count}
																				/>
																			</div>
																			<div className="col-md-4">
																				<AppInputField
																					id={`ticket_amount_${venueId}_${index}`}
																					required={true}
																					type="number"
																					onChangeInput={(value: string) => updateTicket(venueId, index, 'amount', value)}
																					value={ticket.amount}
																					label="Ticket Unit Price (NPR)"
																					placeholder="eg: 3,000"
																					errorMessage={(errors.step_3_info?.venue_data?.[venueId]?.tickets?.[index] as Errors<NonNullable<VenueDataType['tickets']>[0]>)?.amount}
																				/>
																			</div>
																		</div>
																		<Button onClick={() => removeTicket(venueId, index)} danger disabled={(values.step_3_info.venue_data[venueId]?.tickets?.length || 0) <= 1}>
                                  Remove
																		</Button>
																		<hr />
																	</div>
																))}
																<Button onClick={() => addTicket(venueId)}>Add Ticket</Button>
															</div>
														)}
													</>
												)}

												<br />
												<br />
											</div>
										</div>
									</div>
								)
							}
						})}
					/>
				</div>
				: null}
		</div>
	)
}

export default AddEvent
