'use client'

import { AppDispatch, RootState } from "@/store";
import { fetchAllCategoriesAction } from "@/store/slices/category.slice";
import { fetchAllEventsAction } from "@/store/slices/event.slice";
import { PaginationMeta, ServerStatus } from "@/types/index.types";
import { CategoryModel } from "@/types/models/category.model";
import { EventModel } from "@/types/models/event.model";
import { Dropdown, message, Slider, Tag } from "antd";
import _, { debounce } from "lodash";
import { useSearchParams } from "next/navigation";
import { useState, useMemo, useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Category from "../common/category";
import { NoResult } from "../common/NoResult";
import EventGridView from "./EventGridView";
import InfiniteScroll from 'react-infinite-scroll-component';
import EventsListLoader from "./EventsListLoader.component";

export default function EventsPage() {
	const dispatch = useDispatch<AppDispatch>();
	const eventParams = useSearchParams();
	const { rows, status, meta } = useSelector((state: RootState) => state.event) as {
		rows: EventModel[];
		status: ServerStatus;
		meta: PaginationMeta | null;
	};
	const { rows: categories } = useSelector(
		(state: RootState) => state.category
	) as { rows: CategoryModel[] };

	const [isLoading, setIsLoading] = useState<boolean>(false);
	const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
	const [searchTerm, setSearchTerm] = useState<string>('');
	const [priceRange, setPriceRange] = useState<number | null>(null);
	const [selectedDate, setSelectedDate] = useState<string | null>(null);
	const [location, setLocation] = useState<{ lat: number, lng: number} | null>(null);

	const debouncedFetchEvents = useMemo(
		() =>
			debounce((
				searchTerm: string, categories: number[], priceRange: number | null, date: string | null, location: { lat: number; lng: number } | null
			) => {
				const filters = {
					page: 1,
					perPage: 8,
					...(categories.length > 0 && {
						category: categories.join(','),
					}),
					...(searchTerm && { search: searchTerm }),
					...(priceRange && { price: priceRange }),
					...(date && { date }),
					...(location && { latitude: location.lat, longitude: location.lng }),
				};
				dispatch(fetchAllEventsAction(filters));
			}, 500),
		[dispatch]
	);

	const handleSearchChange = useCallback((value: string) => {
		setSearchTerm(value);
		debouncedFetchEvents(value, selectedCategories, priceRange, selectedDate, location);
	}, [debouncedFetchEvents, selectedCategories, priceRange, selectedDate, location]);

	const handleCategoryChange = useCallback((categoryId: number) => {
		const newCategories = selectedCategories.includes(categoryId)
			? selectedCategories.filter(id => id !== categoryId)
			: [...selectedCategories, categoryId];
		setSelectedCategories(newCategories);
		debouncedFetchEvents(searchTerm, newCategories, priceRange, selectedDate, location);
	}, [debouncedFetchEvents, selectedCategories, searchTerm, priceRange, selectedDate, location]);

	const handlePriceRangeChange = useCallback((value: number | null) => {
		debouncedFetchEvents(searchTerm, selectedCategories, value, selectedDate, location);
	}, [debouncedFetchEvents, selectedCategories, searchTerm, selectedDate, location]);

	const handleDateChange = useCallback((value: string | null) => {
		setSelectedDate(value);
		debouncedFetchEvents(searchTerm, selectedCategories, priceRange, value, location);
	}, [debouncedFetchEvents, searchTerm, selectedCategories, priceRange, location]);

	const handleParamsChange = useCallback((categoryId: number) => {
		// Check if the selected categories contain only the passed category
		if (selectedCategories.length === 1 && selectedCategories[0] === categoryId) {
			// Do nothing if the selected category is the only one
			return;
		}

		setSelectedCategories([categoryId]);

		// Prepare the filters object
		const filters = {
			page: 1,
			perPage: 8,
			category: categoryId.toString(),
			...(searchTerm && { search: searchTerm }),
			...(priceRange && { price: priceRange }),
			...(selectedDate && { date: selectedDate }),
			...(location && { latitude: location.lat, longitude: location.lng }),
		};

		dispatch(fetchAllEventsAction(filters));
	}, [selectedCategories, searchTerm, priceRange, selectedDate, dispatch, location]);

	const fetchMoreEvents = () => {
		if (!meta?.nextPage) return;
		const filters = {
			page: meta?.nextPage || 1,
			perPage: 8,
			infiniteScroll: true,
			...(selectedCategories.length > 0 && {
				category: selectedCategories.join(','),
			}),
			...(searchTerm && { search: searchTerm }),
			...(priceRange && { price: priceRange }),
			...(selectedDate && { date: selectedDate }),
		};
		dispatch(fetchAllEventsAction(filters));
	}

	const getUserLocation = () => {
		if (navigator.geolocation) {
			setIsLoading(true);
			navigator.geolocation.getCurrentPosition(
				(position) => {
					const userLocation = {
						lat: position.coords.latitude,
						lng: position.coords.longitude,
					};
					setLocation(userLocation);
					setIsLoading(false);
					debouncedFetchEvents(searchTerm, selectedCategories, priceRange, selectedDate, userLocation);
				},
				(error) => {
					setIsLoading(false);
					console.error('Error getting location:', error);
					switch (error.code) {
					case error.PERMISSION_DENIED:
						message.error('Location access was denied. Please enable location permissions in your browser settings.');
						break;
					case error.POSITION_UNAVAILABLE:
						message.error('Location information is unavailable. This might be due to a GPS or network issue.');
						break;
					case error.TIMEOUT:
						message.error('The request to get your location timed out. Please try again.');
						break;
					default:
						message.error('An unknown error occurred while retrieving your location.');
						break;
					}
				},
				{ enableHighAccuracy: true }
			);
		} else {
			message.error('Geolocation is not supported by this browser.');
		}
	};

	useEffect(() => {
		const categoryId = eventParams.get('categoryId');
		if (categoryId) handleParamsChange(Number(categoryId));
		else {
			setSelectedCategories([]);
			dispatch(fetchAllEventsAction({ page: 1, perPage: 8 }));
		}
		dispatch(fetchAllCategoriesAction({ getMainCategories: true, type: 'active' }));
	}, [dispatch, eventParams]);

	return (
		<>
			<div className='featured-events-list section-container-box container' style={{ backgroundColor: 'white' }}>
				<div className='container-fluid-navigation'>
					<h1 className='app-title-primary'>
						Choose <span className='highlighted-heading'>Events</span>
					</h1>
					<p>
						Most happening events with tons of entertainment in your city by
						authorized organizers, surely you don't want to miss this!
					</p>
					<div className='all-events-container'>
						<div className='search-filter-container'>
							<div className='search-filter-row'>
								{/* Search Input */}
								<div className='search-box'>
									<i className='fa fa-search search-icon' />
									<input
										value={searchTerm}
										autoComplete='off'
										type='text'
										className='search-input'
										spellCheck='false'
										placeholder='Search events...'
										onChange={(e) => handleSearchChange(e.target.value)}
									/>
								</div>

								{/* Location Button */}
								<button
									className={`filter-btn ${location ? 'active' : ''}`}
									onClick={(e) => {
										e.preventDefault();
										getUserLocation();
									}}
									disabled={isLoading}
								>
									<i className={`fa ${isLoading ? 'fa-spinner fa-spin' : 'fa-compass'}`} />
									<span>{isLoading ? 'Locating...' : location ? 'Near You' : 'Near by'}</span>
								</button>

								{/* Date Dropdown */}
								<Dropdown
									menu={{
										items: [
											{ key: 'today', label: 'Today', onClick: () => handleDateChange('today') },
											{ key: 'tomorrow', label: 'Tomorrow', onClick: () => handleDateChange('tomorrow') },
											{ key: 'this-week', label: 'This Week', onClick: () => handleDateChange('this-week') },
											{ key: 'next-week', label: 'Next Week', onClick: () => handleDateChange('next-week') },
											{ key: 'clear', label: 'Clear Selection', onClick: () => handleDateChange(null) }
										]
									}}
									placement='bottomLeft'
									arrow
								>
									<button className={`filter-btn ${selectedDate ? 'active' : ''}`}>
										<i className='fa fa-calendar-alt' />
										<span>{selectedDate ? selectedDate.split('-').map(word => _.capitalize(word)).join(' ') : 'Date'}</span>
									</button>
								</Dropdown>

								{/* Price Range */}
								<div className='price-range-filter'>
									<Slider
										value={priceRange || 0}
										step={100}
										marks={{
											0: '0',
											12500: '12.5k',
											25000: '25k'
										}}
										max={25000}
										min={0}
										onChange={(value) => {
											if (value === 0) setPriceRange(null);
											else setPriceRange(value as number);
										}}
										onChangeComplete={(value) => {
											if (value === 0) handlePriceRangeChange(null);
											else handlePriceRangeChange(value as number);
										}}
									/>
								</div>
							</div>
						</div>
						<Category
							categories={categories}
							selectedCategories={selectedCategories}
							setSelectedCategories={setSelectedCategories}
							filterCategory={handleCategoryChange}
							onAllOptionClick={() => debouncedFetchEvents(searchTerm, [], priceRange, selectedDate, location)}
						/>
						<div className='mt-4 d-flex justify-content-center'>
							{location && (
								<Tag
									closable
									onClose={() => {
										setLocation(null);
										debouncedFetchEvents(searchTerm, selectedCategories, priceRange, selectedDate, null);
									}}
								>
									<strong>Nearby:</strong> Active
								</Tag>
							)}

							{searchTerm && (
								<Tag closable onClose={() => handleSearchChange('')}>
									<strong>Search:</strong> {searchTerm}
								</Tag>
							)}

							{priceRange && (
								<Tag closable onClose={() => {
									setPriceRange(null)
									handlePriceRangeChange(null)
								}}>
									<strong>Price:</strong> {priceRange}
								</Tag>
							)}

							{categories
								.filter((cat) => selectedCategories.includes(cat.id))
								.map((cat) => (
									<Tag
										key={cat.id}
										closable
										onClose={() => handleCategoryChange(cat.id)}
									>
										{cat.name}
									</Tag>
								))}

							{selectedDate && (
								<Tag closable onClose={() => handleDateChange('')}>
									<strong>Date:</strong> {_.capitalize(selectedDate)}
								</Tag>
							)}
						</div>
					</div>

					{rows.length > 0 ? (
						<InfiniteScroll
							dataLength={rows.length}
							hasMore={meta?.nextPage !== null}
							next={fetchMoreEvents}
							loader={<EventsListLoader rows={1} />}
							style={{ width: '100%', overflow: 'hidden' }}
						>
							<EventGridView events={rows} />
						</InfiniteScroll>
					) : status === 'fetching' ? (
						<EventsListLoader />
					) : (
						<NoResult />
					)}

				</div>
			</div>
		</>
	)
}
