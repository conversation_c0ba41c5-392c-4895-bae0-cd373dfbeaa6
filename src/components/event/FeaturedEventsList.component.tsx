'use client'

import { fetchFeaturedEventsAction } from "@/store/slices/event.slice";
import EventGridView from "./EventGridView";
import { AppDispatch, RootState } from "@/store";
import { EventModel } from "@/types/models/event.model";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { NoResult } from "../common/NoResult";
import Link from "next/link";
import EventsListLoader from "./EventsListLoader.component";

export default function FeaturedEventsList() {
	const dispatch = useDispatch<AppDispatch>();
	const { featuredEvents: events } = useSelector((state: RootState) => state.event) as {
		featuredEvents: {
			data: EventModel[];
			count: number;
		};
	};
	const [loading, setLoading] = useState<boolean>(false);

	useEffect(() => {
		const fetchFeaturedEvents = async () => {
			setLoading(true);
			await dispatch(fetchFeaturedEventsAction({ page: 1, perPage: 12 }));
			setLoading(false);
		}
		fetchFeaturedEvents();
	}, [dispatch]);

	return (
		<div className='featured-events-list section-container-box'>
			<div className='container-fluid-navigation'>
				<h1 className='app-title-primary'>
          Featured <span className='highlighted-heading'>Events</span>
				</h1>

				<p>
					Most happening events with tons of entertainment in your city by
					authorized organizers, surely you don't want to miss this!{' '}
				</p>

				<div className='event-lists-grid'>
					<div className="row">
						{loading ? (
							// <div
							// 	className='d-flex justify-content-center align-items-center fs-4'
							// 	style={{ height: '20vh' }}
							// >
							// 	Loading Events......
							// 	<LoadingOutlined />
							// </div>
							<EventsListLoader rows={1} />
						) : events.data.length > 0 ? (
							<EventGridView events={events.data} />
						) : (
							<NoResult message='No Featured Events Available' />
						)}
					</div>
				</div>
			</div>

			{events.data.length > 0 && (
				<div className='view-more-events'>
					<Link href={'/events'}>
						<button
							className='ripple-eff btn btn-primary btn-lg'
							disabled={loading}
						>
							<i className='fa fa-mouse-pointer' /> &nbsp;&nbsp; Load More ...
						</button>
					</Link>
				</div>
			)}
			<br />
		</div>
	)
}
