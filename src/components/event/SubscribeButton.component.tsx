'use client'

import { AppDispatch } from "@/store";
import { appStateChange } from "@/store/slices/app.slice";
import { subscribeOrUnsubscribeEventAction } from "@/store/slices/user.slice";
import { EventModel } from "@/types/models/event.model";
import { isAuthenticatedUser } from "@/utils/cookie.util";
import { useState } from "react";
import { useDispatch } from "react-redux";

export default function SubscribeButton({ event }: { event: EventModel }) {
	const dispatch = useDispatch<AppDispatch>();
	const isLoggedIn = isAuthenticatedUser();
	const [isSubscribed, setIsSubscribed] = useState<boolean>(event.is_subscribed || false);
	const [loading, setLoading] = useState<boolean>(false);

	const handleSubscribeToggle = async () => {
		if (loading) return;
		if (!isLoggedIn) {
			dispatch(appStateChange({ key: "showLoginModal", value: true }));
			return;
		}
		setLoading(true);
		const res = await dispatch(subscribeOrUnsubscribeEventAction(event.id, !isSubscribed));
		if (res && res.status === 200) {
			setIsSubscribed(!isSubscribed);
		}
		setLoading(false);
	};

	return (
		<button
			onClick={handleSubscribeToggle}
			className={`btn btn-light ${isSubscribed ? "active" : ""}`}
			disabled={loading}
		>
			{loading ? <i className="fas fa-spinner fa-spin" /> : isSubscribed ? "Unsubscribe" : "Subscribe this event"}
		</button>
	);
}
