import { Skeleton } from "antd";

export default function EventsListLoader({ rows = 2 }: { rows?: number }) {
	return (
		<div style={{ display: 'flex', flexDirection: 'column', gap: '25px', marginTop: '20px' }}>
			{[...Array(rows)].map((_, rowIndex) => (
				<div key={`row-${rowIndex}`} style={{ display: 'flex', justifyContent: 'space-between' }}>
					{[...Array(4)].map((_, colIndex) => (
						<div key={`item-${rowIndex}-${colIndex}`} style={{ backgroundColor: '#FFFFFF', padding: '3px' }}>
							<Skeleton.Image active style={{ height: 200, width: 320 }} />
							<Skeleton
								paragraph={{
									rows: 2,
								}}
								style={{ marginTop: 5 }}
								active
							/>
						</div>
					))}
				</div>
			))}
		</div>
	);
}
