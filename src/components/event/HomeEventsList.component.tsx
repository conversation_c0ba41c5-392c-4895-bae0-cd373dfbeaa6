'use client'

import { AppDispatch, RootState } from "@/store";
import { fetchAllCategoriesAction } from "@/store/slices/category.slice";
import { eventStateChange, fetchAllEventsAction } from "@/store/slices/event.slice";
import { ServerStatus } from "@/types/index.types";
import { CategoryModel } from "@/types/models/category.model";
import { EventModel } from "@/types/models/event.model";
import { Spin } from "antd";
import dayjs from "dayjs";
import { debounce } from "lodash";
import Link from "next/link";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import ExpandableContent from "../common/ExpandableContent.component";
import { NoResult } from "../common/NoResult";
import { getFullImageUrl } from "@/utils/general.util";

export default function HomeEventsList() {
	const dispatch = useDispatch<AppDispatch>();
	const { rows, status } = useSelector((state: RootState) => state.event) as {
		rows: EventModel[];
		status: ServerStatus;
	};
	const { rows: categories } = useSelector(
		(state: RootState) => state.category
	) as { rows: CategoryModel[] };

	const [selectedDate, setSelectedDate] = useState<string | null>('today');
	const [selectedCategories, setSelectedCategories] = useState<number[]>([]);

	const debouncedFetchEvents = useMemo(
		() =>
			debounce((categories: number[], date: string | null) => {
				const filters = {
					...(categories.length > 0 && {
						category: categories.join(','),
					}),
					...(date && { date }),
				};
				dispatch(fetchAllEventsAction(filters));
			}, 500),
		[dispatch]
	);

	const handleCategoryChange = useCallback((categoryId: number) => {
		dispatch(eventStateChange({ key: 'status', value: 'fetching' }));
		const newCategories = selectedCategories.includes(categoryId)
			? selectedCategories.filter(id => id !== categoryId)
			: [...selectedCategories, categoryId];
		setSelectedCategories(newCategories);
		debouncedFetchEvents(newCategories, selectedDate);
	}, [dispatch, debouncedFetchEvents, selectedCategories, selectedDate]);

	const handleDateChange = useCallback((value: string | null) => {
		dispatch(eventStateChange({ key: 'status', value: 'fetching' }));
		setSelectedDate(value);
		debouncedFetchEvents(selectedCategories, value);
	}, [dispatch, debouncedFetchEvents, selectedCategories]);

	useEffect(() => {
		dispatch(fetchAllCategoriesAction({ getMainCategories: true, type: 'active' }));
		dispatch(fetchAllEventsAction({ date: 'today' }));
	}, [dispatch]);

	const getDateRange = () => {
		switch (selectedDate) {
		case 'today':
			return `For Today: ${dayjs().format('dddd - MMM DD, YYYY')}`;
		case 'tomorrow':
			return `For Tomorrow: ${dayjs().add(1, 'day').format('dddd - MMM DD, YYYY')}`;
		case 'this-week':
			return `From ${dayjs().format('dddd - MMM DD')} to ${dayjs().endOf('week').format('dddd - MMM DD, YYYY')}`;
		case 'next-week':
			return `From ${dayjs().endOf('week').add(1, 'day').format('dddd - MMM DD')} to ${dayjs().endOf('week').add(1, 'day').endOf('week').format('dddd - MMM DD, YYYY')}`;
		default:
			return '';
		}
	};

	return (
		<>
			<div className="filter-event-section">
				<div className="container-fluid-navigation">
					<div className="filter-content">
						<ul className="tab-switcher">
							<li>
								<a href=""
									className={`today ${selectedDate === 'today' ? 'active' : ''}`}
									onClick={(e) => {
										e.preventDefault()
										handleDateChange('today')
									}}
								>
                  Today
									<span className="this-date">{dayjs().format('MMM DD, YYYY')}</span>
								</a>
							</li>
							<li>
								<a href=""
									className={`tomorrow ${selectedDate === 'tomorrow' ? 'active' : ''}`}
									onClick={(e) => {
										e.preventDefault()
										handleDateChange('tomorrow')
									}}
								>
                  Tomorrow
									<span className="this-date">{dayjs().add(1, 'day').format('MMM DD, YYYY')}</span>
								</a>
							</li>
							<li>
								<a href=""
									className={`this_week ${selectedDate === 'this-week' ? 'active' : ''}`}
									onClick={(e) => {
										e.preventDefault()
										handleDateChange('this-week')
									}}
								>
                  This Week
									<span className="this-date">{dayjs().format('MMM DD')} - {dayjs().endOf('week').format('MMM DD, YYYY')}</span>
								</a>
							</li>
							<li>
								<a href=""
									className={`this_weekend ${selectedDate === 'next-week' ? 'active' : ''}`}
									onClick={(e) => {
										e.preventDefault()
										handleDateChange('next-week')
									}}
								>
                  Next Week
									<span className="this-date">{dayjs().endOf('week').add(1, 'day').format('MMM DD')} - {dayjs().endOf('week').add(1, 'day').endOf('week').format('MMM DD, YYYY')}</span>
								</a>
							</li>
						</ul>
						<div className="filter-by-category">
							<div className="filtered-reason">
								<h3>Events below are listed for,</h3>
								<h1>{getDateRange()}</h1>
							</div>
							<ul>
								<li>
									<a href=""
										className={selectedCategories.length === 0 ? 'active' : ''}
										onClick={(e) => {
											e.preventDefault()
											setSelectedCategories([])
											debouncedFetchEvents([], selectedDate);
										}}
									>
										All
									</a>
								</li>
								{categories.map((category, index) => (
									<li key={index}>
										<a href="" className={selectedCategories.includes(category.id) ? 'active' : ''}
											onClick={(e) => {
												e.preventDefault()
												handleCategoryChange(category.id)
											}}
										>
											{category.name}
										</a>
									</li>
								))}
								<div className="clearfix"></div>
							</ul>
							<div className="filtered-event-lists">
								<div className="container">
									<Spin size="large" spinning={status === 'fetching'} delay={1}>
										{rows.length > 0 ? (
											rows?.map((event) => (
												<div key={event?.id} className="filtered-item">
													<div className="row">
														<div className="col-sm-3 filter-evt-info-img">
															<div className="event-image">
																<img src={event?.image ? getFullImageUrl(event.image) : "https://placehold.co/200x200/png?text=Event+Image"} alt={event?.title || ""} />
															</div>
														</div>
														<div className="col-sm-7 filter-evt-info">
															<div className="event-details">
																<div className="row">
																	<div className="col-9">
																		<p className="time-location">
																			{dayjs(event.start_date_time).format('D MMM YYYY h:mm A')} onwards
																		</p>
																		<h2>{event?.title || ""}</h2>
																	</div>
																	<div className="col-3">
																		<Link
																			href={`/events/${event.slug}-${event.id}`}
																			className="buy-ticket-button"
																			title="Buy Ticket"
																			// onClick={() => {
																			// 	dispatch(eventStateChange({ key: 'record', value: event }))
																			// }}
																		>
																			<i className="fas fa-ticket" />
																		</Link>
																	</div>
																</div>
																<hr />
																{event?.about && <div className="event-about">
																	<ExpandableContent content={event.about} initialLimit={150} />
																</div>}
																<h3 className="event-cost">
																	<span>
																		{event.tickets && event.tickets.length > 0
																			? event.tickets.some(ticket => ticket.ticket_type === 'free')
																				? 'Free Event'
																				: `NPR ${Math.min(...event.tickets.map(ticket => ticket.price ?? Infinity))}`
																			: 'Tickets N/A'}
																	</span>
																</h3>
																{/* <div className="social-share">
																	<a className="facebook" href="">
																		<i className="fa-brands fa-facebook-f"></i>
																	</a>
																	<a className="twitter" href="">
																		<i className="fa-brands fa-x-twitter"></i>
																	</a>
																</div> */}
															</div>
														</div>
													</div>
												</div>
											))
										) : (
											<NoResult message="No events available."/>
										)}
									</Spin>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="clearfix" />
			</div>
		</>
	)
}
