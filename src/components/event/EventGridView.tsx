'use client'

import { EventModel } from "@/types/models/event.model";
import Link from 'next/link';
import dayjs from 'dayjs';
import { Tag } from "antd";
import { getEventPrice, getFullImageUrl } from "@/utils/general.util";

const EventGridView = ({ events }: { events: EventModel[]; column?: number }) => {
	return (
		<div className="event-lists-grid">
			<div className="events-grid-container">
				{events.map((event) => (
					<div key={event.id} className="event-card">
						<div className="event-image">
							<img src={event?.image ? getFullImageUrl(event.image) : "https://placehold.co/130x90/png?text=No+Image"} alt={event.title || ""}/>
							<div className="event-overlay">
								<Link className="view-more-btn" href={`/events/${event.slug}-${event.id}`}>
									View Details
								</Link>
							</div>
							{getEventPrice(event) && (
								<div className="event-price">
									{getEventPrice(event)}
								</div>
							)}
						</div>
						<div className="event-content">
							<h3 className="event-title">
								<Link href={`/events/${event.slug}-${event.id}`}>
									{event.title}
								</Link>
								{event.is_delayed && <Tag color="orange">Delayed</Tag>}
							</h3>
							<div className="event-details">
								<p className="event-date">
									<i className="far fa-calendar-alt"></i> {dayjs(event.start_date_time).format('D MMM YYYY h:mm A')}
								</p>
								<p className="event-location">
									<i className="fas fa-map-marker-alt"></i> {event.venues && event.venues.length > 0 ? event.venues[0].name : 'Venue TBA'}
								</p>
							</div>
						</div>
					</div>
				))}
			</div>
		</div>
	);
};

export default EventGridView;
