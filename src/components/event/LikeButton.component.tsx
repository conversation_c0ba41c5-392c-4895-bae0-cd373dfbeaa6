'use client'

import { AppDispatch } from "@/store";
import { appStateChange } from "@/store/slices/app.slice";
import { likeOrUnlikeEventAction } from "@/store/slices/event.slice";
import { EventModel } from "@/types/models/event.model";
import { isAuthenticatedUser } from "@/utils/cookie.util";
import pluralize from "pluralize";
import { useState } from "react";
import { useDispatch } from "react-redux";

export default function LikeButton({ event }: { event: EventModel }) {
	const dispatch = useDispatch<AppDispatch>();
	const isLoggedIn = isAuthenticatedUser();
	const [isLiked, setIsLiked] = useState<boolean>(event.is_liked || false);
	const [likeCount, setLikeCount] = useState<number>(Number(event.likes_count) || 0);
	const [loading, setLoading] = useState<boolean>(false);

	const handleLikeToggle = async (e: React.MouseEvent<HTMLAnchorElement>) => {
		e.preventDefault();
		if (loading) return;
		if (!isLoggedIn) {
			dispatch(appStateChange({ key: "showLoginModal", value: true }));
			return;
		}
		setLoading(true);
		const res = await dispatch(likeOrUnlikeEventAction(event.id, !isLiked));
		if (res && res.status === 200) {
			setIsLiked(!isLiked);
			setLikeCount(isLiked ? likeCount - 1 : likeCount + 1);
		}
		setLoading(false);
	};

	return (
		<>
			<a href="#" onClick={handleLikeToggle} className="like-button">
				{loading ? (
					<i className="fas fa-spinner fa-spin" />
				) : (
					<i className={isLiked ? "fas fa-heart" : "far fa-heart"} />
				)}
			</a>
			<p>
				<i>{likeCount}</i> {pluralize("like", likeCount)}
			</p>
		</>
	);
}
