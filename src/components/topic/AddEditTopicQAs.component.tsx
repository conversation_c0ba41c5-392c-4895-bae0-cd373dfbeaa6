'use client';

import { <PERSON><PERSON>, Modal } from 'antd';
import FullScreenModal from '../FullScreenModal.component'
import { Formik } from 'formik';
import { AppDispatch, RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON>rro<PERSON>, ServerStatus } from '@/types/index.types';
import { AppInputField, AppInputSelectField, AppInputTextField } from '../common/AppInputField.component';
import { useEffect, useState } from 'react';
import { TopicModel } from '@/types/models/topic.model';
import { AddTopicQaType } from '@/types/forms/add-topic.type';
import { createTopicQaAction, topicStateChange, updateTopicQaAction } from '@/store/slices/topic.slice';
import { TopicQasModel } from '@/types/models/topic-qas.model';
import { fetchGeneralInfoAction } from '@/store/slices/general-info.slice';
import { GeneralInfoModal } from '@/types/models/general-info.model';

const AddEditTopicQAs = ({ showModal, setShowModal } : {showModal: boolean, setShowModal: React.Dispatch<React.SetStateAction<boolean>>}) => {
	const dispatch = useDispatch<AppDispatch>();

	const [showCloseModal, setShowCloseModal] = useState<boolean>(false);

	const { rows, topicQaRecord: record, status } = useSelector((state: RootState) => state.topic) as { rows: TopicModel[], topicQaRecord: TopicQasModel | null, status: ServerStatus };
	const { record: generalInfo } = useSelector((state: RootState) => state.generalInfo) as { record: GeneralInfoModal };

	useEffect(() => {
		if (showModal) {
			dispatch(fetchGeneralInfoAction());
		}
	}, [dispatch, showModal])

	return (
		<Formik<AddTopicQaType>
			key={record?.id}
			initialValues={{
				topic_id: record?.topic_id || null,
				question: record?.question || '',
				answer: record?.answer || '',
				order: record?.order || null,
			}}
			validate={(data) => {
				const errors: Errors<AddTopicQaType> = {};
				if (!data.topic_id) {
					errors.topic_id = 'Topic is required';
				}
				if (!data.question) {
					errors.question = 'Question is required';
				}
				if (!data.answer) {
					errors.answer = 'Answer is required';
				} else if (data.answer.length < 30) {
					errors.answer = 'Answer must be at least 30 characters';
				} else if (data.answer.length > 500) {
					errors.answer = 'Answer must be less than 500 characters';
				}
				return errors;
			}}
			onSubmit={async (values, actions) => {
				const res = record ?
					await dispatch(updateTopicQaAction<AddTopicQaType>(record.id, values))
					: await dispatch(createTopicQaAction<AddTopicQaType>(values));
				if (res) {
					if (res.status === 201 || (record && res.status === 200)) {
						actions.resetForm();
						setShowModal(false)
					}
					if (record) {
						dispatch(topicStateChange({ key: 'topicQaRecord', value: null }));
					}
				}
			}}
		>
			{({ values, errors, handleSubmit, setFieldValue, resetForm, isValid, isValidating, dirty }) => (
				<>
					<FullScreenModal
						className={'dashboard_add_form'}
						onClose={() => {
							if (dirty) setShowCloseModal(true);
							else {
								resetForm();
								setShowModal(false);
								if (record) {
									dispatch(topicStateChange({ key: 'topicQaRecord', value: null }));
								}
							}
						}}
						show={showModal}
					>
						<div className="modal-body-container-form">
							<div className="add_form_header">
								<div className="container">
									<div className="d-flex justify-content-between align-items-center">
										<div>
											<h1>Add Topic QA</h1>
										</div>
									</div>
								</div>
							</div>

							<form>
								<div className="add_form_container">
									<div className="container">
										<div className="row">

											<div className="col-md-6">
												<AppInputSelectField
													id="topic_id"
													required={true}
													value={values.topic_id}
													onChangeInput={(value: number) => {
														setFieldValue('topic_id', value);
													}}
													label={'Select Topic'}
													info={'Select the topic of the QA'}
													options={rows.map((item) => ({
														label: item.title,
														value: item.id,
													}))}
													errorMessage={errors.topic_id}
												/>
											</div>

											<div className="col-md-6">
												<AppInputField
													id="order"
													maxLength={2}
													type={'number'}
													value={values.order}
													onChangeInput={(value: string) => {
														const sanitized = value.replace(/^0+/, '');
														const parsed = sanitized ? parseInt(sanitized) : null;
														setFieldValue('order', parsed);
													}}
													label={'Order'}
													info={'Order of the question (optional)'}
													placeholder={'1'}
												/>
											</div>

											<div className="col-md-12">
												<AppInputField
													id="question"
													required={true}
													value={values.question}
													onChangeInput={(value: string) => {
														setFieldValue('question', value);
													}}
													label={'Question'}
													info={'Question of the topic QA (eg: How do I reset my password?)'}
													placeholder={'Example Question'}
													errorMessage={errors.question}
												/>
											</div>

											<div className="col-md-12">
												<AppInputTextField
													id="answer"
													required={true}
													value={values.answer}
													onChangeInput={(value: string) => {
														setFieldValue('answer', value);
													}}
													label={'Answer'}
													placeholder={'Answer to the question ....'}
													rows={6}
													info={'Answer to the question (minimum 30 characters / max 500 characters)'}
													errorMessage={errors.answer}
													maxLength={500}
												/>
											</div>

										</div>
									</div>
								</div>
							</form>

							<div className="add_form_footer">
								<div className="container-fluid">
									<div className="row">
										<div className="col-md-12">
											<div className="d-flex justify-content-between">
												<p style={{ color: '#999' }}>
													<small>Need assistance? Contact us at <a
														href={"mailto:" + (generalInfo?.email_one || '<EMAIL>')}
														target="_blank"
														rel="noopener noreferrer">{generalInfo?.email_one || '<EMAIL>'}</a>
													</small>
												</p>
												<div>
													<a href=""
														onClick={(e) => {
															e.preventDefault();
															if (dirty) setShowCloseModal(true);
															else {
																resetForm();
																setShowModal(false);
																if (record) {
																	window.history.replaceState(
																		null,
																		'',
																		'/user/dashboard/topics'
																	);
																	dispatch(topicStateChange({ key: 'topicQaRecord', value: null }));
																}
															}
														}}
														className='me-3'
														style={{ pointerEvents: status === 'saving' ? 'none' : 'auto', opacity: status === 'saving' ? 0.5 : 1 }}
													>
														Cancel
													</a>
													<Button
														loading={status === 'saving'}
														size={'large'}
														type={'primary'}
														disabled={!isValid || status === 'saving' || isValidating || !dirty}
														onClick={() => handleSubmit()}
													>
														{record ? 'Update' : 'Save'} QA
													</Button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</FullScreenModal>
					<Modal
						title={'Are you sure?'}
						open={showCloseModal}
						onOk={() => {
							resetForm();
							setShowCloseModal(false);
							setShowModal(false);
							if (record) {
								window.history.replaceState(
									null,
									'',
									'/user/dashboard/topics'
								);
								dispatch(topicStateChange({ key: 'topicQaRecord', value: null }));
							}
						}}
						onCancel={() => setShowCloseModal(false)}
						okText={'Quit'}
						closable={false}
					>
						<div>
							Are you sure you want to quit? Your data has not been saved.
						</div>
					</Modal>
				</>
			)}
		</Formik>
	)
}

export default AddEditTopicQAs
