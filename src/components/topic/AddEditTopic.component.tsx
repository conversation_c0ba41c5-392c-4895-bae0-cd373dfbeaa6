'use client';

import { <PERSON><PERSON>, Modal } from 'antd';
import FullScreenModal from '../FullScreenModal.component'
import { Formik } from 'formik';
import { AppDispatch, RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { Errors, ServerStatus } from '@/types/index.types';
import { AppInputField } from '../common/AppInputField.component';
import { useEffect, useState } from 'react';
import { TopicModel } from '@/types/models/topic.model';
import { AddTopicType } from '@/types/forms/add-topic.type';
import { createTopicAction, topicStateChange, updateTopicAction } from '@/store/slices/topic.slice';
import { fetchGeneralInfoAction } from '@/store/slices/general-info.slice';
import { GeneralInfoModal } from '@/types/models/general-info.model';

const AddEditTopic = ({ showModal, setShowModal } : {showModal: boolean, setShowModal: React.Dispatch<React.SetStateAction<boolean>>}) => {
	const dispatch = useDispatch<AppDispatch>();

	const [showCloseModal, setShowCloseModal] = useState<boolean>(false);

	const { topicRecord: record, status } = useSelector((state: RootState) => state.topic) as { topicRecord: TopicModel | null, status: ServerStatus };
	const { record: generalInfo } = useSelector((state: RootState) => state.generalInfo) as { record: GeneralInfoModal };

	useEffect(() => {
		if (showModal) {
			dispatch(fetchGeneralInfoAction());
		}
	}, [dispatch, showModal])

	return (
		<Formik<AddTopicType>
			key={record?.id}
			initialValues={{
				title: record?.title || '',
				order: record?.order || null,
			}}
			validate={(data) => {
				const errors: Errors<AddTopicType> = {};
				if (!data.title) {
					errors.title = 'Title is required';
				}
				return errors;
			}}
			onSubmit={async (values, actions) => {
				const res = record ?
					await dispatch(updateTopicAction<AddTopicType>(record.id, values))
					: await dispatch(createTopicAction<AddTopicType>(values));
				if (res) {
					if (res.status === 201 || (record && res.status === 200)) {
						actions.resetForm();
						setShowModal(false)
					}
					if (record) {
						window.history.replaceState(
							null,
							'',
							'/user/dashboard/topics'
						);
						dispatch(topicStateChange({ key: 'topicRecord', value: null }));
					}
				}
			}}
		>
			{({ values, errors, handleSubmit, setFieldValue, resetForm, isValid, isValidating, dirty }) => (
				<>
					<FullScreenModal
						className={'dashboard_add_form'}
						onClose={() => {
							if (dirty) setShowCloseModal(true);
							else {
								resetForm();
								setShowModal(false);
								if (record) {
									window.history.replaceState(
										null,
										'',
										'/user/dashboard/topics'
									);
									dispatch(topicStateChange({ key: 'topicRecord', value: null }));
								}
							}
						}}
						show={showModal}
					>
						<div className="modal-body-container-form">
							<div className="add_form_header">
								<div className="container">
									<div className="d-flex justify-content-between align-items-center">
										<div>
											<h1>Add Topic</h1>
										</div>
									</div>
								</div>
							</div>

							<form>
								<div className="add_form_container">
									<div className="container">
										<div className="row">

											<div className="col-md-12">
												<AppInputField
													id="topic_title"
													required={true}
													value={values.title}
													onChangeInput={(value: string) => {
														setFieldValue('title', value);
													}}
													label={'Topic Title'}
													info={'Title of the topic (eg: Password Recovery, Change Email, etc.)'}
													placeholder={'Example Topic'}
													errorMessage={errors.title}
												/>
											</div>

											<div className="col-md-6">
												<AppInputField
													id="topic_order"
													maxLength={2}
													type={'number'}
													value={values.order}
													onChangeInput={(value: string) => {
														const sanitized = value.replace(/^0+/, '');
														const parsed = sanitized ? parseInt(sanitized) : null;
														setFieldValue('order', parsed);
													}}
													label={'Order'}
													info={'Order of the topic (optional)'}
													placeholder={'1'}
												/>
											</div>
										</div>
									</div>
								</div>
							</form>

							<div className="add_form_footer">
								<div className="container-fluid">
									<div className="row">
										<div className="col-md-12">
											<div className="d-flex justify-content-between">
												<p style={{ color: '#999' }}>
													<small>Need assistance? Contact us at <a
														href={"mailto:" + (generalInfo?.email_one || '<EMAIL>')}
														target="_blank"
														rel="noopener noreferrer">{generalInfo?.email_one || '<EMAIL>'}</a>
													</small>
												</p>
												<div>
													<a href=""
														onClick={(e) => {
															e.preventDefault();
															if (dirty) setShowCloseModal(true);
															else {
																resetForm();
																setShowModal(false);
																if (record) {
																	window.history.replaceState(
																		null,
																		'',
																		'/user/dashboard/topics'
																	);
																	dispatch(topicStateChange({ key: 'topicRecord', value: null }));
																}
															}
														}}
														className='me-3'
														style={{ pointerEvents: status === 'saving' ? 'none' : 'auto', opacity: status === 'saving' ? 0.5 : 1 }}
													>
														Cancel
													</a>
													<Button
														loading={status === 'saving'}
														size={'large'}
														type={'primary'}
														disabled={!isValid || status === 'saving' || isValidating || !dirty}
														onClick={() => handleSubmit()}
													>
														{record ? 'Update' : 'Save'} Topic
													</Button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</FullScreenModal>
					<Modal
						title={'Are you sure?'}
						open={showCloseModal}
						onOk={() => {
							resetForm();
							setShowCloseModal(false);
							setShowModal(false);
							if (record) {
								window.history.replaceState(
									null,
									'',
									'/user/dashboard/topics'
								);
								dispatch(topicStateChange({ key: 'topicRecord', value: null }));
							}
						}}
						onCancel={() => setShowCloseModal(false)}
						okText={'Quit'}
						closable={false}
					>
						<div>
							Are you sure you want to quit? Your data has not been saved.
						</div>
					</Modal>
				</>
			)}
		</Formik>
	)
}

export default AddEditTopic
