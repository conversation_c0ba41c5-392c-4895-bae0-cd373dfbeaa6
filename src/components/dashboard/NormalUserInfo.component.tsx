'use client'

import { updateProfileImageAction } from "@/store/slices/user.slice";
import { UserModel } from "@/types/models/user.model";
import { CameraOutlined, DashOutlined } from "@ant-design/icons";
import { Spin } from "antd";
import dayjs from "dayjs";
import ImageCropper from "../ImageCropper.component";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { ServerStatus } from "@/types/index.types";
import { getFullImageUrl } from "@/utils/general.util";

export function NormalUserInfo({ user }: { user: UserModel }) {
	const dispatch = useDispatch<AppDispatch>();
	const { status } = useSelector((state: RootState) => state.user) as { status: ServerStatus };
	const [profileImage, setProfileImage] = useState<string>(user.image || '');

	return (
		<div className="col-sm-3 user-profile-info">
			<div className="profile-picture">
				{/* <Progress showInfo={false} type="circle"
					percent={30}/> */}

				<ImageCropper
					value={profileImage as string}
					aspectRatio={1}
					isCircular={true}
					renderUploadTrigger={(openUpload) => (
						status === 'waiting' ? <Spin size="large" /> :
							<>
								<img
									src={profileImage ? getFullImageUrl(profileImage) : '/images/user_avatar.jpg'}
									alt="Profile Image"/>
								<div onClick={openUpload} className="upload-btn-wrapper">
									<CameraOutlined  className={'upload-icon'}/>
								</div>
							</>
					)}
					onCropComplete={async (croppedImageUrl) => {
						const res = await dispatch(updateProfileImageAction(croppedImageUrl.split('/').at(-1) as string));
						if (res && res.status === 200) setProfileImage(croppedImageUrl.split('/').at(-1) as string);
					}}
				/>
			</div>
			<div>
				<h3>{user.f_name ? `${user.f_name} ${user.l_name || ''}` : <DashOutlined />}</h3>
				<p>
					{user.email ? (
						<>
							{user.email} {user.email_verified_at && <i className="fa fa-check-circle" />}
						</>
					) : <DashOutlined />}
				</p>
			</div>

			{/* <div className="padding-left-10">
				<div className="profile-completion">
					{<p>profile information update are still pending.</p>}

					<div className="profile-completion-content">
						<Tooltip title="1 Update Info / 2 Upload Profile / 3 Verify">
							<Progress percent={20} status="active"/>
						</Tooltip>
					</div>
				</div>
			</div> */}

			<div className="profile-footer">
				<div>
					<p className="mt-3 mx-2">
						<strong>Last Login:</strong> {!user?.last_logged_in_at ? '-' : (
							<>
								{dayjs(user.last_logged_in_at).format('DD MMM YYYY, hh:mm A')}
							</>
						)}
					</p>
				</div>
				{/* <div>
				</div> */}
			</div>
		</div>
	)
}
