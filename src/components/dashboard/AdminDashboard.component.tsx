'use client'

import { AppDispatch, RootState } from "@/store";
import { approveEventAction, cancelEventAction, eventStateChange, fetchAllProtectedEventsAction, fetchEventAction, fetchFeaturedEventsAction, fetchSliderEventsAction, rejectEventAction, setEventAsFeaturedAction, setEventAsSliderAction, toggleEventStatusAction } from "@/store/slices/event.slice";
import { PaginationMeta, ServerStatus } from "@/types/index.types";
import { EventModel } from "@/types/models/event.model";
import { VenueModel } from "@/types/models/venue.model";
import { AccountBookFilled, CheckCircleFilled, CloseCircleFilled, EditFilled, EyeFilled, FileImageOutlined, MoreOutlined, SplitCellsOutlined, StarFilled, StopFilled } from "@ant-design/icons";
import { TableProps, Tag, Dropdown, Table, Pagination, DatePicker, Modal, Button, Badge, Tabs } from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import TableSkeleton from "../common/TableSkeleton";
import { triggerConfirmation } from "@/store/slices/app.slice";
import relativeTime from 'dayjs/plugin/relativeTime';
import AddEvent from "../event/AddEvent.component";
import { useRouter } from "next-nprogress-bar";
import { getFullImageUrl } from "@/utils/general.util";

dayjs.extend(relativeTime);

export function AdminDashboard({ params }:  { params: { slug: string[] } }) {
	const eventId = params.slug ? params.slug.pop() : null;

	const dispatch = useDispatch<AppDispatch>();
	const router = useRouter();

	const {
		rows,
		featuredEvents,
		sliderEvents,
		meta,
		status
	} = useSelector((state: RootState) => state.event) as {
		rows: EventModel[],
		featuredEvents: { data: EventModel[], count: number },
		sliderEvents: { data: EventModel[], count: number },
		meta: PaginationMeta | null,
		status: ServerStatus
	};

	const [showEventDateModal, setShowEventDateModal] = useState<boolean>(false);
	const [operation, setOperation] = useState<'slider' | 'featured' | null>(null);
	const [event, setEvent] = useState<EventModel | null>(null);
	const [fromDate, setFromDate] = useState<string | null>(null);
	const [toDate, setToDate] = useState<string | null>(null);
	const [activeTab, setActiveTab] = useState<string>('published');
	const [showModal, setShowModal] = useState<boolean>(false);

	const columns: TableProps<EventModel>['columns'] = [
		{
			title: '#',
			key: 'index',
			width: '1%',
			align: 'center',
			render: (_text, _record, index) => index + 1,
		},
		{
			title: 'Name',
			dataIndex: 'title',
			key: 'title',
			width: '25%',
			render: (text: string, record: EventModel) => (
				<div className="event-item">
					<div className="event-item__image-container">
						{record.image ? (
							<img
								src={getFullImageUrl(record.image)}
								alt={text || 'Event'}
								className="event-item__image"
							/>
						) : (
							<div className="event-item__placeholder">
								<FileImageOutlined size={20} />
							</div>
						)}
					</div>
					<div className="event-item__content">
						<span className="event-item__title">
							{text || '-'}
						</span>
						{record.categories && record.categories.length > 0 && (
							<div className="event-item__categories">
								{record.categories.map((category, index) => (
									<Tag key={index} color="pink" className="event-item__category-tag">{category.name}</Tag>
								))}
							</div>
						)}
					</div>
				</div>
			)
		},
		{
			title: 'Status',
			dataIndex: 'published_at',
			key: 'status',
			width: '15%',
			align: 'left',
			render: (_, record: EventModel) => (
				<>
					{record.published_at && (
						<Tag color="green">Published</Tag>
					)}
					{record.approved_at && (
						<Tag color="blue">Approved</Tag>
					)}
					{record.is_delayed && (
						<Tag color="orange">Delayed</Tag>
					)}
					{record.status ? (
						<>
							<Tag color="green">Active</Tag>
							{!record.published_at && !record.approved_at && (
								<Tag color="blue">On Review</Tag>
							)}
						</>
					) : (
						<>
							<Tag color="red">Inactive</Tag>
							{!record.published_at && !record.approved_at && (
								<Tag color="red">Draft</Tag>
							)}
						</>
					)}
					{record.feature_from && record.feature_expiry && (
						<Tag color="purple">Featured</Tag>
					)}
					{record.sliders && record.sliders.length > 0 && (
						<Tag color="orange">Slider</Tag>
					)}
				</>
			)
		},
		{
			title: 'Location',
			dataIndex: 'venues',
			key: 'location',
			width: '15%',
			align: 'left',
			render: (text: VenueModel[]) => (
				<div>
					{text.map((venue, index) => (
						<Tag key={index} color="blue">{venue.name}</Tag>
					))}
				</div>
			)
		},
		{
			title: 'Created At',
			dataIndex: 'created_at',
			key: 'created_at',
			width: '10%',
			align: 'left',
			render: (text: string) => (
				<div>
					<p>{dayjs(text).format('Do MMM YYYY')}</p>
					<span style={{ fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)' }}>
						{dayjs(text).fromNow()}
					</span>
				</div>
			)
		},
		{
			title: 'Published At',
			dataIndex: 'published_at',
			key: 'published_at',
			width: '10%',
			align: 'left',
			render: (text: string) => (
				text ? <div>
					<p>{dayjs(text).format('Do MMM YYYY')}</p>
					<span style={{ fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)' }}>
						{dayjs(text).fromNow()}
					</span>
				</div> : <div>-</div>
			)
		},
		{
			title: 'Actions',
			key: 'actions',
			width: '5%',
			align: 'right',
			render: (_text, record: EventModel) => (
				<Dropdown
					overlayStyle={{ width: 200 }}
					trigger={['click']}
					menu={{
						items: [
							{
								label: <span className="d-flex align-items-center gap-2"><EditFilled /> Edit</span>,
								key: 'edit',
								onClick: async () => {
									dispatch(eventStateChange({ key: 'record', value: record }));
									dispatch(eventStateChange({ key: 'processingId', value: record.id }));
									setShowModal(true);
									window.history.replaceState(
										null,
										'',
										`/user/dashboard/${record.id}`
									);
								},
							},
							{
								label: <span className="d-flex align-items-center gap-2"><EyeFilled /> Preview</span>,
								key: 'preview',
								onClick: () => {
									window.open(`/events/${record.slug}-${record.id}`, '_blank', 'noopener,noreferrer');
								},
							},
							{
								label: record.status ? (
									<span className="d-flex align-items-center gap-2">
										<StopFilled /> Deactivate
									</span>
								) : (
									<span className="d-flex align-items-center gap-2">
										<CheckCircleFilled /> Activate
									</span>
								),
								key: 'status',
								danger: record.status,
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: `${record.status ? 'Deactivate' : 'Activate'} Event`,
										subTitle: <>Are you sure you want to {record.status ? 'deactivate' : 'activate'} <strong>{record.title}</strong> event?</>,
										additionalInfo: `${record.status ? 'Deactivated' : 'Activated'} event will ${record.status ? 'not be' : 'be'} visible to other users.`,
										btnLabel: `${record.status ? 'Deactivate' : 'Activate'} Now`,
										onBtnClick: async () => {
											const res = await dispatch(toggleEventStatusAction(record.id, !record.status));
											if (res && res.status === 200) {
												await dispatch(fetchAllProtectedEventsAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							},
							...(!record.approved_at ? [
								{
									label: (
										<span className="d-flex align-items-center gap-2">
											<CheckCircleFilled /> Approve
										</span>
									),
									key: 'approve',
									onClick: () => {
										dispatch(triggerConfirmation({
											modal: true,
											title: 'Approve Event',
											subTitle: <>Are you sure you want to approve <strong>{record.title}</strong> event?</>,
											additionalInfo: 'Approved event will be visible to normal users.',
											btnLabel: 'Approve Now',
											onBtnClick: async () => {
												const res = await dispatch(approveEventAction(record.id));
												if (res && res.status === 200) {
													await dispatch(fetchAllProtectedEventsAction({ type: activeTab, page: meta?.currentPage, perPage: meta?.perPage }));
													return res;
												}
											}
										}))
									}
								},
								{
									label: (
										<span className="d-flex align-items-center gap-2">
											<CloseCircleFilled /> Reject
										</span>
									),
									key: 'reject',
									danger: true,
									onClick: () => {
										dispatch(triggerConfirmation({
											modal: true,
											title: 'Reject Event',
											subTitle: <>Are you sure you want to reject <strong>{record.title}</strong> event?</>,
											additionalInfo: 'Please provide a reason for rejecting the event. The partner will be notified via email.',
											btnLabel: 'Reject Now',
											requireInput: true,
											inputPlaceholder: 'Enter the reason for rejecting the event',
											onBtnClick: async (reason?: string) => {
												if (!reason || reason.trim().length === 0) return;
												const res = await dispatch(rejectEventAction(record.id, reason));
												if (res && res.status === 200) {
													await dispatch(fetchAllProtectedEventsAction({ type: activeTab, page: meta?.currentPage, perPage: meta?.perPage }));
													return res;
												}
											}
										}))
									}
								}
							] : []),
							{
								label: <span className="d-flex align-items-center gap-2">
									<SplitCellsOutlined /> Use as Slider
								</span>,
								key: 'use-as-slider',
								disabled: !record.published_at || !record.status,
								onClick: () => {
									setEvent(record);
									setOperation('slider');
									setShowEventDateModal(true);
								}
							},
							{
								label: <span className="d-flex align-items-center gap-2">
									<StarFilled /> Set as featured
								</span>,
								key: 'set-as-featured',
								disabled: !record.published_at || !record.status,
								onClick: () => {
									setEvent(record);
									setOperation('featured');
									setShowEventDateModal(true);
								}
							},
							...((record.published_at && record.approved_at) ? [
								{
									label: <span className="d-flex align-items-center gap-2"><AccountBookFilled /> View Purchases</span>,
									key: 'view-purchases',
									onClick: () => {
										dispatch(eventStateChange({ key: 'record', value: record }));
										router.push(`/user/dashboard/purchases/${record.id}`);
									},
								},
								{
									label: (
										<span className="d-flex align-items-center gap-2">
											<CloseCircleFilled /> Cancel
										</span>
									),
									key: 'cancel',
									danger: true,
									onClick: () => {
										dispatch(triggerConfirmation({
											modal: true,
											title: 'Cancel Event',
											subTitle: <>Are you sure you want to cancel <strong>{record.title}</strong> event?</>,
											additionalInfo: 'Please provide a reason for canceling the event. The partner and users will be notified via email.',
											btnLabel: 'Cancel Event',
											requireInput: true,
											inputPlaceholder: 'Enter the reason for canceling the event',
											onBtnClick: async (reason?: string) => {
												if (!reason || reason.trim().length === 0) return;
												const res = await dispatch(cancelEventAction(record.id, reason));
												if (res && res.status === 200) {
													await dispatch(fetchAllProtectedEventsAction({ type: activeTab, page: meta?.currentPage, perPage: meta?.perPage }));
													return res;
												}
											}
										}))
									},
								}
							] : []),
						]
					}}
				>
					<MoreOutlined />
				</Dropdown>
			)
		},
	];

	useEffect(() => {
		if (!showModal) {
			dispatch(fetchAllProtectedEventsAction({ type: activeTab, page: meta?.currentPage || 1, perPage: 10 }));
			dispatch(fetchFeaturedEventsAction({ getCount: true }));
			dispatch(fetchSliderEventsAction({ getCount: true }));
		}
		if (eventId) {
			dispatch(fetchEventAction(Number(eventId)))
				.then((res) => {
					if (res && res.status === 200) {
						if (res.data.published_at) {
							window.history.replaceState(
								null,
								'',
								'/user/dashboard'
							);
						} else setShowModal(true);
					}
				});
		}
	}, [dispatch, showModal, eventId])

	useEffect(() => {
		dispatch(fetchAllProtectedEventsAction({ type: activeTab, page: meta?.currentPage || 1, perPage: 10 }));
	}, [activeTab])

	return (
		<>
			<div className="page-intro-header">
				<div className="header-title">
					<h1 className="titled-info">Events List</h1>
					<p>Manage all events here</p>
				</div>

				<div className="action-button-container">
					<Button onClick={() => setShowModal(true)}>Add Event</Button>
				</div>
			</div>

			<div className="d-flex gap-4">
				<Badge count={featuredEvents.count} color="green" showZero>
					<Tag color="pink">Featured Events</Tag>
				</Badge>
				<Badge count={sliderEvents.count} color="green" showZero>
					<Tag color="pink">Slider Events</Tag>
				</Badge>
			</div>

			<div className="dashboard-main-content">
				<Tabs
					activeKey={activeTab}
					items={[
						{
							key: 'published',
							label: 'Published Events',
						},
						{
							key: 'on-review',
							label: 'To Review',
						},
						{
							key: 'draft',
							label: 'Your Drafts',
						},
					]}
					onChange={(key) => {
						if (status === 'fetching') return;
						setActiveTab(key);
					}}
				/>
				<div className="tabled-content">
					{status === 'fetching' ? (
						<Table<EventModel>
							rowKey="id"
							columns={columns}
							dataSource={[]}
							rowClassName={() => 'custom-table-row'}
							className="dashboard-table"
							size='small'
							pagination={false}
							locale={{
								emptyText: <TableSkeleton length={10} />
							}}
						/>
					) : (
						<Table<EventModel>
							rowKey="id"
							columns={columns}
							dataSource={rows}
							rowClassName={() => 'custom-table-row'}
							size='small'
							pagination={false}
							locale={{
								emptyText: <div>No {activeTab} events available.</div>
							}}
						/>
					)}
					{(meta && meta.total > 10) && (
						<Pagination
							align="center"
							total={meta?.total}
							showSizeChanger={false}
							style={{ marginTop: '20px' }}
							current={meta?.currentPage}
							disabled={status === 'fetching'}
							onChange={(page) => dispatch(fetchAllProtectedEventsAction({ page, perPage: meta?.perPage }))}
						/>
					)}
				</div>
			</div>

			{event && operation && (
				<Modal
					title={operation === 'slider' ? 'Use Event in Slider' : operation === 'featured' ? 'Set Event as Featured' : 'Set Dates'}
					open={showEventDateModal}
					onCancel={() => {
						setFromDate(null);
						setToDate(null);
						setEvent(null);
						setOperation(null);
						setShowEventDateModal(false)
					}}
					footer={[
						<Button key="cancel"
							onClick={() => {
								setFromDate(null);
								setToDate(null);
								setEvent(null);
								setOperation(null);
								setShowEventDateModal(false)
							}}
						>
							Cancel
						</Button>,
						<Button
							type="primary"
							disabled={!fromDate || !toDate}
							onClick={() => {
								if (!fromDate || !toDate) return;
								if (operation === 'slider') {
									dispatch(triggerConfirmation({
										modal: true,
										title: 'Use Event in Slider',
										subTitle: <>Are you sure you want to use <strong>{event.title}</strong> as a slider event?</>,
										additionalInfo: 'This will show the event in the slider section of Home Page',
										btnLabel: 'Confirm',
										onBtnClick: async () => {
											const res = await dispatch(setEventAsSliderAction(event.id, fromDate, toDate));
											if (res && res.status === 201) {
												setShowEventDateModal(false);
												setEvent(null);
												setFromDate(null);
												setToDate(null);
												setOperation(null);
												dispatch(fetchAllProtectedEventsAction({ type: activeTab, page: meta?.currentPage, perPage: meta?.perPage }));
												dispatch(fetchSliderEventsAction({ getCount: true }));
											}
											return res;
										}
									}))
								} else if (operation === 'featured') {
									dispatch(triggerConfirmation({
										modal: true,
										title: 'Set Event as Featured',
										subTitle: <>Are you sure you want to set <strong>{event.title}</strong> as a featured event?</>,
										additionalInfo: 'This will show the event in the featured section of Home Page',
										btnLabel: 'Confirm',
										onBtnClick: async () => {
											const res = await dispatch(setEventAsFeaturedAction(event.id, fromDate, toDate));
											if (res && res.status === 200) {
												setShowEventDateModal(false);
												setEvent(null);
												setFromDate(null);
												setToDate(null);
												setOperation(null);
												dispatch(fetchAllProtectedEventsAction({ type: activeTab, page: meta?.currentPage, perPage: meta?.perPage }));
												dispatch(fetchFeaturedEventsAction({ getCount: true }));
											}
											return res;
										}
									}))
								} else return;
							}}
						>
							{operation === 'slider' ? 'Add to Slider' : operation === 'featured' ? 'Set as Featured' : 'Add'}
						</Button>,
					]}
					zIndex={999}
				>
					<div className="container mt-4">
						<div className="row">
							<div className="col-md-12">
								<div className="mb-3">
									<label className="font-weight-bold" htmlFor="fromDate">
										From
									</label>
									<DatePicker
										id="fromDate"
										className="w-100"
										value={fromDate ? dayjs(fromDate) : null}
										onChange={(value: dayjs.Dayjs | null) => {
											if (value) {
												setFromDate(value.startOf('day').toISOString());
												if (toDate && value.isAfter(dayjs(toDate))) {
													setToDate(null); // Reset toDate if fromDate is after it
												}
											} else {
												setFromDate(null);
											}
										}}
										disabledDate={(current) => {
											if (toDate) {
												return current && (current >= dayjs(toDate) || current < dayjs().startOf('day'))
											}
											return current && current < dayjs().startOf('day')
										}}
										format={(date) => dayjs(date).format('dddd, Do MMM YYYY')}
										aria-describedby="fromDateHelp"
									/>
									<small id="fromDateHelp" className="form-text text-muted">
										Date from which you want to {operation === 'slider' ? 'show the event in slider' : operation === 'featured' ? 'set the event as featured' : 'start'}
									</small>
								</div>
								<div className="mb-3">
									<label className="font-weight-bold" htmlFor="toDate">
										To
									</label>
									<DatePicker
										id="toDate"
										className="w-100"
										value={toDate ? dayjs(toDate) : null}
										onChange={(value: dayjs.Dayjs | null) => {
											if (value) {
												setToDate(value.startOf('day').toISOString());
												if (fromDate && value.isBefore(dayjs(fromDate))) {
													setFromDate(null); // Reset fromDate if toDate is before it
												}
											} else {
												setToDate(null);
											}
										}}
										disabledDate={(current) => {
											if (fromDate) {
												return current && current < dayjs(fromDate).add(1, 'day');
											}
											return current && current < dayjs().add(1, 'day');
										}}
										format={(date) => dayjs(date).format('dddd, Do MMM YYYY')}
										aria-describedby="toDateHelp"
									/>
									<small id="toDateHelp" className="form-text text-muted">
										Date to which you want to {operation === 'slider' ? 'remove the event from slider' : operation === 'featured' ? 'remove the event from featured' : 'end'}
									</small>
								</div>
							</div>
						</div>
					</div>
				</Modal>
			)}

			<AddEvent showModal={showModal} setShowModal={setShowModal} />
		</>
	)
}
