'use client'

import { usePathname } from "next/navigation";
import AuthLink from "../common/AuthLink.component";

export function NormalUserNav() {
	const pathname = usePathname();

	return (
		<div className="dashboard-navigation">
			<div className="dashboard-home">
				<a href="/"><i className="fa fa-home"/></a>
			</div>
			<div className="dash-nav-items">
				<ul>
					{/* <li>
						<AuthLink
							className={`${pathname === '/user/dashboard' ? 'active' : ''}`}
							href={'/user/dashboard'}
						>
							<i className="fa fa-history"/> HISTORY
						</AuthLink>
					</li> */}
					<li>
						<AuthLink
							className={`${pathname === '/user/dashboard/booked-events' ? 'active' : ''}`}
							href={'/user/dashboard/booked-events'}
						>
							<i className="fa-solid fa-calendar-day" /> EVENTS
						</AuthLink>
					</li>
					<li>
						<AuthLink
							className={`${pathname === '/user/dashboard/subscriptions' ? 'active' : ''}`}
							href={'/user/dashboard/subscriptions'}
						>
							<i className="fa-solid fa-square-check"/> SUBSCRIPTIONS
						</AuthLink>
					</li>
					<li>
						<AuthLink
							className={`${pathname === '/user/dashboard/settings' ? 'active' : ''}`}
							href={'/user/dashboard/settings'}
						>
							<i className="fa fa-cogs"/> SETTINGS
						</AuthLink>
					</li>
					<li>
						<AuthLink
							className={`${pathname === '/user/dashboard/preferences' ? 'active' : ''}`}
							href={'/user/dashboard/preferences'}
						>
							<i className="fa-solid fa-list-check"/> PREFERENCES
						</AuthLink>
					</li>
				</ul>
			</div>
			{/* <div className="add-info">
				<span><i className="fa fa-circle"/> 01:25</span>
				<a href="">
					<span style={{ marginRight: 24 }}>
						<Badge count={1}><Avatar shape="square" icon="user"/></Badge>
					</span>
				</a>
			</div> */}
		</div>
	)
}
