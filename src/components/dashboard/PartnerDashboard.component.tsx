'use client'

import { UserModel } from "@/types/models/user.model";
import { CaretR<PERSON>Filled, BarChartOutlined, WalletFilled } from "@ant-design/icons";
import { <PERSON><PERSON>, Divide<PERSON>, Spin } from "antd";
import { useRouter } from "next-nprogress-bar";
import ImageCropper from "../ImageCropper.component";
import { AppDispatch, RootState } from "@/store";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { PartnerStats, ServerStatus } from "@/types/index.types";
import { fetchPartnerStatsAction, updateCompanyLogoAction } from "@/store/slices/partner.slice";
import { UserLogModel } from "@/types/models/user-log.model";
import dayjs from "dayjs";
import { fetchUserLogsAction } from "@/store/slices/user.slice";
import { getFullImageUrl } from "@/utils/general.util";

export function PartnerDashboard({ user }: { user: UserModel }) {
	const router = useRouter();
	const dispatch = useDispatch<AppDispatch>();
	const { status, stats } = useSelector((state: RootState) => state.partner) as { status: ServerStatus, stats: PartnerStats | null };
	const { userLogs } = useSelector((state: RootState) => state.user) as { userLogs: UserLogModel[] };
	const [companyLogo, setCompanyLogo] = useState<string>(user?.companies?.[0]?.logo || '');

	useEffect(() => {
		dispatch(fetchPartnerStatsAction());
		dispatch(fetchUserLogsAction());
	}, [dispatch])

	return (
		<div className="row">

			<div className="col-md-8">
				<div className="dashboard_stats_card hightlights">

					<ImageCropper
						value={companyLogo as string}
						aspectRatio={1}
						isCircular={true}
						renderUploadTrigger={(openUpload) => (
							status === 'saving' ? <div className="company-logo">
								<Spin size="large" />
							</div> :
								<div className="company-logo" onClick={openUpload}>
									<img
										src={companyLogo ?
											getFullImageUrl(companyLogo) :
											'https://placehold.co/150x150/png?text=Upload'}
										alt="Company Logo"
									/>
								</div>
						)}
						onCropComplete={async (croppedImageUrl) => {
							const res = await dispatch(
								updateCompanyLogoAction(
									croppedImageUrl.split('/').at(-1) as string,
									user?.companies?.[0]?.id as number
								));
							if (res && res.status === 200) setCompanyLogo(croppedImageUrl.split('/').at(-1) as string);
						}}
					/>
					<h1 className="titled-info">{user?.companies?.[0]?.name || '-'}</h1>
					<p>{user?.companies?.[0]?.about || '-'}</p>
					<br />
					<div className="company_info_box">
						<div className="form-group">
							<label htmlFor="">Email</label>
							<div>{user?.companies?.[0]?.email || '-'}</div>
						</div>
						<div className="form-group">
							<label htmlFor="">Phone</label>
							<div>{user?.companies?.[0]?.phone || '-'}</div>
						</div>
						<div className="form-group">
							<label htmlFor="">Address</label>
							<div>{user?.companies?.[0]?.address?.description || '-'}</div>
						</div>
					</div>

					<div className={'d-flex mt-5'}>
						<Button block type={'primary'} shape={'round'} size={'large'}
							onClick={() => router.push('/user/dashboard/company')}>
							Update Info
						</Button>
					</div>
				</div>
			</div>

			{false && <div className="col-md-4">
				<div className="dashboard_stats_card">
					<h1 className="titled-info">Partner</h1>
					<p>Promote different kind of business online for free. Click the below options to add Event, Travels, Venues, Theatre etc.</p>
					<ul className={'venue_add_action'}>
						<li>
							<a
								href=""
								onClick={(e) => {
									e.preventDefault();
									router.push('/user/dashboard/venues')
								}}
							>
								<div><h3>Venues </h3> 3 Venue</div>
								<CaretRightFilled />
							</a>
						</li>
						<li>
							<a
								href=""
								onClick={(e) => {
									e.preventDefault();
									router.push('/user/dashboard/travels')
								}}
							>
								<div><h3>Travels </h3> 4 Travels</div>
								<CaretRightFilled />
							</a>
						</li>
						<li>
							{/* <a
							href=""
							// onClick={(e) => {
							// 	e.preventDefault();
							// 	this.props.fetchCategories();
							// 	this.props.eventGenerateStateChange({
							// 		props: 'addEvents',
							// 		value: true,
							// 		deep: 'addModal'
							// 	});
							// }}
						>
							<div><h3>Events </h3> 2 Events</div>
							<CaretRightFilled />
						</a> */}
							<a href="" onClick={(e) => {
								e.preventDefault();
								router.push('/user/dashboard/events')
							}}>
								<div><h3>Events </h3> 2 Events</div>
								<CaretRightFilled />
							</a>
						</li>
						{/* <li>
						<a href="" className={'inactive'}>
							<div><h3>Theater </h3> 0 Theater</div>
							<CaretRightFilled />
						</a>
					</li>
					<li>
						<a href="">
							<div><h3>Vehicles </h3> 0 Vehicles</div>
							<CaretRightFilled />
						</a>
					</li> */}
					</ul>
				</div>
			</div>
			}

			<div className="col-md-4">
				<div className="dashboard_statistics">
					<h1>My Stats</h1>
					<ul className={'stats_info'}>
						<li>
							<div className={'icon_presenter'}>
								<BarChartOutlined />
							</div>
							<div>
								<p>Your Total Events</p>
								<h3>{stats?.totalEvents || '-'}</h3>
							</div>
						</li>

						<li>
							<div className={'icon_presenter'}>
								<WalletFilled />
							</div>
							<div>
								<p>Total Bookings</p>
								<h3>{stats?.totalTicketsSold || '-'}</h3>
							</div>
						</li>

					</ul>
				</div>

				<Divider />

				<div className="activity_logs">
					<div className="title_sub_box">
						<div>
							<h2>My Latest Activity</h2>
							{/* <p>6 Activities this month</p> */}
						</div>
						{/* <Button>See all</Button> */}
					</div>
					<ul className={'activity_list'}>
						{userLogs && userLogs.length > 0 ? (
							userLogs.map((log) => (
								<li key={log.id}>
									<div>
										<h4>{log.details || 'No Details'}</h4>
										<p>{dayjs(log.created_at).format('DD MMM YYYY, h:mm A')}</p>
									</div>
								</li>
							))
						) : (
							<li>
								<div>
									<h4>No activity found</h4>
								</div>
							</li>
						)}
						{/* <li>
							<div>
								<h4>Albatross Concert</h4>
								<p>2 tickets purchased</p>
							</div>
							<h4>
								RS 400
							</h4>
						</li>
						<li>
							<div>
								<h4>Booked Art Gallery</h4>
								<p>30th september 2020</p>
							</div>
							<h4>
								RS 720
							</h4>
						</li>

						<li>
							<div>
								<h4>Travel Package Inquiry</h4>
								<p>2 enquiries</p>
							</div>
							<h4>
								RS 180
							</h4>
						</li> */}
					</ul>
				</div>
			</div>
		</div>
	)
}
