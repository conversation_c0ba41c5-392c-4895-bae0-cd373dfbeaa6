'use client'

import { isAuthenticatedUser } from "@/utils/cookie.util";
import Link from "next/link";
import AuthLink from "./common/AuthLink.component";

export default function FooterLinks() {
	const isAuthenticated = isAuthenticatedUser();

	return (
		<div className="col-lg-3">
			<h2>eticketnepal</h2>
			<p><Link href={'/support'}>Help & Support</Link></p>
			<p><Link href={'/events'}>Events</Link></p>
			{isAuthenticated ? (
				<p>
					<AuthLink href={'/user/dashboard'}>Dashboard</AuthLink>
				</p>
			) : (
				<>
					<p><Link href={'/user/signin'}>Sign in</Link></p>
					<p><Link href={'/user/signup'}>Register</Link></p>
				</>
			)}
		</div>
	)
}
