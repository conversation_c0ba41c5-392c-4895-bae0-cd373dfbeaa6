'use client';

import { useState } from 'react';
import AppModal from './AppModal';
import { message } from 'antd';
import { DirectionsRenderer, GoogleMap } from '@react-google-maps/api';
import { useGoogleMaps } from '@/context/GoogleMapsContext';

type LatLng = { lat: number; lng: number };

type DirectionsMapProps = { destination: LatLng };

const DirectionsMap = ({ destination }: DirectionsMapProps) => {
	const [directions, setDirections] = useState<google.maps.DirectionsResult | null>(null);
	const [showMapModal, setShowMapModal] = useState<boolean>(false);
	const [isLoading, setIsLoading] = useState<boolean>(false);

	const { isLoaded } = useGoogleMaps();

	if (!isLoaded) return null;

	const getUserLocation = () => {
		if (navigator.geolocation) {
			setIsLoading(true);
			navigator.geolocation.getCurrentPosition(
				(position) => {
					const origin = { lat: position.coords.latitude, lng: position.coords.longitude };
					calculateRoute(origin, destination);
				},
				(error) => {
					setIsLoading(false);
					console.error('Error getting location:', error);
					switch (error.code) {
					case error.PERMISSION_DENIED:
						message.error('Location access was denied. Please enable location permissions in your browser settings.');
						break;
					case error.POSITION_UNAVAILABLE:
						message.error('Location information is unavailable. This might be due to a GPS or network issue.');
						break;
					case error.TIMEOUT:
						message.error('The request to get your location timed out. Please try again.');
						break;
					default:
						message.error('An unknown error occurred while retrieving your location.');
						break;
					}
				},
				{ enableHighAccuracy: true }
			);
		} else {
			message.error('Geolocation is not supported by this browser.');
		}
	};

	const calculateRoute = (origin: LatLng, destination: LatLng) => {
		const directionsService = new google.maps.DirectionsService();
		directionsService.route(
			{
				origin,
				destination,
				travelMode: google.maps.TravelMode.DRIVING,
			},
			(result, status) => {
				setIsLoading(false);
				if (status === google.maps.DirectionsStatus.OK) {
					setDirections(result);
					setShowMapModal(true);
				} else {
					console.error('Directions request failed due to ', status);
					message.error('Unable to get directions. Please try again later.');
				}
			}
		);
	};

	return (
		<>
			<p>
				<a
					href="#"
					onClick={(e) => {
						e.preventDefault();
						getUserLocation();
					}}
				>
					<i className='fa-regular fa-compass me-2' />
					{isLoading ? (
						'Loading directions ...'
					) : 'Get directions'}
				</a>
			</p>
			<AppModal showModal={showMapModal} toggleAction={() => setShowMapModal(false)} title="Directions">
				{directions && (
					<GoogleMap
						mapContainerStyle={{
							height: '500px',
							width: '100%',
						}}
						zoom={15}
						center={destination}
					>
						<DirectionsRenderer directions={directions} />
					</GoogleMap>
				)}
			</AppModal>
		</>
	);
};

export default DirectionsMap;
