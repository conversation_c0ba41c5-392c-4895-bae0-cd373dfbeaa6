'use client'

import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalHeader } from 'reactstrap'
import { Button } from 'antd'

interface AppModalProps {
  showModal: boolean
  toggleAction?: () => void
  title: string
  hideHeader?: boolean
  showFooter?: boolean
  modalClass?: string
  children: React.ReactNode
}

const AppModal: React.FC<AppModalProps> = ({
	showModal,
	toggleAction,
	title,
	hideHeader = false,
	showFooter = false,
	modalClass = 'modal-lg',
	children
}) => {
	const footer = showFooter ? (
		<ModalFooter>
			<Button type="primary" onClick={toggleAction} className={''}>
        Submit Review
			</Button>
			{/* <Button onClick={toggleAction}>Cancel</Button> */}
		</ModalFooter>
	) : null

	const header = !hideHeader ? (
		<ModalHeader
			toggle={toggleAction}
			className='modal-header'
		>
			<p className="modal-title" id="exampleModalLabel">
				{title}
			</p>
			<div className="decorate-bar" />
		</ModalHeader>
	) : null

	return (
		<Modal
			fade={true}
			isOpen={showModal}
			toggle={toggleAction}
			className={`app-modal modal-dialog modal-dialog-centered ${modalClass}`}
		>
			{header}
			<div className="modal-body">{children}</div>
			{footer}
		</Modal>
	)
}

export default AppModal
