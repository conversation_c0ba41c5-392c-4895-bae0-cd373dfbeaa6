'use client';

import Link from 'next/link';
import React from 'react';
import { isAuthenticatedUser } from '@/utils/cookie.util';

interface AuthLinkProps {
  href: string;
	className?: string;
  children: React.ReactNode;
}

const AuthLink: React.FC<AuthLinkProps> = ({ href, className = '', children }) => {
	// Check if user is authenticated
	const isAuthenticated = isAuthenticatedUser();

	return (
		<Link href={href} className={className} prefetch={isAuthenticated}>
			{children}
		</Link>
	);
};

export default AuthLink;
