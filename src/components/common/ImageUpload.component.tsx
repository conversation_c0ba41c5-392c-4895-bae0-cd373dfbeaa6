'use client'

import React, { useEffect, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Image, message, Upload } from 'antd';
import type { UploadFile, UploadProps } from 'antd';
import Compressor from 'compressorjs';
import { AppDispatch } from '@/store';
import { useDispatch } from 'react-redux';
import { uploadImageAction } from '@/store/slices/app.slice';
import { getFullImageUrl } from '@/utils/general.util';

// Helper to convert a file to a Base64 string for previewing
const getBase64 = (file: File): Promise<string> =>
	new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = () => resolve(reader.result as string);
		reader.onerror = (error) => reject(error);
	});

interface ImageUploaderProps {
  maxCount?: number; // maximum number of files allowed
	initialImages: string[]; // Array of image names from DB
  onImagesChange: (images: string[]) => void; // Callback for returning updated images list
}

const ImageUpload: React.FC<ImageUploaderProps> = ({
	maxCount = 6,
	initialImages,
	onImagesChange,
}) => {
	const dispatch = useDispatch<AppDispatch>();
	const [previewOpen, setPreviewOpen] = useState<boolean>(false);
	const [previewImage, setPreviewImage] = useState<string>('');
	const [fileList, setFileList] = useState<UploadFile[]>([]);
	const [uploading, setUploading] = useState<boolean>(false);

	useEffect(() => {
		const formattedImages: UploadFile[] = initialImages.map((name, index) => ({
			uid: `existing-${index}`,
			name,
			status: 'done',
			url: getFullImageUrl(name),
		}));
		setFileList(formattedImages);
	}, []);

	// Handle file preview. If no URL is available, convert the file to Base64.
	const handlePreview = async (file: UploadFile) => {
		if (!file.url && !file.preview) {
			file.preview = await getBase64(file.originFileObj as File);
		}
		setPreviewImage(file.url || (file.preview as string));
		setPreviewOpen(true);
	};

	// Handle changes to the file list (adding, removing, etc.)
	const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
		setFileList(newFileList);
		const updatedImageList = newFileList.filter(file => file.status === 'done').map(file => file.url?.split('/').at(-1) || file.response?.split('/').at(-1));
		onImagesChange(updatedImageList);
	};

	const handleRemove = (file: UploadFile) => {
		const updatedFileList = fileList.filter(item => item.uid !== file.uid);
		setFileList(updatedFileList);
		onImagesChange(updatedFileList.map(f => f.url?.split('/').at(-1) || f.response?.split('/').at(-1)));
	};

	// Custom upload request using Compressor.js to compress the image before uploading.
	const handleCustomRequest: UploadProps['customRequest'] = (options) => {
		const { file, onSuccess, onError } = options;
		const currentFile = file as File;
		const maxSize = 5 * 1024 * 1024; // 5 MB in bytes

		// Validate file size
		if (currentFile.size > maxSize) {
			message.error('Image size exceeds 5 MB');
			onError?.(new Error('Image size exceeds 5 MB'));
			return;
		}

		// Compress the image using Compressor.js
		new Compressor(currentFile, {
			quality: 0.4,
			success(compressedFile) {
				const formData = new FormData();
				// const fileName =
				//   compressedFile instanceof File ? compressedFile.name : currentFile.name;
				formData.append('file', compressedFile);

				setUploading(true);
				// Upload the compressed image to the server
				dispatch(uploadImageAction(formData))
					.then((res) => {
						if (res && res.status === 200) {
							const imageUrl = res.data?.Location;
							onSuccess?.(imageUrl, {} as XMLHttpRequest);
						} else {
							message.error('Upload failed');
							onError?.(new Error('Upload failed'));
						}
					})
					.catch(() => {
						message.error('Upload failed');
						onError?.(new Error('Upload failed'));
					})
					.finally(() => {
						setUploading(false);
					});
			},
			error(err) {
				message.error('Image compression failed');
				onError?.(err);
			},
		});
	};

	const uploadButton = (
		<div>
			<PlusOutlined />
			<div style={{ marginTop: 8 }}>Upload</div>
		</div>
	);

	return (
		<div className='mx-2'>
			{maxCount > 1 && (
				fileList.length >= maxCount ? (
					<p className='mb-2'>Max Count Reached</p>
				) : (
					<p className='mb-2'>{`Click to upload image (max ${maxCount})`}</p>
				)
			)}
			<Upload
				listType="picture-card"
				fileList={fileList}
				onPreview={handlePreview}
				onChange={handleChange}
				customRequest={handleCustomRequest}
				onRemove={handleRemove}
				disabled={uploading}
			>
				{fileList.length >= maxCount ? null : uploadButton}
			</Upload>
			{previewImage && (
				<Image
					wrapperStyle={{ display: 'none' }}
					preview={{
						visible: previewOpen,
						onVisibleChange: (visible) => setPreviewOpen(visible),
						afterOpenChange: (visible) => !visible && setPreviewImage(''),
					}}
					src={previewImage}
				/>
			)}
		</div>
	);
};

export default ImageUpload;
