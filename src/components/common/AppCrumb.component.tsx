import { HomeOutlined } from "@ant-design/icons";
import React from "react";
import Link from "next/link";
import { BreadcrumbItem } from "@/types/index.types";

interface AppCrumbProps {
  looper: BreadcrumbItem[];
}

const AppCrumb: React.FC<AppCrumbProps> = ({ looper }) => {
	return (
		<div className="ant-breadcrumb">
			{looper.map((slug, i) => (
				<span key={i}>
					{i === 0 ? (
						<Link href={slug.link}>
							<HomeOutlined />
						</Link>
					) : i === looper.length - 1 ? (
						<span>{slug.name}</span>
					) : (
						<Link href={slug.link}>{slug.name}</Link>
					)}
					{i < looper.length - 1 && <span className="ant-breadcrumb-separator mx-1">/</span>}
				</span>
			))}
		</div>
	);
};

export default AppCrumb;
