'use client'

import { Button, Input, Modal } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../store";
import { triggerConfirmation } from "@/store/slices/app.slice";
import { useState } from "react";

const ConfirmationModal = () => {
	const { modal, title, subTitle, additionalInfo, btnLabel, onBtnClick, status, requireInput, inputPlaceholder } = useSelector((state: RootState) => state.app.actionConfirmation);
	const dispatch = useDispatch<AppDispatch>();

	const [inputValue, setInputValue] = useState<string>("");

	return <>
		<div>
			<Modal
				open={modal}
				footer={false}
				width={450}
				closable={false}
			>
				<div className="confirmation-app-modal">
					<div className="alert-modal-exclam">
						<img src='/images/exclamation.png'/>
						<h2>{title}</h2>
						<p>{subTitle}</p>

						{additionalInfo && <p>{additionalInfo}</p>}
					</div>
					{requireInput && (
						<div style={{ marginTop: '1rem' }}>
							<Input.TextArea
								rows={3}
								placeholder={inputPlaceholder || "Enter your reason..."}
								value={inputValue}
								showCount
								maxLength={200}
								onChange={(e) => setInputValue(e.target.value)}
							/>
							{inputValue.length < 10 && <p style={{ color: 'red', marginTop: '0.5rem' }}>Minimum 10 characters required.</p>}
						</div>
					)}
					<br />
					<div className="confirmation-action">
						<div>
							<Button
								block
								type='primary'
								size='large'
								loading={status === 'waiting'}
								disabled={requireInput && inputValue.trim().length < 10}
								onClick={async () => {
									if (onBtnClick) {
										try {
											dispatch(triggerConfirmation({ status: 'waiting' }));
											const res = await onBtnClick(requireInput ? inputValue : undefined);
											if (res && (res.status === 200 || res.status === 201)) {
												setInputValue("");
												dispatch(triggerConfirmation({ modal: false, status: 'idle', requireInput: false }));
											}
										} catch (error) {
											console.log(error);
										} finally {
											dispatch(triggerConfirmation({ status: 'idle' }));
										}
									}
								}}
							>
								{btnLabel}
							</Button>
						</div>
						<div>
							<Button
								block
								size="large"
								disabled={status === 'waiting'}
								onClick={ () => {
									setInputValue("");
									dispatch(triggerConfirmation({ modal: false, requireInput: false }))
								}}
							>
								Cancel
							</Button>
						</div>
					</div>

				</div>
			</Modal>
		</div>
	</>
}
export default ConfirmationModal;
