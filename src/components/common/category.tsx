'use client'

import { CategoryModel } from "@/types/models/category.model";
import { Dropdown, Checkbox } from "antd";
import type { MenuProps } from "antd";

type CategoryProps = {
  categories: CategoryModel[];
	selectedCategories: number[];
	setSelectedCategories: React.Dispatch<React.SetStateAction<number[]>>;
	filterCategory: (categoryId: number) => void;
	onAllOptionClick?: () => void;
};

export default function Component({ categories, selectedCategories, setSelectedCategories, filterCategory, onAllOptionClick }: CategoryProps) {
	const visibleCategories = categories.slice(0, 7);
	const dropdownCategories = categories.slice(7);

	const dropdownItems: MenuProps['items'] = dropdownCategories.map((cat) => ({
		key: cat.id.toString(),
		label: (
			<Checkbox
				checked={selectedCategories.includes(cat.id)}
				onChange={() => filterCategory(cat.id)}
			>
				<i className={cat.icon_name || ""} /> {cat.name}
			</Checkbox>
		),
	}));

	return (
		<div className="category-container">
			<div className="category-select-filter">
				<div className="category-group">
					<a
						href=""
						onClick={(e) => {
							e.preventDefault();
							setSelectedCategories([]);
							onAllOptionClick?.();
						}}
						className={`category-item primary ${selectedCategories.length === 0 ? 'active' : ''}`}
					>
						<i className="fas fa-th-large" style={{ color: '#00000 !important' }} /> All Events
					</a>
					{visibleCategories.map((category) => (
						<a
							key={category.id}
							href=""
							onClick={(e) => {
								e.preventDefault();
								filterCategory(category.id);
							}}
							className={`category-item ${selectedCategories.includes(category.id) ? 'active' : ''}`}
						>
							<i className={category.icon_name || ""} />
							<span>{category.name}</span>
						</a>
					))}
					{dropdownCategories.length > 0 && (
						<Dropdown menu={{ items: dropdownItems }} trigger={['click']}>
							<a href="" onClick={(e) => e.preventDefault()} className="category-item more-btn">
								<i className="fas fa-ellipsis-h" />
								<span>More Categories</span>
							</a>
						</Dropdown>
					)}
				</div>
			</div>
		</div>
	);
}
