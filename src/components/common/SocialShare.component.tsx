'use client'

import {
	FacebookShareButton,
	TwitterShareButton,
	LinkedinShareButton,
	FacebookIcon,
	LinkedinIcon,
	FacebookMessengerIcon,
	FacebookMessengerShareButton,
	XIcon
} from "react-share";

export default function SocialShare({ url, title }: { url: string; title: string }) {
	return (
		<ul>
			{/* Facebook */}
			<li>
				<FacebookShareButton url={url} htmlTitle={title} hashtag="#eticketnepal">
					<FacebookIcon size={32} round />
				</FacebookShareButton>
			</li>

			{/* Twitter / X */}
			<li>
				<TwitterShareButton url={url} title={title} hashtags={['eticketnepal']}>
					<XIcon size={32} round />
				</TwitterShareButton>
			</li>

			{/* Messenger */}
			<li>
				<FacebookMessengerShareButton url={url} appId={process.env.NEXT_PUBLIC_FACEBOOK_CLIENT_ID as string}>
					<FacebookMessengerIcon size={32} round />
				</FacebookMessengerShareButton>
			</li>

			{/* LinkedIn */}
			<li>
				<LinkedinShareButton url={url} title={title}>
					<LinkedinIcon size={32} round />
				</LinkedinShareButton>
			</li>
		</ul>
	);
}
