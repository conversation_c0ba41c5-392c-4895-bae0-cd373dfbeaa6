'use client'

import { convertNewLinesToBr, stringLimit } from '@/utils/general.util';
import { useState } from 'react';

interface ExpandableContentProps {
  content: string; // HTML string
  initialLimit: number; // Character limit for truncated view
  className?: string; // Optional styling
}

const ExpandableContent: React.FC<ExpandableContentProps> = ({
	content,
	initialLimit,
	className
}) => {
	const [isExpanded, setIsExpanded] = useState(false);

	const displayContent = isExpanded ? content : stringLimit(content, initialLimit);

	const toggleExpand = (e: React.MouseEvent) => {
		e.preventDefault();
		setIsExpanded(!isExpanded);
	};

	return (
		<div className={className}>
			<div
				style={{
					overflowWrap: 'break-word',
					hyphens: 'auto',
					WebkitHyphens: 'auto',
					msHyphens: 'auto'
				}}
				dangerouslySetInnerHTML={{
					__html: convertNewLinesToBr(displayContent),
				}}
			/>
			{content.length > initialLimit && (
				<a
					href="#"
					onClick={toggleExpand}
					className="text-blue-500 hover:underline"
				>
					<small style={{ fontSize: '12px' }}>{isExpanded ? 'Show Less' : 'Read More'}</small>
				</a>
			)}
		</div>
	);
};

export default ExpandableContent;
