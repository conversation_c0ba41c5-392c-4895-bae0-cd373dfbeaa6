"use client";

import { useEffect, useMemo, useState } from "react";
import CryptoJS from "crypto-js";
import { v4 as uuidv4 } from 'uuid';
export default function EsewaPay({ amount }: { amount: number }) {

	const transactionUUID = useMemo(() => uuidv4(), []);
	const [formData, setFormData] = useState({
		amount: amount,
		tax_amount: "0",
		total_amount: amount,
		transaction_uuid: transactionUUID,
		product_code: process.env.NEXT_PUBLIC_ESEWA_PRODUCT_CODE,
		product_service_charge: "0",
		product_delivery_charge: "0",
		// success_url: window.location.href+"&paymentsuccess=true",
		success_url: window.location.href + "&payment_gateway=esewa&paymentsuccess=true"+"&uuid="+transactionUUID,
		failure_url: window.location.href + "payment_gateway=esewa&paymentsuccess=false"+"&uuid="+transactionUUID,
		// failure_url: window.location.href+"&paymentsuccess=false",
		signature: "",
		secret: "8gBm/:&EnhH.1/q",
		signed_field_names: "total_amount,transaction_uuid,product_code",


	})
	const generateSignature = (
		total_amount: number,
		transaction_uuid: string,
		product_code: string,
		secret: string
	) => {
		const hashString = `total_amount=${total_amount},transaction_uuid=${transaction_uuid},product_code=${product_code}`;
		const hash = CryptoJS.HmacSHA256(hashString, secret);
		const hashedSignature = CryptoJS.enc.Base64.stringify(hash);
		return hashedSignature;
	};

	useEffect(() => {
		const { total_amount, transaction_uuid, product_code, secret } = formData;
		const hashedSignature = generateSignature(total_amount, transaction_uuid, product_code!, secret)
		// setFormData({ ...formData, signature: hashedSignature })
		setFormData((prev) => ({
			...prev,
			signature: hashedSignature,
		}));
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [amount])

	return (
		<div>
			<form
				className="amount-form"
				action={process.env.NEXT_PUBLIC_ESEWA_GATEWAY}
				method="POST"
			>
				<label htmlFor="amount">Amount:</label>
				<input type="text" name="amount" value={formData.amount} readOnly required />
				<input type="hidden" name="tax_amount" value={formData.tax_amount} required />
				<input type="hidden" name="total_amount" value={formData.total_amount} required />
				<input type="hidden" name="transaction_uuid" value={formData.transaction_uuid} required />
				<input type="hidden" name="product_code" value={formData.product_code} required />
				<input type="hidden" name="product_service_charge" value={formData.product_service_charge} required />
				<input
					type="hidden"
					name="product_delivery_charge"
					value={formData.product_delivery_charge}
					required
				/>
				<input
					type="hidden"
					name="success_url"
					value={formData.success_url}
					required
				/>
				<input
					type="hidden"
					name="failure_url"
					value={formData.failure_url}
					required
				/>
				<input
					type="hidden"
					name="signed_field_names"
					value={formData.signed_field_names}
					required
				/>
				<input
					type="hidden"
					name="signature"
					value={formData.signature}
					required
				/>
				<button type="submit">Submit</button>
			</form>
		</div>
	);
}
