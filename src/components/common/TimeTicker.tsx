'use client'

import { useState, useEffect } from "react"
import dayjs from "dayjs"
import duration from "dayjs/plugin/duration"

dayjs.extend(duration)

export default function TimeTicker({ initialTime, showDraftSaved = false }: { initialTime: dayjs.Dayjs, showDraftSaved?: boolean }) {
	const [currentTime, setCurrentTime] = useState(dayjs())

	useEffect(() => {
		const interval = setInterval(() => {
			setCurrentTime(dayjs())
		}, 1000)

		return () => clearInterval(interval)
	}, [])

	const diff = dayjs.duration(currentTime.diff(initialTime))
	const hours = diff.hours()
	const minutes = diff.minutes()
	const seconds = diff.seconds()

	let duration = '';
	if (hours === 0 && minutes === 0 && seconds > 0) {
		duration = `${seconds} sec`
	} else {
		duration = `${hours > 0 ? `${hours} hr` : ''}${minutes > 0 ? ` ${minutes} min` : ''}`.trim()
	}

	if (!duration) return null;

	return showDraftSaved ? (
		<>
			<strong>Draft saved </strong> {duration} ago
		</>
	) : (
		duration
	)
}
