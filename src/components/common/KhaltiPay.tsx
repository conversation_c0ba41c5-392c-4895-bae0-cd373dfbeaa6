'use client';

import { AppDispatch } from "@/store";
import { khaltiPayment } from "@/store/slices/event.slice";
import { FormEvent } from "react";
import { useDispatch } from 'react-redux'


export default function KhaltiPay({ amount, title, eventId }: { amount: number, title: string, eventId: number }) {
	const dispatch = useDispatch<AppDispatch>()

	const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		// Ensure that khaltiPayment is defined as an async thunk returning a promise.
		const khaltiPayRes =  await dispatch(khaltiPayment({ amount, purchase_order_name: title, purchase_order_id: eventId,  return_url: window.location.href+"&payment_gateway=khalti" }));
		if (khaltiPayRes && khaltiPayRes.data.data.pidx && khaltiPayRes.data.data.payment_url) {
			window.location.href = khaltiPayRes.data.data.payment_url;
		}
	};
	return (
		<form className="amount-form" onSubmit={handleSubmit}>
			<label htmlFor="amount">Amount:</label>
			<input
				type="number"
				id="amount"
				name="amount"
				value={amount}
				readOnly
				// onChange={(e) => setAmount(e.target.value)}
				required
			/>
			<button type="submit">Pay With Khalti</button>
		</form>
	)

}

