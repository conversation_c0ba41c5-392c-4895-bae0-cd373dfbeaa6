'use client'

import { useGoogleMaps } from '@/context/GoogleMapsContext';
import { AddressData } from '@/types/index.types';
import { HomeOutlined, EnvironmentOutlined, GlobalOutlined, MailOutlined } from '@ant-design/icons';
import { GoogleMap, Marker } from '@react-google-maps/api';

type MapProps = {
  address: AddressData;
  showInfo?: boolean;
	mapHeight?: string;
};

// Kathmandu
const DEFAULT_CENTER = {
	lat: 27.7172,
	lng: 85.3240,
};

const AddressInfo = ({ address }: { address: AddressData }) => (
	<div className="map-info">
		<h4>{address.description || '-'}</h4>
		<div className="address-details">
			<div key={'country'} className="address-item">
				<GlobalOutlined />
				<span>{address.country || '-'}</span>
			</div>
			<div key={'zone'} className="address-item">
				<EnvironmentOutlined />
				<span>{address.zone || '-'}</span>
			</div>
			<div key={'city'} className="address-item">
				<HomeOutlined />
				<span>{address.city || '-'}</span>
			</div>
			<div key={'zip-code'} className="address-item">
				<MailOutlined />
				<span>{address.zipCode || '-'}</span>
			</div>
		</div>
	</div>
);

const MapComponent = ({
	address,
	showInfo = false,
	mapHeight = '300px',
}: MapProps) => {
	const { isLoaded } = useGoogleMaps();

	const center = {
		lat: address?.lat || DEFAULT_CENTER.lat,
		lng: address?.lng || DEFAULT_CENTER.lng,
	};

	if (!isLoaded) return null;

	return (
		<div className='map-container'>
			<GoogleMap
				mapContainerStyle={{
					height: mapHeight,
					width: '100%',
					borderRadius: '10px',
				}}
				zoom={15}
				center={center}
			>
				<Marker position={center} />
			</GoogleMap>
			{showInfo && <AddressInfo address={address} />}
		</div>
	);
};

export default MapComponent;
