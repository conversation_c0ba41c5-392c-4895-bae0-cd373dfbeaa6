import Link from 'next/link';
import ExpandableContent from './ExpandableContent.component';
import MapComponent from './Map.component';
import { AddressData } from '@/types/index.types';
import { getFullImageUrl } from '@/utils/general.util';

type InfoCardProps = {
  image?: string;
  title: string;
  subTitle?: string;
  description: string;
  href?: string;
  as: string;
	address?: AddressData | null;
};

const InfoCard: React.FC<InfoCardProps> = ({ image, title, subTitle, href = null, as, description, address }) => {
	return (
		<div className="card_horizontal">
			<div className="image_container">
				{image && <img src={getFullImageUrl(image)} alt={title} />}
			</div>
			<div className="content_container">
				<h2>
					<Link as={as} href={href || '#'}>
						{title}
					</Link>
				</h2>
				{subTitle && <p><small>{subTitle}</small></p>}
				<ExpandableContent content={description} initialLimit={100} className='content' />
			</div>
			{address && <div className='mt-4'>
				<MapComponent address={address} showInfo={true} />
			</div>}
		</div>
	);
};

export { InfoCard };
