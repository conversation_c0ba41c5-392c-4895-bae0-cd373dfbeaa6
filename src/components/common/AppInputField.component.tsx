import { CaretDownFilled, CloseCircleFilled, PlusOutlined } from "@ant-design/icons";
import { Button, DatePicker, DatePickerProps, Input, Radio, RadioGroupProps, Select, Spin, Tag, TimePicker } from "antd";
import { SelectProps } from "antd/lib";
import dayjs from "dayjs";
import { CSSProperties, useState } from "react";
import relativeTime from 'dayjs/plugin/relativeTime';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { IMaskInput } from 'react-imask'

dayjs.extend(relativeTime);
dayjs.extend(customParseFormat);

const { TextArea } = Input;

/* eslint-disable @typescript-eslint/no-explicit-any */
type AppInputFieldProps = {
  onChangeInput?: (val : any) => void;
  label?: string;
  info?: string;
  id?: string;
  placeholder?: string;
  mask?: string;
  value?: any;
  style?: CSSProperties;
  onClose?: any;
	mode?: SelectProps['mode'];
	options?: SelectProps['options'];
	rows?: number;
	showTime?: boolean;
	showSelectedInfo?: boolean;
	errorMessage?: string;
	disabled?: boolean;
	disabledDate?: (current: dayjs.Dayjs) => boolean;
	maxCount?: SelectProps['maxCount'];
	optionRender?: SelectProps['optionRender'];
	type?: 'string' | 'number';
	disabledTime?: DatePickerProps['disabledTime'];
	autoFocus?: boolean;
	radioOptions?: RadioGroupProps['options'];
	radioOptionType?: RadioGroupProps['optionType'];
	maxLength?: number;
	onSearch?: (value: string) => void;
	searching?: boolean;
	required?: boolean;
};

const AppInputField: React.FC<AppInputFieldProps> = ({ onChangeInput, label, info, id, placeholder, mask, value, style, onClose, errorMessage, disabled, type = 'string', autoFocus = false, maxLength, required = false }) => {
	return <div className="input-container" style={style}>
		{label && <label htmlFor={id}>{label} {required && <span className="text-danger">*</span>}</label>}
		{errorMessage && <span className="error-message">({errorMessage})</span>}
		{info && <p>{info}</p>}

		{
			mask ?
				<IMaskInput
					id={id}
					mask={mask}
					value={value}
					placeholder={placeholder || ''}
					onChange={(e) => {
						if (onChangeInput) {
							onChangeInput(e.currentTarget.value)
						}
					}}
					className="mask-input"
				/>
				: <Input
					suffix={onClose !== undefined ? <CloseCircleFilled style={{ cursor: 'pointer' }} onClick={onClose} /> : null}
					value={value}
					id={id}
					autoFocus={autoFocus}
					maxLength={maxLength}
					onChange={(e) => {
						if (onChangeInput) {
							const inputValue = e.target.value;

							// Check for spaces at the beginning or consecutive spaces at the end
							const isInvalid = /^\s|\s{2,}$/.test(inputValue);

							if (!isInvalid) {
								// For numeric inputs, ensure the value is a valid number
								if (type === 'number') {
									// Allow only numeric values
									const isNumeric = /^\d*$/.test(inputValue); // Matches only digits
									if (isNumeric) {
										onChangeInput(inputValue);
									}
								} else {
									// For strings, allow non-numeric values
									onChangeInput(inputValue);
								}
							}
						}
					}}

					placeholder={placeholder || ''}
					disabled={disabled}
				/>
		}
	</div>
};

const AppInputTagField: React.FC<AppInputFieldProps> = ({
	onChangeInput,
	label,
	info,
	id,
	placeholder,
	value = [],
	errorMessage,
	required = false,
}) => {
	const [inputValue, setInputValue] = useState<string>('');

	const handleClose = (removedTag: string) => {
		const newTags = value.filter((tag: string) => tag !== removedTag);
		if (onChangeInput) {
			onChangeInput(newTags);
		}
	};

	const handleInputConfirm = () => {
		if (inputValue && !value.includes(inputValue)) {
			const newTags = [...value, inputValue];
			if (onChangeInput) {
				onChangeInput(newTags);
			}
			setInputValue(''); // Clear the input field
		}
	};

	return (
		<div className="input-container">
			{label && <label htmlFor={id}>{label} {required && <span className="text-danger">*</span>}</label>}
			{errorMessage && <span className="error-message">({errorMessage})</span>}
			{info && <p>{info}</p>}

			<div className="d-flex align-items-center justify-center mb-2 mt-2 gap-4">
				<Input
					id={id || 'tag-input'}
					type="text"
					size="small"
					className="tag-input"
					placeholder={placeholder || 'New Tag'}
					value={inputValue}
					onChange={(e) => setInputValue(e.target.value.trim())}
					onPressEnter={handleInputConfirm}
				/>
				<Button
					type="dashed"
					icon={<PlusOutlined />}
					onClick={handleInputConfirm}
				>
          Add Tag
				</Button>
			</div>

			<div>
				{value.map((tag: string) => (
					<Tag
						key={tag}
						closable
						onClose={() => handleClose(tag)}
						style={{ marginBottom: 8 }}
					>
						{tag}
					</Tag>
				))}
			</div>
		</div>
	);
};

const AppInputSelectField: React.FC<AppInputFieldProps> = ({ onChangeInput, label, info, id, mode, options = [], value = [], errorMessage, maxCount, optionRender, required = false }) => {
	return <div className="input-container">
		{label && <label htmlFor={id}>{label} {required && <span className="text-danger">*</span>}</label>}
		{errorMessage && <span className="error-message">({errorMessage})</span>}
		{info && <p className="mb-3">{info}</p>}

		<Select
			id={id}
			mode={mode}
			maxCount={maxCount}
			allowClear={mode === 'multiple' ? false : true}
			style={{ width: '100%' }}
			value={value}
			onChange={(val) => {
				if (onChangeInput)
					onChangeInput(val)
			}}
			optionLabelProp="label"
			suffixIcon={<CaretDownFilled />}
			options={options}
			optionRender={optionRender}
			filterOption={(input, option) =>
				typeof option?.label === 'string' && option.label.toLowerCase().includes(input.toLowerCase())
			}
		/>
	</div>
};

const AppInputDebouncedSelectField: React.FC<AppInputFieldProps> = ({ onChangeInput, label, info, id, options = [], value = [], errorMessage, maxCount, onSearch, searching = false, required = false }) => {
	return <div className="input-container">
		{label && <label htmlFor={id}>{label} {required && <span className="text-danger">*</span>}</label>}
		{errorMessage && <span className="error-message">({errorMessage})</span>}
		{info && <p className="mb-3">{info}</p>}

		<Select
			id={id}
			mode={"multiple"}
			maxCount={maxCount}
			allowClear={true}
			style={{ width: '100%' }}
			value={value}
			onChange={(val) => {
				if (onChangeInput)
					onChangeInput(val)
			}}
			optionLabelProp="label"
			suffixIcon={<CaretDownFilled />}
			options={options}
			onSearch={onSearch}
			filterOption={false}
			dropdownRender={(menu) => (
				<Spin spinning={searching} size="small">
					{menu}
				</Spin>
			)}
		/>
	</div>
};

const AppInputTextField: React.FC<AppInputFieldProps> = ({ onChangeInput, label, info, id, placeholder, rows = 8, value, errorMessage, disabled, required = false, maxLength }) => {
	return <div className="input-container">
		{label && <label htmlFor={id}>{label} {required && <span className="text-danger">*</span>}</label>}
		{errorMessage && <span className="error-message">({errorMessage})</span>}
		{info && <p>{info}</p>}

		<div className="position-relative">
			<TextArea
				value={value}
				onChange={(e) => {
					if (onChangeInput) {
					// Check if there is more than 1 spaces in the end or spaces in the beginning
						const isInvalid = /^\s|\s{2,}$/.test(e.target.value);
						if (!isInvalid) onChangeInput(e.target.value);
					}
				}}
				placeholder={placeholder}
				rows={rows}
				id={id}
				disabled={disabled}
				maxLength={maxLength}
			/>

			{maxLength && (
				<span
					style={{
						position: "absolute",
						bottom: "8px",
						right: "8px",
						fontSize: "12px",
						color: "gray",
						pointerEvents: "none",
						userSelect: "none",
					}}
				>
					{value.length} / {maxLength}
				</span>
			)}
		</div>
	</div>
};

const AppInputDateField: React.FC<AppInputFieldProps> = ({ label, info, id, placeholder, showTime = false, onChangeInput, errorMessage, disabledDate, showSelectedInfo = false, value, disabled = false, disabledTime, required = false }) => {
	return <div className="input-container">
		{label && <label htmlFor={id}>{label} {required && <span className="text-danger">*</span>}</label>}
		{errorMessage && <span className="error-message">({errorMessage})</span>}
		{info && <p className="mb-2">{info}</p>}

		<DatePicker
			id={id}
			placeholder={placeholder}
			showTime={showTime}
			value={value? dayjs(value) : null}
			disabledDate={disabledDate}
			disabledTime={disabledTime}
			allowClear={false}
			format={showTime ? 'MMM D, YYYY h:mm A' : 'MMM D, YYYY'}
			disabled={disabled}
			onChange={(date) => {
				if (onChangeInput)
					onChangeInput(date);
			}}
		/>

		{value && showSelectedInfo ? (
			<div className="mt-2">
				{`${dayjs(value).format(
					showTime ? 'dddd, MMMM D, YYYY h:mm A' : 'dddd, MMMM D, YYYY'
				)} (i.e. ${dayjs(value).fromNow()})`}
			</div>
		) : null}
	</div>
};

const AppInputTimeField: React.FC<AppInputFieldProps> = ({ label, info, id, placeholder, onChangeInput, errorMessage, value, required = false }) => {
	return <div className="input-container">
		{label && <label htmlFor={id}>{label} {required && <span className="text-danger">*</span>}</label>}
		{errorMessage && <span className="error-message">({errorMessage})</span>}
		{info && <p className="mb-2">{info}</p>}

		<TimePicker
			id={id}
			format={'h:mm A'}
			placeholder={placeholder}
			value={value? dayjs(value) : null}
			allowClear={false}
			onChange={(date) => {
				if (onChangeInput)
					onChangeInput(date);
			}}
		/>
	</div>
};

const AppInputRadioField: React.FC<AppInputFieldProps> = ({ id, onChangeInput, value, radioOptions = [], radioOptionType = 'default', label, info, errorMessage, required = false }) => {
	return <div className={`${label ? 'input-container' : ''}`}>
		{label && <label htmlFor={id}>{label} {required && <span className="text-danger">*</span>}</label>}
		{errorMessage && <span className="error-message">({errorMessage})</span>}
		{info && <p>{info}</p>}

		<Radio.Group
			id={id}
			onChange={(e) => {
				if (onChangeInput)
					onChangeInput(e.target.value)
			}}
			options={radioOptions}
			value={value}
			size="large"
			optionType={radioOptionType}
		/>
	</div>
};

export {
	AppInputField,
	AppInputSelectField,
	AppInputTextField,
	AppInputDateField,
	AppInputRadioField,
	AppInputTagField,
	AppInputTimeField,
	AppInputDebouncedSelectField
};
