'use client'

import { Modal } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../store";
import { appStateChange } from "@/store/slices/app.slice";
import LoginForm from "../Layouts/Partials/app/auth/LoginForm";

const LoginModal = () => {
	const { showLoginModal } = useSelector((state: RootState) => state.app);
	const dispatch = useDispatch<AppDispatch>();

	return (
		<div>
			<Modal
				open={showLoginModal}
				footer={false}
				width={600}
				onCancel={() => dispatch(appStateChange({ key: 'showLoginModal', value: false }))}
				centered
				maskClosable={false}
			>
				<LoginForm />
			</Modal>
		</div>
	)
}
export default LoginModal;
