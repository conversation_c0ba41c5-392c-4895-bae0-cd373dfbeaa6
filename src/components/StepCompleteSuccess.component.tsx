import { But<PERSON>, Divider } from "antd";
import SuccessSVG from "./common/SuccessSVG";

interface StepCompleteSuccessProps {
  title: string;
  message: React.ReactNode;
  actionText: string;
	onAction?: () => void;
	supportEmail?: string;
	supportPhone?: string;
}

const StepCompleteSuccess: React.FC<StepCompleteSuccessProps> = ({ title, message, actionText, onAction, supportEmail, supportPhone }) => {
	return (
		<div className={'container step_progress_complete'}>
			<h1>{title}</h1>
			<p>{message}</p>
			<div className={'success_svg'}><SuccessSVG /></div>

			<div className="preview_act_buton">
				<Button type={'primary'} ghost onClick={onAction}>{actionText}</Button>
			</div>
			<br />
			<br />
			<br />
			<Divider />
			<p style={{ marginTop: 10, fontSize: 18, color: '#ccc' }}>
				If you have any enquiries please feel free to contact us at <a
					href={"mailto:" + (supportEmail || '<EMAIL>')}
					target="_blank"
					rel="noopener noreferrer"
					style={{ color: '#c71782' }}
				>
					{supportEmail || '<EMAIL>'}
				</a>
				{supportPhone && (
					<> or give us a call at <a
						href={`tel:${supportPhone}`}
						style={{ color: '#c71782' }}
					>{supportPhone}</a>
					</>
				)}
			</p>

		</div>
	)
}

export default StepCompleteSuccess
