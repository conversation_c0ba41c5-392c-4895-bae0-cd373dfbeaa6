'use client';

import { <PERSON><PERSON>, Modal } from 'antd';
import FullScreenModal from '../FullScreenModal.component'
import { Formik } from 'formik';
import { AppDispatch, RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { ServerStatus } from '@/types/index.types';
import { AppInputDateField, AppInputField, AppInputTagField, AppInputTextField } from '../common/AppInputField.component';
import { useEffect, useState } from 'react';
import { ArtistModel } from '@/types/models/artist.model';
import { AddArtistType } from '@/types/forms/add-artist.type';
import { artistStateChange, createArtistAction, updateArtistAction } from '@/store/slices/artist.slice';
import dayjs from 'dayjs';
import { CloseCircleFilled } from '@ant-design/icons';
import ImageCropper from '../ImageCropper.component';
import { addArtistValidation } from '@/utils/validation.util';
import { GeneralInfoModal } from '@/types/models/general-info.model';
import { fetchGeneralInfoAction } from '@/store/slices/general-info.slice';
import { getFullImageUrl } from '@/utils/general.util';

const AddEditArtist = ({ showModal, setShowModal } : {showModal: boolean, setShowModal: React.Dispatch<React.SetStateAction<boolean>>}) => {
	const dispatch = useDispatch<AppDispatch>();

	const [showCloseModal, setShowCloseModal] = useState<boolean>(false);

	const { record, status } = useSelector((state: RootState) => state.artist) as { record: ArtistModel | null, status: ServerStatus };
	const { record: generalInfo } = useSelector((state: RootState) => state.generalInfo) as { record: GeneralInfoModal };

	useEffect(() => {
		if (showModal) {
			dispatch(fetchGeneralInfoAction());
		}
	}, [dispatch, showModal])

	return (
		<Formik<AddArtistType>
			key={record?.id}
			initialValues={{
				name: record?.name || '',
				title: record?.title || '',
				stage_name: record?.stage_name || '',
				band_name: record?.band_name || '',
				image: record?.image || '',
				dob: record?.dob || '',
				home_town: record?.home_town || '',
				about: record?.about || '',
				meta_desc: record?.meta_desc || '',
				meta_keywords: record?.meta_keywords ? record.meta_keywords.split(',') : [],
			}}
			validate={(data) => {
				return addArtistValidation(data);
			}}
			onSubmit={async (values, actions) => {
				const res = record ?
					await dispatch(updateArtistAction<AddArtistType>(record.id, values))
					: await dispatch(createArtistAction<AddArtistType>(values));
				if (res) {
					if (res.status === 201 || (record && res.status === 200)) {
						actions.resetForm();
						setShowModal(false)
					}
					if (record) {
						window.history.replaceState(
							null,
							'',
							'/user/dashboard/artists'
						);
						dispatch(artistStateChange({ key: 'record', value: null }));
					}
				}
			}}
		>
			{({ values, errors, handleSubmit, setFieldValue, resetForm, isValid, isValidating, dirty }) => (
				<>
					<FullScreenModal
						className={'dashboard_add_form'}
						onClose={() => {
							if (dirty) setShowCloseModal(true);
							else {
								resetForm();
								setShowModal(false);
								if (record) {
									window.history.replaceState(
										null,
										'',
										'/user/dashboard/artists'
									);
									dispatch(artistStateChange({ key: 'record', value: null }));
								}
							}
						}}
						show={showModal}
					>
						<div className="modal-body-container-form">
							<div className="add_form_header">
								<div className="container">
									<div className="d-flex justify-content-between align-items-center">
										<div>
											<h1>Add Artist</h1>
										</div>
									</div>
								</div>
							</div>

							<form>
								<div className="add_form_container">
									<div className="container">
										<div className="row">
											<ImageCropper
												aspectRatio={1}
												value={values.image as string}
												renderUploadTrigger={(openUpload) => (
													<div className="col-md-12">
														<div onClick={openUpload} className="drag_drp_cover">
															<div>
																<h3><i className="fas fa-images" /></h3>
																<p>Click to upload artist's image.</p>
															</div>
														</div>
													</div>
												)}
												renderPreview={(resetImage) => (
													<div className="col-md-12">
														<div className="input-container">
															<div className="image-upload-preview">
																<div className="image-preview">
																	<img src={getFullImageUrl(values.image || '')} alt="" />
																</div>
																<div className="image-info">
																	<h1>Image of the artist</h1>
																</div>
																<div className="image-action">
																	{/* <a href="" onClick={(e) => e.preventDefault()}>
																		<FileImageFilled />
																	</a> */}
																	<a href="" onClick={(e) => {
																		e.preventDefault();
																		resetImage();
																	}}>
																		<CloseCircleFilled />
																	</a>
																</div>
															</div>
														</div>
													</div>
												)}
												onCropComplete={(croppedImageUrl) => {
													setFieldValue('image', croppedImageUrl.split('/').at(-1))
												}}
											/>

											<div className="col-md-6">
												<AppInputField
													id="artist_name"
													required={true}
													value={values.name}
													label={'Artist Name'}
													info={'Name of the artist'}
													onChangeInput={(value: string) => {
														setFieldValue('name', value);
													}}
													placeholder={'James Bond'}
													errorMessage={errors.name}
												/>
											</div>

											<div className="col-md-6">
												<AppInputField
													id="artist_title"
													required={true}
													value={values.title}
													onChangeInput={(value: string) => {
														setFieldValue('title', value);
													}}
													label={'Artist Title'}
													info={'Title of the artist (eg: Singer, Actor, etc.)'}
													placeholder={'Singer'}
													errorMessage={errors.title}
												/>
											</div>

											<div className="col-md-6">
												<AppInputField
													id="artist_stage_name"
													value={values.stage_name}
													onChangeInput={(value: string) => {
														setFieldValue('stage_name', value);
													}}
													label={'Artist Stage Name'}
													info={'Stage name of the artist (eg: Laure, Yama Buddha, etc.)'}
													placeholder={'Laure'}
													errorMessage={errors.stage_name}
												/>
											</div>

											<div className="col-md-6">
												<AppInputField
													id="artist_band_name"
													value={values.band_name}
													onChangeInput={(value: string) => {
														setFieldValue('band_name', value);
													}}
													label={'Artist Band Name'}
													info={'Band name of the artist (eg: Albatross, 1974 AD, etc.)'}
													placeholder={'Albatross'}
													errorMessage={errors.band_name}
												/>
											</div>

											<div className="col-md-6">
												<AppInputDateField
													id={'artist_dob'}
													showTime={false}
													value={values.dob}
													onChangeInput={(date: Date) => {
														setFieldValue('dob', date);
													}}
													placeholder={'Select DOB'}
													label="Date of Birth"
													info="Select date of birth of the artist"
													disabledDate={(current: dayjs.Dayjs) => current && current.isAfter(dayjs().startOf('day'))}
													errorMessage={errors.dob}
												/>
											</div>

											<div className="col-md-6">
												<AppInputField
													id="artist_home_town"
													value={values.home_town}
													onChangeInput={(value: string) => {
														setFieldValue('home_town', value);
													}}
													label={'Home Town'}
													info={'Home Town of the artist'}
													placeholder={'Kathmandu, Nepal'}
													errorMessage={errors.home_town}
												/>
											</div>

											<div className="col-md-12">
												<AppInputTextField
													id="about_artist"
													required={true}
													value={values.about}
													onChangeInput={(value: string) => {
														setFieldValue('about', value);
													}}
													label={'About the Artist'}
													placeholder={'Description about the artist ....'}
													rows={6}
													info={'Short info about your the artist (minimum 30 characters / max 500 characters)'}
													errorMessage={errors.about}
													maxLength={500}
												/>
											</div>

											<div className='col-md-12'>
												<AppInputTagField
													label='Meta Keywords'
													id='meta_keywords'
													value={values.meta_keywords}
													onChangeInput={(value: string[]) => {
														setFieldValue('meta_keywords', value);
													}}
													placeholder={'New Keyword'}
													info={'Keywords for the artist for SEO optimization (minimum 3 keywords)'}
													errorMessage={errors.meta_keywords as string}
												/>
											</div>

											<div className="col-md-12">
												<AppInputTextField
													id="meta_description"
													value={values.meta_desc}
													onChangeInput={(value: string) => {
														setFieldValue('meta_desc', value);
													}}
													label={'Meta Description'}
													placeholder={'Meta Description for the artist ....'}
													rows={6}
													info={'Short meta description about the artist for SEO optimization (minimum 50 characters / max 500 characters)'}
													errorMessage={errors.meta_desc}
													maxLength={500}
												/>
											</div>
										</div>
									</div>
								</div>
							</form>

							<div className="add_form_footer">
								<div className="container-fluid">
									<div className="row">
										<div className="col-md-12">
											<div className="d-flex justify-content-between">
												<p style={{ color: '#999' }}>
													<small>Need assistance? Contact us at <a
														href={"mailto:" + (generalInfo?.email_one || '<EMAIL>')}
														target="_blank"
														rel="noopener noreferrer">{generalInfo?.email_one || '<EMAIL>'}</a>
													</small>
												</p>
												<div>
													<a href=""
														onClick={(e) => {
															e.preventDefault();
															if (dirty) setShowCloseModal(true);
															else {
																resetForm();
																setShowModal(false);
																if (record) {
																	window.history.replaceState(
																		null,
																		'',
																		'/user/dashboard/artists'
																	);
																	dispatch(artistStateChange({ key: 'record', value: null }));
																}
															}
														}}
														className='me-3'
														style={{ pointerEvents: status === 'saving' ? 'none' : 'auto', opacity: status === 'saving' ? 0.5 : 1 }}
													>
														Cancel
													</a>
													<Button
														loading={status === 'saving'}
														size={'large'}
														type={'primary'}
														disabled={!isValid || status === 'saving' || isValidating || !dirty}
														onClick={() => handleSubmit()}
													>
														{record ? 'Update' : 'Save'} Artist
													</Button>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</FullScreenModal>
					<Modal
						title={'Are you sure?'}
						open={showCloseModal}
						onOk={() => {
							resetForm();
							setShowCloseModal(false);
							setShowModal(false);
							if (record) {
								window.history.replaceState(
									null,
									'',
									'/user/dashboard/artists'
								);
								dispatch(artistStateChange({ key: 'record', value: null }));
							}
						}}
						onCancel={() => setShowCloseModal(false)}
						okText={'Quit'}
						closable={false}
					>
						<div>
							Are you sure you want to quit? Your data has not been saved.
						</div>
					</Modal>
				</>
			)}
		</Formik>
	)
}

export default AddEditArtist
