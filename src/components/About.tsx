'use client';

import React from "react";
import { FaAward, FaUsers, FaRocket, FaShieldAlt, FaClock, FaHandshake } from 'react-icons/fa';

const About = () => {
	return (
		<div className="about-container">
			<section className="about-hero">
				<h1 className="about-title">
					Innovating Digital Experiences
				</h1>
				<p className="about-subtitle">
					We're a passionate team dedicated to crafting exceptional digital solutions
					that transform businesses and delight users.
				</p>
			</section>

			<section className="about-mission">
				<div className="mission-content">
					<h2>Our Mission</h2>
					<p>
						At eTicketNepal, we believe in the power of technology to transform how
						people experience events and entertainment. We're committed to creating
						seamless, secure, and innovative solutions that bring communities together
						and make ticketing accessible to everyone.
					</p>
				</div>
			</section>

			<section className="about-history">
				<div className="history-content">
					<h2>Our Journey</h2>
					<div className="timeline">
						{[
							{
								year: "2020",
								title: "The Beginning",
								description: "Founded with a vision to revolutionize digital ticketing in Nepal."
							},
							{
								year: "2021",
								title: "Rapid Growth",
								description: "Expanded our platform to serve over 100 event organizers across the country."
							},
							{
								year: "2022",
								title: "Innovation Award",
								description: "Recognized as the most innovative ticketing platform in South Asia."
							},
							{
								year: "2023",
								title: "Going Global",
								description: "Launched international operations and partnerships across multiple countries."
							}
						].map((milestone) => (
							<div
								key={milestone.year}
								className="milestone"
							>
								<div className="year">{milestone.year}</div>
								<h3>{milestone.title}</h3>
								<p>{milestone.description}</p>
							</div>
						))}
					</div>
				</div>
			</section>

			<section className="about-achievements">
				<div className="achievements-header">
					<h2>Our Achievements</h2>
					<p>Milestones that mark our commitment to excellence and innovation</p>
				</div>
				<div className="achievements-grid">
					{[
						{
							number: "1M+",
							title: "Tickets Sold",
							description: "Successfully processed over a million ticket transactions"
						},
						{
							number: "500+",
							title: "Events",
							description: "Powered hundreds of successful events nationwide"
						},
						{
							number: "99.9%",
							title: "Uptime",
							description: "Maintaining reliable service for our users"
						},
						{
							number: "50K+",
							title: "Happy Users",
							description: "Growing community of satisfied customers"
						}
					].map((achievement) => (
						<div
							key={achievement.title}
							className="achievement-card"
						>
							<div className="number">{achievement.number}</div>
							<h3>{achievement.title}</h3>
							<p>{achievement.description}</p>
						</div>
					))}
				</div>
			</section>

			<section className="why-choose-us">
				<div className="choose-us-header">
					<h2>Why Choose Us</h2>
					<p>Experience the difference of working with a team that puts your success first</p>
				</div>
				<div className="features-grid">
					{[
						{
							icon: <FaRocket />,
							title: "Fast & Efficient",
							description: "Lightning-fast ticket processing and seamless user experience"
						},
						{
							icon: <FaShieldAlt />,
							title: "Secure Platform",
							description: "Bank-grade security for all transactions and user data"
						},
						{
							icon: <FaUsers />,
							title: "Customer Focus",
							description: "24/7 support and dedicated account management"
						},
						{
							icon: <FaHandshake />,
							title: "Trusted Partner",
							description: "Long-term relationships with leading event organizers"
						},
						{
							icon: <FaClock />,
							title: "Real-time Updates",
							description: "Instant notifications and live event analytics"
						},
						{
							icon: <FaAward />,
							title: "Industry Leader",
							description: "Award-winning platform with proven success"
						}
					].map((feature) => (
						<div
							key={feature.title}
							className="feature-card"
						>
							<div className="icon">{feature.icon}</div>
							<h3>{feature.title}</h3>
							<p>{feature.description}</p>
						</div>
					))}
				</div>
			</section>

			<section className="about-values">
				<div className="values-grid">
					{[
						{
							title: "Innovation",
							description: "We constantly push boundaries and embrace cutting-edge technologies to deliver solutions that set new industry standards."
						},
						{
							title: "Quality",
							description: "Every line of code, every pixel, and every interaction is crafted with meticulous attention to detail and excellence."
						},
						{
							title: "Collaboration",
							description: "We believe in the power of teamwork and partner with our clients to create solutions that exceed expectations."
						},
						{
							title: "Impact",
							description: "Our work goes beyond code – we're dedicated to creating meaningful change in the digital landscape."
						}
					].map((value) => (
						<div
							key={value.title}
							className="value-card"
						>
							<h3>{value.title}</h3>
							<p>{value.description}</p>
						</div>
					))}
				</div>
			</section>

			{/* <section className="about-team">
				<motion.div {...fadeIn} className="team-section-header">
					<h2>Meet Our Team</h2>
					<p>
						Talented individuals united by a passion for innovation and excellence in
						digital solutions.
					</p>
				</motion.div>
				<div className="team-grid">
					{[
						{
							name: "Sarah Johnson",
							role: "CEO",
							image: "/images/user_avatar.jpg",
							social: {
								linkedin: "#",
								twitter: "#",
								github: "#"
							}
						},
						{
							name: "Michael Chen",
							role: "CTO",
							image: "/images/user_avatar.jpg",
							social: {
								linkedin: "#",
								twitter: "#",
								github: "#"
							}
						},
						{
							name: "Emma Davis",
							role: "Design Lead",
							image: "/images/user_avatar.jpg",
							social: {
								linkedin: "#",
								twitter: "#",
								github: "#"
							}
						},
						{
							name: "James Wilson",
							role: "Product Manager",
							image: "/images/user_avatar.jpg",
							social: {
								linkedin: "#",
								twitter: "#",
								github: "#"
							}
						}
					].map((member, index) => (
						<motion.div
							key={member.name}
							className="team-member"
							initial={{ opacity: 0, scale: 0.9 }}
							animate={{ opacity: 1, scale: 1 }}
							transition={{ delay: index * 0.2 }}
						>
							<div className="member-image-container">
								<Image
									src={member.image}
									alt={member.name}
									width={180}
									height={180}
									className="member-image"
								/>
							</div>
							<h3>{member.name}</h3>
							<p>{member.role}</p>
							<div className="social-links">
								<a href={member.social.linkedin} target="_blank" rel="noopener noreferrer">
									<FaLinkedin size={20} />
								</a>
								<a href={member.social.twitter} target="_blank" rel="noopener noreferrer">
									<FaTwitter size={20} />
								</a>
								<a href={member.social.github} target="_blank" rel="noopener noreferrer">
									<FaGithub size={20} />
								</a>
							</div>
						</motion.div>
					))}
				</div>
			</section> */}

			{/* <section className="about-contact">
				<motion.div {...fadeInUp} className="contact-content">
					<h2>Let's Connect</h2>
					<p>
						Ready to transform your digital experience? We're here to help bring your
						vision to life.
					</p>
					<button className="contact-button">Get in Touch</button>
				</motion.div>
			</section> */}
		</div>
	);
};

export default About;
