'use client';

import { But<PERSON>, Checkbox, Input } from "antd";
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { Formik } from "formik";
import { userSignupValidation } from "@/utils/validation.util";
import { UserSignupType } from "@/types/forms/user-signup.type";
import { registerUserAction } from "@/store/slices/user.slice";
import { AppDispatch, RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import UserRegistrationSuccess from "@/components/UserRegistrationSuccess";
import { useState } from "react";
import Link from "next/link";

const SignupForm = () => {
	const dispatch = useDispatch<AppDispatch>();
	const { registerSuccess, status } = useSelector((state: RootState) => state.user);
	const [regCredential, setRegCredential] = useState<string>('');
	return (
		<>
			{registerSuccess ? <UserRegistrationSuccess regCredential={regCredential} /> :
				<div className="login-input-box">
					<div><h1>Create your login info.</h1>
						<p>
							Please create your login credentials. Your email will be used for account access and important notifications.
							<i className="fa fa-info-circle" title="Your email will be your login ID and used for account-related communication." />
						</p>

						<Formik<UserSignupType>
							enableReinitialize={true}
							initialValues={{
								credential: '',
								password: '',
								password_confirmation: '',
								agreement: false,
							}}
							validate={userSignupValidation}
							onSubmit={async (values, actions) => {
								const res = await dispatch(registerUserAction<UserSignupType>(values));
								if (res && res.status === 201) {
									setRegCredential(values.credential);
									actions.resetForm();
								}
							}}
						>
							{({ values, setFieldValue, isValid, errors, handleSubmit }) => (
								<form onSubmit={handleSubmit} autoComplete="off">
									<Input
										placeholder="Sign up with email"
										prefix={<UserOutlined />}
										value={values.credential}
										onChange={(e) => setFieldValue('credential', e.target.value)}
									/>
									{errors.credential && <div className="text-danger mt-2">{errors.credential}</div>}
									<br/>
									<br/>
									<br/>
									<Input.Password
										type={'password'}
										placeholder="Password"
										prefix={<LockOutlined />}
										value={values.password}
										onChange={(e) => setFieldValue('password', e.target.value)}
									/>
									{errors.password && <div className="text-danger mt-2">{errors.password}</div>}
									<br/>
									<br/>
									<br/>
									<Input.Password
										type={'password'}
										placeholder="Confirm password"
										prefix={<LockOutlined />}
										value={values.password_confirmation}
										onChange={(e) => setFieldValue('password_confirmation', e.target.value)}
									/>
									{errors.password_confirmation && <div className="text-danger mt-2">{errors.password_confirmation}</div>}
									<br/>
									<br/>
									<p>
										<Checkbox
											checked={values.agreement}
											onChange={(e) => setFieldValue('agreement', e.target.checked)}
										>
											I have read and agree with the <a href="/terms" target={'_blank'}><u>Terms and conditions.</u></a>
										</Checkbox>
									</p>
									<br/>

									<Button
										block
										loading={status === 'saving'}
										size="large"
										type={'primary'}
										htmlType={'submit'}
										disabled={!isValid || !values.agreement}
									>
										Sign up
									</Button>
								</form>
							)}
						</Formik>
						<br />
						<div className="login-extra-option">
							<div className="more-links">
								<p>Already have an account? <Link href={'/user/signin'}><span >Sign in.</span></Link>
								</p>
							</div>
						</div>
					</div>
				</div>
			}
		</>
	);
}

export default SignupForm;
