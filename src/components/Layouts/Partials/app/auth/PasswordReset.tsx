'use client'

import { AppDispatch, RootState } from "@/store";
import { forgotPasswordAction, resetPasswordAction } from "@/store/slices/user.slice";
import { isEmail } from "@/utils/general.util";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { Button, Input, message } from "antd";
import { Formik } from "formik";
import { useRouter } from "next-nprogress-bar";
import Link from "next/link";
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

export default function ResetPassword() {
	const dispatch = useDispatch<AppDispatch>();
	const { status } = useSelector((state: RootState) => state.user);
	const searchParam = useSearchParams();
	const router = useRouter();
	const email = searchParam.get('email');
	const code = searchParam.get('code');
	const [emailSentSuccess, setEmailSentSuccess] = useState<boolean>(false);
	const [countdown, setCountdown] = useState<number>(0);
	const [passwordResetSuccess, setPasswordResetSuccess] = useState<boolean>(false);

	useEffect(() => {
		let timer: NodeJS.Timeout;
		if (countdown > 0) {
			timer = setTimeout(() => setCountdown(countdown - 1), 1000);
		}
		return () => clearTimeout(timer);
	}, [countdown]);

	return (
		<div className="login-container" style={{ padding: '80px 15px' }}>
			<div className="login-input-box">
				{email && code ? (
					<div>
						<h1>New password!</h1>

						<p>
							Looks like you forgot your password. Here you can set up your new password to login to your
              eticketnepal user's dashboard.<i className="fa fa-info-circle"/>
						</p>

						<Formik
							initialValues={{
								password: '',
								password_confirmation: ''
							}}
							validate={(values) => {
								const errors = {} as { password: string, password_confirmation: string };
								if (!values.password) errors.password = 'Please enter your password';
								else if (values.password.length < 6) errors.password = 'Password must be at least 6 characters';
								if (!values.password_confirmation) errors.password_confirmation = 'Please confirm your password';
								else if (values.password_confirmation !== values.password) errors.password_confirmation = 'Passwords do not match';
								return errors;
							}}
							onSubmit={async (values) => {
								if (!email || !code) {
									message.error('Email or code is missing. Please try again');
									return;
								}
								const res = await dispatch(resetPasswordAction(email, code, values.password, values.password_confirmation));
								if (res && res.status === 200) {
									setPasswordResetSuccess(true);
									router.push('/user/signin');
								}
							}}
						>
							{({ values, setFieldValue, isValid, handleSubmit, errors, isValidating }) => (
								<form onSubmit={handleSubmit}>
									<div className="mb-5">
										<Input.Password
											type={'password'}
											placeholder="New password"
											prefix={<LockOutlined />}
											value={values.password}
											onChange={(e) => setFieldValue('password', e.target.value)}
										/>
										{errors.password && <div className="text-danger mt-2">{errors.password}</div>}
									</div>

									<div className="mb-4">
										<Input.Password
											type={'password'}
											placeholder="Confirm new password"
											prefix={<LockOutlined />}
											value={values.password_confirmation}
											onChange={(e) => setFieldValue('password_confirmation', e.target.value)}
										/>
										{errors.password_confirmation && <div className="text-danger mt-2">{errors.password_confirmation}</div>}
									</div>

									<Button
										block
										htmlType="submit"
										type="primary"
										size="large"
										disabled={!isValid || isValidating}
										loading={status === 'saving' || passwordResetSuccess}
									>
										{passwordResetSuccess ? 'Redirecting to Sign in...' : 'Reset'}
									</Button>
								</form>
							)}
						</Formik>

						<div className="login-extra-option">
							<div className="more-links">
								<Link href={'/user/signin'}>Sign in</Link>
							</div>
						</div>
					</div>
				) : (
					<Formik
						initialValues={{
							email: '',
						}}
						validate={(values) => {
							const errors = {} as { email: string };
							if (!values.email) errors.email = 'Please enter your email';
							else if (!isEmail(values.email)) errors.email = 'Please enter a valid email address';
							return errors;
						}}
						onSubmit={async (values) => {
							const res = await dispatch(forgotPasswordAction(values.email));
							if (res && res.status === 200) {
								setEmailSentSuccess(true);
								setCountdown(60);
							}
						}}
					>
						{({ values, setFieldValue, isValid, handleSubmit, errors, isValidating }) => (
							emailSentSuccess ? (
								<div>
									<h1>Verification email sent!!</h1>
									<p style={{ fontSize: 14, fontWeight: 300 }}>
										A link has been sent to <b style={{ fontWeight: 500 }}>{values.email}</b>.
										Please visit your email account and click the link to reset your password.{' '}
										<strong style={{ fontWeight: 500 }}>
											Please also confirm if the email has been delivered to your spam, <br/> or you can{' '}
											{countdown > 0 ? (
												<span style={{ color: '#c71782' }}>Try again in {countdown}s</span>
											) : (
												<>
													<a href="" onClick={(e) => {
														e.preventDefault();
														if (status === 'waiting') return;
														handleSubmit();
													}}>
														{status === 'waiting' ? 'Please wait...' : 'click here'}{' '}
													</a>
													to try again.
												</>
											)}
										</strong>
									</p>
								</div>
							) : (
								<div>
									<h1>No worry, We got you covered.</h1>

									<p>
										Please enter your email address for which you would like to reset password. A password reset link
										would be sent to your email address.<i className="fa fa-info-circle"/>
									</p>
									<form onSubmit={handleSubmit}>
										<div className="mb-4">
											<Input
												placeholder="Enter your email"
												prefix={<UserOutlined />}
												value={values.email}
												onChange={(e) => setFieldValue('email', e.target.value)}
											/>
											{errors.email && <div className="text-danger mt-2">{errors.email}</div>}
										</div>

										<Button
											block
											htmlType="submit"
											type="primary"
											size="large"
											disabled={!isValid || isValidating}
											loading={status === 'waiting'}
										>
											Send Reset Link
										</Button>
									</form>

									<div className="login-extra-option">
										<div className="more-links">
											<Link href={'/user/signin'}>Sign in</Link>
										</div>
									</div>
								</div>
							)
						)}
					</Formik>
				)}
			</div>
		</div>
	)
}
