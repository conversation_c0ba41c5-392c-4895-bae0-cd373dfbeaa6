// 'use client';

// import { AppDispatch, RootState } from "@/store";
// import { LoadingOutlined } from "@ant-design/icons";
// import { Button } from "antd";
// import { useEffect, useRef, useState } from "react";
// import { useDispatch, useSelector } from "react-redux";
// import { useSearchParams } from 'next/navigation';
// import { resendVerificationEmailAction, verifyUserAction } from "@/store/slices/user.slice";
// import { UserModel } from "@/types/models/user.model";
// import Link from "next/link";

// const EmailVerification = () => {
// 	const dispatch = useDispatch<AppDispatch>();
// 	const { status } = useSelector((state: RootState) => state.user);
// 	const [title, setTitle] = useState<string>('Email Verification in Progress.');
// 	const [message, setMessage] = useState<string>('Verifying your email. Please wait...');
// 	const [success, setSuccess] = useState<boolean>();
// 	const searchParams = useSearchParams();
// 	const hasRequestedRef = useRef(false);
// 	const [countdown, setCountdown] = useState<number>(0);

// 	useEffect(() => {
// 		let timer: NodeJS.Timeout;
// 		if (countdown > 0) {
// 			timer = setTimeout(() => setCountdown(countdown - 1), 1000);
// 		}
// 		return () => clearTimeout(timer);
// 	}, [countdown]);

// 	useEffect(() => {
// 		if (hasRequestedRef.current) return;
// 		const email = searchParams.get('email');
// 		const code = searchParams.get('code');

// 		verifyEmail(email, code);
// 		hasRequestedRef.current = true;
// 	// eslint-disable-next-line react-hooks/exhaustive-deps
// 	}, []);

// 	const verifyEmail = async (email: string | null, code: string | null) => {
// 		if (!email || !code) {
// 			setMessage('Required information is missing. Please try again with new link.');
// 			setSuccess(false);
// 			return;
// 		}

// 		const res = await dispatch(verifyUserAction<Pick<UserModel, 'email' | 'verification_code'>>({ email, verification_code: code }));
// 		if (res && res.status === 200) {
// 			setTitle('Email Verification Successful.');
// 			setMessage('Email verification was successful. You can sign in now.');
// 			setSuccess(true);
// 		} else {
// 			setTitle('Email Verification Failed.');
// 			setMessage('Something went wrong during email verification. Please try again.');
// 			setSuccess(false);
// 		}
// 	};

// 	const handleResendEmail = async () => {
// 		const email = searchParams.get('email');
// 		if (!email) return;
// 		setMessage('Resending verification email. Please wait...');
// 		setCountdown(60);
// 		const res = await dispatch(resendVerificationEmailAction<Pick<UserModel, 'email'>>({ email }));
// 		if (res && res.status === 200) setMessage('Verification email was sent successfully. Please check your inbox for the verification email.');
// 		else setMessage('Something went wrong during sending verification email. Please try again.');
// 	};

// 	return (
// 		<div className="login-container" style={{ padding: '80px 15px' }}>
// 			<div className="login-input-box">
// 				<h1>{title}</h1>
// 				<br/>
// 				<h2 style={{ fontWeight: 600 }}>{message} {status === 'saving' && <LoadingOutlined />}</h2>
// 				<br/>

// 				{success && <Link href={'/user/signin'}><Button type="primary">Sign in</Button></Link>}

// 				{typeof(success) === 'boolean' && !success && (
// 					<div className="more-links">
// 						{countdown > 0 ? (
// 							<span>Try again in {countdown}s</span>
// 						) : (
// 							<Link href={''} onClick={handleResendEmail}>Resend Verification Email</Link>
// 						)}
// 					</div>
// 				)}
// 			</div>
// 		</div>
// 	)
// }

// export default EmailVerification;

'use client';

import { AppDispatch, RootState } from "@/store";
import { LoadingOutlined, UserOutlined } from "@ant-design/icons";
import { Button, Input } from "antd";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from 'next/navigation';
import { resendVerificationEmailAction, verifyUserAction } from "@/store/slices/user.slice";
import { UserModel } from "@/types/models/user.model";
import Link from "next/link";
import { Formik } from "formik";
import { isEmail } from "@/utils/general.util";

const EmailVerification = () => {
	const dispatch = useDispatch<AppDispatch>();
	const { status } = useSelector((state: RootState) => state.user);
	const [title, setTitle] = useState<string>('Email Verification in Progress.');
	const [message, setMessage] = useState<string>('Verifying your email. Please wait...');
	const [success, setSuccess] = useState<boolean | undefined>();
	const searchParams = useSearchParams();
	const [countdown, setCountdown] = useState<number>(0);
	const [emailSentSuccess, setEmailSentSuccess] = useState<boolean>(false);

	const email = searchParams.get('email');
	const code = searchParams.get('code');

	useEffect(() => {
		let timer: NodeJS.Timeout;
		if (countdown > 0) {
			timer = setTimeout(() => setCountdown(countdown - 1), 1000);
		}
		return () => clearTimeout(timer);
	}, [countdown]);

	useEffect(() => {
		if (!email || !code) return;

		verifyEmail(email, code);
	}, [email, code]);

	const verifyEmail = async (email: string | null, code: string | null) => {
		if (!email || !code) {
			setMessage('Required information is missing. Please provide your email address.');
			setSuccess(false);
			return;
		}

		const res = await dispatch(verifyUserAction<Pick<UserModel, 'email' | 'verification_code'>>({ email, verification_code: code }));
		if (res && res.status === 200) {
			setTitle('Email Verification Successful.');
			setMessage('Email verification was successful. You can sign in now.');
			setSuccess(true);
		} else {
			setTitle('Email Verification Failed.');
			setMessage('Something went wrong during email verification. Please try again.');
			setSuccess(false);
		}
	};

	const handleResendEmail = async (emailAddress: string) => {
		if (!emailAddress) return;
		setMessage('Sending verification email. Please wait...');
		setCountdown(60);
		const res = await dispatch(resendVerificationEmailAction<Pick<UserModel, 'email'>>({ email: emailAddress }));
		if (res && res.status === 200) {
			setMessage('Verification email was sent successfully. Please check your inbox for the verification email.');
			setEmailSentSuccess(true);
		} else {
			setMessage('Something went wrong during sending verification email. Please try again.');
		}
	};

	return (
		<div className="login-container" style={{ padding: '80px 15px' }}>
			<div className="login-input-box">
				{email && code ? (
					<div>
						<h1>{title}</h1>
						<br/>
						<h2 style={{ fontWeight: 600 }}>{message} {status === 'saving' && <LoadingOutlined />}</h2>
						<br/>

						{success && <Link href={'/user/signin'}><Button type="primary">Sign in</Button></Link>}

						{typeof(success) === 'boolean' && !success && (
							<div className="more-links">
								{countdown > 0 ? (
									<span>Try again in {countdown}s</span>
								) : (
									<Link href={''} onClick={() => handleResendEmail(email)}>Resend Verification Email</Link>
								)}
							</div>
						)}
					</div>
				) : (
					<Formik
						initialValues={{
							email: '',
						}}
						validate={(values) => {
							const errors = {} as { email: string };
							if (!values.email) errors.email = 'Please enter your email';
							else if (!isEmail(values.email)) errors.email = 'Please enter a valid email address';
							return errors;
						}}
						onSubmit={async (values) => {
							await handleResendEmail(values.email);
						}}
					>
						{({ values, setFieldValue, isValid, handleSubmit, errors, isValidating }) => (
							emailSentSuccess ? (
								<div>
									<h1>Verification email sent!!</h1>
									<p style={{ fontSize: 14, fontWeight: 300 }}>
										A verification link has been sent to <b style={{ fontWeight: 500 }}>{values.email}</b>.
										Please visit your email account and click the link to verify your email.{' '}
										<strong style={{ fontWeight: 500 }}>
											Please also confirm if the email has been delivered to your spam, <br/> or you can{' '}
											{countdown > 0 ? (
												<span style={{ color: '#c71782' }}>Try again in {countdown}s</span>
											) : (
												<>
													<a href="" onClick={(e) => {
														e.preventDefault();
														if (status === 'waiting') return;
														handleSubmit();
													}}>
														{status === 'waiting' ? 'Please wait...' : 'click here'}{' '}
													</a>
													to try again.
												</>
											)}
										</strong>
									</p>
								</div>
							) : (
								<div>
									<h1>Email Verification</h1>

									<p>
										Please enter your email address to receive a verification link. We'll send you an
										email with instructions to verify your account.<i className="fa fa-info-circle"/>
									</p>
									<form onSubmit={handleSubmit}>
										<div className="mb-4">
											<Input
												placeholder="Enter your email"
												prefix={<UserOutlined />}
												value={values.email}
												onChange={(e) => setFieldValue('email', e.target.value)}
											/>
											{errors.email && <div className="text-danger mt-2">{errors.email}</div>}
										</div>

										<Button
											block
											htmlType="submit"
											type="primary"
											size="large"
											disabled={!isValid || isValidating}
											loading={status === 'waiting'}
										>
											Send Verification Link
										</Button>
									</form>

									<div className="login-extra-option">
										<div className="more-links">
											<Link href={'/user/signin'}>Sign in</Link>
										</div>
									</div>
								</div>
							)
						)}
					</Formik>
				)}
			</div>
		</div>
	)
}

export default EmailVerification;
