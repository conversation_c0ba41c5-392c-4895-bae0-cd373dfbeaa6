'use client';

import { Button, Input, message } from "antd";
import { UserOutlined, LockOutlined, LoadingOutlined } from '@ant-design/icons';
import { Formik } from "formik";
import { UserModel } from "@/types/models/user.model";
import { userLoginValidation } from "@/utils/validation.util";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { loginUserAction } from "@/store/slices/user.slice";
import { useRouter } from 'next-nprogress-bar';
import { useEffect, useState } from "react";
import Link from 'next/link';
import { signIn } from "next-auth/react";
import { useSearchParams } from "next/navigation";
import { appStateChange } from "@/store/slices/app.slice";

const LoginForm = () => {
	const dispatch = useDispatch<AppDispatch>();
	// const { status } = useSelector((state: RootState) => state.user);
	const { showLoginModal } = useSelector((state: RootState) => state.app);
	const router = useRouter();
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const [loginSuccess, setLoginSuccess] = useState<boolean>(false);
	const searchParams = useSearchParams();
	const [provider, setProvider] = useState<'google' | 'facebook' | null>(null);

	const handleSignIn = async (provider: 'google' | 'facebook') => {
		try {
			setProvider(provider);
			signIn(provider);
		} catch {
			setProvider(null);
			message.error({
				content: 'Something went wrong. Please try again.',
				style: {
					fontSize: 15,
					color: '#FF0000',
					opacity: 1,
				}
			})
		}
	};

	useEffect(() => {
		if (searchParams.get('error')) {
			if (searchParams.get('error') === 'AccessDenied') {
				message.error({
					content: 'Login failed. Please ensure you have registered with the given email first.',
					style: {
						fontSize: 15,
						color: '#FF0000',
						opacity: 1,
					}
				})
			}
			window.history.replaceState(
				null,
				'',
				`/user/signin`
			);
		}
	}, [])

	return (
		<div className="login-container" style={{ padding: showLoginModal ? "0px" : "80px 15px" }}>
			<div className="login-input-box">
				<h1>What's your login info?</h1>
				<p>Our heartiest Welcome goes to you. Thanks for being a proud member of our unique and ever-expanding line of products! We’re honored to have you with us always <i className="fa fa-info-circle"/></p>
				<Formik<Pick<UserModel, 'email' | 'password'>>
					enableReinitialize={true}
					initialValues={{
						email: '',
						password: '',
					}}
					validate={userLoginValidation}
					onSubmit={async (values, actions) => {
						setIsLoading(true)
						const res = await dispatch(loginUserAction<Pick<UserModel, 'email' | 'password'>>(values));
						if (res && res.status === 200) {
							setLoginSuccess(true)
							setIsLoading(false)
							if (showLoginModal) {
								dispatch(appStateChange({ key: 'showLoginModal', value: false }))
								actions.resetForm();
								router.refresh();
							}
							else router.push('/user/dashboard');
						} else if (res && res.status === 400) {
							router.push('/user/verify');
						}
						else setIsLoading(false)
					}}
				>
					{({ setFieldValue, isValid, errors, handleSubmit }) => (
						<form onSubmit={handleSubmit} autoComplete="off">
							<Input
								id='primary_email'
								name='primary_email'
								autoFocus
								placeholder='Enter your email'
								prefix={<UserOutlined />}
								onChange={(e) => setFieldValue('email', e.target.value)}
							/>
							{errors.email && <div className="text-danger mt-2">{errors.email}</div>}
							<br />
							<br />
							<br />
							<Input.Password
								type={'password'}
								placeholder="Password"
								prefix={<LockOutlined />}
								onChange={(e) => setFieldValue('password', e.target.value)}
							/>
							{errors.password && <div className="text-danger mt-2">{errors.password}</div>}
							<br />
							<br />
							<br />
							<Button
								block
								loading={isLoading}
								size="large"
								type="primary"
								htmlType="submit"
								disabled={!isValid || (provider !== null) || loginSuccess}
							>
								{loginSuccess && !showLoginModal ? 'Redirecting to Dashboard...' : 'Sign in'}
							</Button>
						</form>
					)}
				</Formik>
				<div className="login-extra-option">
					<h3>Or</h3>
					<p className='alternate-login'>
            you can sign in to your personal account with &nbsp;&nbsp;
						<br />
						{/* <span className='social_login_option'>
							<a href="" className="facebook"
								onClick={(e) => {
									e.preventDefault();
									// signIn('facebook');
									handleSignIn('facebook');
								}}
								style={{
									pointerEvents: provider ? "none" : "auto",
									opacity: provider ? 0.5 : 1,
									cursor: provider ? "not-allowed" : "pointer",
								}}
							>
								{provider === 'facebook' ? (
									<span>Loading...<LoadingOutlined /></span>
								) : (
									<span><i className="fab fa-facebook-f" />acebook</span>
								)}
							</a>
						</span>
            &nbsp;&nbsp;or&nbsp;&nbsp; */}
						<span className='social_login_option'>
							<a href="" className="google"
								onClick={(e) => {
									e.preventDefault();
									// signIn('google')
									handleSignIn('google');
								}}
								style={{
									pointerEvents: provider ? "none" : "auto",
									opacity: provider ? 0.5 : 1,
									cursor: provider ? "not-allowed" : "pointer",
								}}
							>
								{provider === 'google' ? (
									<span>Loading...<LoadingOutlined /></span>
								) : (
									<span><i className="fab fa-google" />oogle</span>
								)}
							</a>
						</span>
					</p>
					<div className="more-links">
						<Link href={'/user/signup'}>
							<span> Sign up </span>
						</Link>
						<Link href={'/user/reset'}>Forgot password</Link>
					</div>
				</div>
			</div>
		</div>
	)
}

export default LoginForm;
