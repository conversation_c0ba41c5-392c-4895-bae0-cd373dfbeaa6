'use client';

import React, { useState } from 'react';
import { Tab<PERSON>ontent, TabPane, Nav, NavItem, NavLink } from 'reactstrap';
import Link from 'next/link';
import { Collapse } from 'antd';
import { sentenceCase } from 'change-case';
import { ArtistModel } from '@/types/models/artist.model';
import { EventFaqModel } from '@/types/models/event-faq.model';
import { VenueModel } from '@/types/models/venue.model';
import { EventGalleryModel } from '@/types/models/event-gallery.model';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';
import { EventActivityModel } from '@/types/models/event-activity.model';
import { CaretRightOutlined } from '@ant-design/icons';
import ExpandableContent from '@/components/common/ExpandableContent.component';
import { getFullImageUrl } from '@/utils/general.util';

interface DetailTabsProps {
	data: {
		bookings?: unknown;
		activities?: EventActivityModel[];
		artists?: ArtistModel[];
		galleries?: EventGalleryModel[];
		venue?: VenueModel[];
		terms?: string;
		faqs?: EventFaqModel[];
	};
}

const titleObject = {
	bookings: 'Book Ticket',
	activities: 'Activities',
	artists: 'Special Guests',
	galleries: 'Gallery',
	venue: 'Venue Detail',
	terms: 'T & C',
	faqs: 'FAQ',
};

export default function DetailTabs({ data }: DetailTabsProps) {
	const [activeTab, setActiveTab] = useState<number>(1);

	// For gallery
	const [photoIndex, setPhotoIndex] = useState<number>(0);
	const [isOpen, setIsOpen] = useState<boolean>(false);

	const toggle = (tab: number) => {
		if (activeTab !== tab) {
			setActiveTab(tab);
		}
	};

	const getTabItems = () => {
		if (Object.keys(data).length < 1) return {};

		return Object.keys(titleObject).reduce((acc, key) => {
			const value = data[key as keyof typeof data];

			// Check conditions for filtering out empty or non-existent data
			const isValidData = (
				value !== undefined &&
				value !== null &&
				value !== '' &&
				(Array.isArray(value) ? value.length > 0 : true)
			);

			if (isValidData) {
				acc[key] = value;
			}
			return acc;
		}, {} as Record<string, unknown>);
	};

	const renderArtistJSX = (artists: ArtistModel[]) => {
		return (
			<div className='tab-details'>
				{artists.map((artist, i) => (
					<div key={i} className='detail-panel'>
						<div className='dimage'>
							<img src={artist?.image ? getFullImageUrl(artist.image) : '/images/user_avatar.jpg'} alt='Artist Image' width={200} height={200} />
						</div>
						<div className='dinfo'>
							<div className='dinfo-content'>
								<h2>
									<Link href={`/artists/${artist.id}`}>{artist.name}</Link>
									{artist.title && <span> | {artist.title}</span>}
								</h2>
								<p>
									{artist.stage_name && <span>{artist.stage_name}</span>}
									{artist.band_name && artist.stage_name && ' - from '}
									{artist.band_name && <span>{artist.band_name}</span>}
								</p>
								<ExpandableContent className={'artist-details'} content={artist.about} initialLimit={100} />
							</div>
						</div>
					</div>
				))}
			</div>
		);
	};

	const renderFaqJSX = (faqs: EventFaqModel[]) => {
		const items = faqs.map((faq, index) => ({
			key: index + 1,
			label: sentenceCase(faq.question),
			children: sentenceCase(faq.answer!),
		}));

		return (
			<div className='faq-events'>
				<Collapse
					bordered={false}
					defaultActiveKey={['1']}
					accordion
					items={items}
					expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
				/>
			</div>
		);
	};

	const renderGalleryJSX = (galleries: EventGalleryModel[]) => {
		return (
			<div className="event-gallery">
				{galleries.map((image, i) => (
					<img
						key={image.id}
						onClick={() => {
							setPhotoIndex(i);
							setIsOpen(true);
						}}
						src={getFullImageUrl(image.name)}
						className="gallery-images"
						alt={`gallery image ${i}`}
					/>
				))}

				{isOpen && (
					<Lightbox
						mainSrc={getFullImageUrl(galleries[photoIndex].name)}
						nextSrc={getFullImageUrl(galleries[(photoIndex + 1) % galleries.length].name)}
						prevSrc={getFullImageUrl(galleries[(photoIndex + galleries.length - 1) % galleries.length].name)}
						onCloseRequest={() => setIsOpen(false)}
						onMovePrevRequest={() =>
							setPhotoIndex((photoIndex + galleries.length - 1) % galleries.length)
						}
						onMoveNextRequest={() =>
							setPhotoIndex((photoIndex + 1) % galleries.length)
						}
					/>
				)}
			</div>
		);
	};

	const renderActivityJSX = (activities: EventActivityModel[]) => {
		return (
			<div className='activity-container'>
				{activities.map((activity, index) => (
					<div key={index} className='activity-list'>
						<h3>{activity.title}</h3>
						<div>{activity.details}</div>
					</div>
				))}
			</div>
		);
	};

	const tabItemsCollection = getTabItems();

	return (
		<div className='row event-brief-tab'>
			<div className='col-md-12'>
				<Nav tabs>
					{Object.keys(tabItemsCollection).map((key, index) => (
						<NavItem key={key}>
							<NavLink
								className={activeTab === index + 1 ? 'active' : ''}
								onClick={() => toggle(index + 1)}
							>
								{titleObject[key as keyof typeof titleObject]}
							</NavLink>
						</NavItem>
					))}
				</Nav>
				<TabContent activeTab={activeTab}>
					{Object.keys(tabItemsCollection).map((key, index) => (
						<TabPane key={index} tabId={index + 1}>
							{key === 'artists' &&
								renderArtistJSX(tabItemsCollection[key] as ArtistModel[])}
							{key === 'faqs' && renderFaqJSX(tabItemsCollection[key] as EventFaqModel[])}
							{key === 'terms' && (
								<div
									className='event-terms'
									dangerouslySetInnerHTML={{
										__html: tabItemsCollection[key] as string,
									}}
								/>
							)}
							{key === 'galleries' && renderGalleryJSX(tabItemsCollection[key] as EventGalleryModel[])}
							{key === 'activities' && renderActivityJSX(tabItemsCollection[key] as EventActivityModel[])}
						</TabPane>
					))}
				</TabContent>
			</div>
		</div>
	);
}
