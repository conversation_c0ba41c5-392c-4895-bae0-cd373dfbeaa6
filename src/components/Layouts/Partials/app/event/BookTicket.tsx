'use client'

import { useEffect, useState } from 'react'
import {  <PERSON><PERSON>, <PERSON>, Button } from 'antd'
import { ArrowRightOutlined, RightOutlined } from '@ant-design/icons'
import Image from 'next/image'
import AppModal from '@/components/common/AppModal'
import { EventModel } from '@/types/models/event.model'
import { VenueModel } from '@/types/models/venue.model'
import dayjs from 'dayjs'
import { EventTicketModel } from '@/types/models/event-ticket.model'
import { EventDateModel } from '@/types/models/event-date.model'
import { useRouter } from 'next-nprogress-bar'
import { useDispatch } from 'react-redux'
import { AppDispatch } from '@/store'
import { eventStateChange } from '@/store/slices/event.slice'
import { isAuthenticatedUser } from '@/utils/cookie.util'
import { appStateChange } from '@/store/slices/app.slice'
import { checkTicketAvailabilityAction } from '@/store/slices/purchases.slice'
import { useSearchParams } from 'next/navigation'

type BookTicketWizardProps = {
	event: EventModel;
}

type TicketSelectionType = {
	venue: VenueModel | null;
	type: EventTicketModel | null; // Ticket type
	ticketCount: number | null; // Number of tickets
	date: EventDateModel | null;
}

const BookTicketWizard: React.FC<BookTicketWizardProps> = ({
	event,
}) => {
	const dispatch = useDispatch<AppDispatch>();
	const router = useRouter();
	const searchParams = useSearchParams();

	const [showModal, setShowModal] = useState<boolean>(false);
	const [step, setStep] = useState<number>(1);
	const [loading, setLoading] = useState<boolean>(false);
	const [ticketSelection, setTicketSelection] = useState<TicketSelectionType>({
		venue: event.venues?.length === 1 ? event.venues[0] : null,
		type: null,
		ticketCount: 2,
		date: null,
	});
	const isLoggedIn = isAuthenticatedUser();

	useEffect(() => {
		const showWizard = searchParams.get('showBuyWizard');
		if (showWizard === 'true') {
			setShowModal(true);
			// Clean up URL
			const params = new URLSearchParams(Array.from(searchParams.entries()));
			params.delete('showBuyWizard');
			router.replace(`${window.location.pathname}?${params.toString()}`, { scroll: false });
		}
	}, [searchParams, router]);

	// useEffect(() => {
	// 	if (showModal) {
	// 		setLoading(true);
	// 		if (event.venues && event.venues.length === 1) {
	// 			setTicketSelection((prev) => ({ ...prev, venue: event.venues![0] }));
	// 		}
	// 		setLoading(false);
	// 	}
	// }, [showModal]);

	const renderTicketPanel = () => {
		if (!ticketSelection?.venue?.id) {
			return (
				<>
					<h1>Choose venue</h1>
					<p>Seems like the event is hosted on multiple venues in the same city. Please choose a venue of your desire to proceed further.</p>
					<div className="planbox-container">
						<div className="row">
							<br />
							<br />
							{event.venues && event.venues.map((venue: VenueModel, i: number) => (
								<div
									key={i}
									onClick={() => setTicketSelection((prev) => ({ ...prev, venue }))}
									className="venue-chooser"
								>
									<div className="venue-icon">
										<Image src={venue.image || 'https://placehold.co/100x100/png?text=Venue+Image'} alt="" width={100} height={100} />
									</div>
									<div className="venue-details">
										<h3>{venue.name}</h3>
										{/* <p>{StringUtils.crop(StringUtils.stripHtmlTags(venue.about), 70)}</p> */}
									</div>
								</div>
							))}
						</div>
						<br />
						<br />
					</div>
				</>
			)
		}

		return (
			<>
				<h1>Choose ticket</h1>
				<p>Confirm the ticket for <strong>({ticketSelection?.venue?.name})</strong> by clicking the proceed button for ticket type i.e. shown below.</p>
				<div className="planbox-container">
					<div className="row plan-row">
						{event.tickets && event.tickets.filter(ticket => ticket.venue_id === ticketSelection?.venue?.id).map((ticket, i) => (
							<div key={i} className="col-md-4 ml-auto mr-auto plan-col">
								{/* <div className={`most-popular-mark ${type[ticket.ticket_type as keyof typeof type].popular ? 'active' : ''}`}>
									<p>Most Popular</p>
								</div> */}
								<div className={`most-popular-mark active`}>
									<p>Most Popular</p>
								</div>
								<div className="box-plus plan-box">
									{/* <Icon type={type[ticket.ticket_type as keyof typeof type].icon} style={{ fontSize: 60, color: '#666' }} /> */}
									<h3>{ticket.name}</h3>
									{/* <p>Lorem ipsum dolor sit amet</p> */}
									<h1 className={'price'}>
										{ticket.ticket_type === 'free' ? 'Free' : <>
											<small>RS</small>
											{Number(ticket.price)}
										</>}
									</h1>
									<Button
										size='large'
										type='primary'
										className='btn-proceed'
										onClick={(e) => {
											e.preventDefault()
											setTicketSelection((prev) => ({ ...prev, type: ticket }))
											setStep(2)
										}}
									>
										Proceed <ArrowRightOutlined />
									</Button>
								</div>
							</div>
						))}
					</div>
				</div>
			</>
		)
	}

	const renderSelectedInfoSection = () => {
		const { venue, type: selectedType, ticketCount, date } = ticketSelection

		return (
			<div style={{ backgroundColor: 'white', padding: 10 }} className="selection-info-section">
				<span>Venue: {venue?.name}</span>
				<span>{selectedType ? `Ticket Type: ${selectedType.name} (Rs. ${selectedType.price})` : ''}</span>
				<span>{ticketCount ? `${ticketCount} Tickets` : ''}</span>
				<span>{date ? `${dayjs(date.date).format('dddd, DD MMM YYYY')}` : ''}</span>
			</div>
		)
	}

	return (
		<>
			<a
				href=''
				data-toggle='modal'
				data-target='#bookTicketModal'
				className='ripple-eff btn btn-primary btn-lg'
				onClick={(e) => {
					e.preventDefault();
					if (event.external_ticket_link) {
						window.open(event.external_ticket_link, '_blank', 'noopener,noreferrer');
						return;
					}
					if (!isLoggedIn) {
						dispatch(appStateChange({ key: "showLoginModal", value: true }));
						return;
					}
					const ticketType = event.tickets?.[0]?.ticket_type;
					// if (ticketType === 'free') return;
					if (ticketType !== 'paid') return;
					setShowModal(true);
				}}
			>
				{event.tickets?.[0]?.ticket_type === 'free'
					? 'Free Event'
					: (event.tickets?.[0]?.ticket_type === 'paid' || event.external_ticket_link)
						? 'Buy Tickets'
						: 'N/A'}
			</a>

			{showModal && (
				<AppModal showModal={showModal} toggleAction={() => setShowModal(false)} title={'Buy Ticket'}>
					<Spin spinning={loading} tip="Please Wait ....">
						<div className="m-4">
							<Tabs
								activeKey={step.toString()}
								onChange={(key) => setStep(Number(key))}
								items={[
									{
										key: '1',
										label: 'Choose ticket',
										className: 'tab-1-content',
										children: (
											<div className="ticket-plans">
												{renderTicketPanel()}
											</div>
										),
									},
									{
										key: '2',
										label: 'How many?',
										className: 'tab-2-content',
										disabled: step === 2 ? false: true,
										children: (
											<div className="ticket-plans">
												<h1>Choose number of tickets.</h1>
												<p>Select the number of tickets you want to buy.</p>
												<hr />
												<div className="seat-selector">

													<div className="flex gap-2">
														{Array.from({ length: 15 }, (_, i) => i + 1).map((num) => (
															<button
																key={num}
																className={`number-button ${ticketSelection.ticketCount === num ? 'selected' : ''}`}
																onClick={() => setTicketSelection((prev) => ({ ...prev, ticketCount: num }))}
															>
																{num}
															</button>
														))}
													</div>
												</div>
												<hr />
												<Button
													size='large'
													type='primary'
													className='btn-proceed'
													onClick={async (e) => {
														e.preventDefault()
														setLoading(true);
														const res = await dispatch(checkTicketAvailabilityAction(
															event.id,
															ticketSelection.type!.id,
															ticketSelection.ticketCount!
														));

														if (res && res.status === 200) {
															dispatch(eventStateChange({ key: 'record', value: event }));
															localStorage.removeItem("buyFormState");
															router.push(`/events/buy/${event.id}?sessionId=${res.data.id}&ticket=${ticketSelection.type?.id}&venue=${ticketSelection.venue?.id}&ticketCount=${ticketSelection.ticketCount}`);
														} else {
															setLoading(false);
														}
													}}
													disabled={!ticketSelection.ticketCount}
												>
													Proceed <RightOutlined />
												</Button>
												<br />
											</div>
										),
									},
									// {
									// 	key: '3',
									// 	label: 'Select when?',
									// 	className: 'tab-3-content',
									// 	disabled: step === 3 ? false: true,
									// 	children: (
									// 		<div className="ticket-plans">
									// 			<br />
									// 			<h1 />
									// 			<p>The events is being hosted on the following dates.</p>
									// 			<hr />
									// 			{event.dates && event.dates.filter(date => date.venue_id === ticketSelection?.venue?.id).map((date, i) => (
									// 				<div
									// 					onClick={() => setTicketSelection((prev) => ({ ...prev, date }))}
									// 					key={i}
									// 					className={`date-on-panel ${date === ticketSelection.date ? "active" : ""}`}
									// 				>
									// 					{dayjs(date.date).format('dddd, DD MMM YYYY')}
									// 				</div>
									// 			))}
									// 			<div className="clearfix" />
									// 			<hr />
									// 			<Button
									// 				size='large'
									// 				type='primary'
									// 				className='btn-proceed'
									// 				onClick={(e) => {
									// 					setLoading(true);
									// 					e.preventDefault()
									// 					router.push(`/events/buy/${event.id}?ticket=${ticketSelection.type?.id}&date=${ticketSelection.date?.id}&venue=${ticketSelection.venue?.id}&ticketCount=${ticketSelection.ticketCount}`);
									// 					setLoading(false);
									// 				}}
									// 				disabled={!ticketSelection.date || !ticketSelection.ticketCount || !ticketSelection.venue || !ticketSelection.type}
									// 			>
									// 				Proceed <RightOutlined />
									// 			</Button>
									// 			<br />
									// 		</div>
									// 	),
									// },
								]}
							/>
							{step > 1 ? renderSelectedInfoSection() : null}
						</div>
					</Spin>
				</AppModal>
			)}
		</>
	)
}

export default BookTicketWizard
