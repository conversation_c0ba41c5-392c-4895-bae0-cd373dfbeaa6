'use client';

import React, { useState } from 'react';
import pluralize from 'pluralize';
import { LoadingOutlined } from '@ant-design/icons';

const Comment = () => {
	const [comment, setComment] = useState('');
	const [isSaving, setIsSaving] = useState(false);

	// Example data
	const user = {
		data: {
			image: '/images/user_avatar.jpg',
			full_name: '<PERSON>'
		}
	};

	const exampleComments = [
		{
			id: 1,
			user: {
				full_name: '<PERSON>',
				image: '/images/user_avatar.jpg'
			},
			comment: 'This is a great post! Thanks for sharing.',
			at: '2 hours ago',
			replies: [
				{
					user: {
						full_name: '<PERSON>',
						image: '/images/user_avatar.jpg'
					},
					comment: 'I agree, very informative!',
					at: '1 hour ago'
				}
			]
		},
		{
			id: 2,
			user: {
				full_name: '<PERSON>',
				image: '/images/user_avatar.jpg'
			},
			comment: 'Could you elaborate more on the third point?',
			at: '1 day ago',
			replies: []
		}
	];

	const onInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		setComment(e.target.value);
	};

	const saveComment = () => {
		setIsSaving(true);
		// Simulating an API call
		setTimeout(() => {
			setIsSaving(false);
			setComment('');
			// In a real application, you would update the comments state here
		}, 1000);
	};

	return (
		<div className='row'>
			<div className='col-sm-8'>
				<div id='comments-section' className='comment-section mt-5'>
					<h2 className='text-2xl font-bold mb-4'>
						{exampleComments.length} {pluralize('Comment', exampleComments.length)}
					</h2>

					{/* Main comment input */}
					<div className='comment-grid-view'>
						<div className='commenter-img'>
							<img
								// src={user.data.image}
								src='https://res.cloudinary.com/galaktik-nepal-p-ltd/image/upload/c_scale,w_140/v1543906497/no_user_available.png'
								alt=''
							/>
						</div>
						<div className='comment-action-box'>
							<form className='evt-comment'>
								<textarea
									value={comment}
									onChange={onInputChange}
									onKeyDown={(e) => {
										if (e.key === 'Enter' && !e.shiftKey) {
											e.preventDefault();
											saveComment();
										}
									}}
									spellCheck='false'
									placeholder={
										exampleComments.length > 0
											? 'Leave your comment.'
											: 'Be the first person to post comment.'
									}
									className='w-full min-h-[100px] p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500'
								/>
								{isSaving && <LoadingOutlined />}
							</form>
						</div>
					</div>

					{/* Comments sorting (only show if more than 2 comments) */}
					{exampleComments.length > 2 && <ul className="nav nav-tabs" id="myTab" role="tablist">
						<li className="nav-item">
							<a className="nav-link dropdown-toggle" data-toggle="dropdown" href="#"
								role="button"
								aria-haspopup="true" aria-expanded="false">Popular &nbsp;&nbsp;&nbsp;<i
									className="fa fa-angle-down" /></a>
							<div className="dropdown-menu">
								<a className="dropdown-item" href="#">Latest.</a>
								<a className="dropdown-item" href="#">Popular.</a>
							</div>
						</li>
					</ul>}

					{/* Comments list */}
					<div className="tab-content" id="myTabContent">
						<div className="tab-pane fade show active" id="home" role="tabpanel"
							aria-labelledby="home-tab">

							{exampleComments.map((comment, i) => (
								<div key={i} className={'comments-list-container'}>
									<div className="comment-grid-view">
										<div className="commenter-img">
											<img src={comment.user.image} alt="" />
										</div>
										<div className="comment-action-box comment-text">
											<h4>{comment.user.full_name} <span>{comment.at}</span></h4>
											<p>{comment.comment}</p>
											<a href=""> Reply </a>
										</div>
									</div>

									{comment.replies.map((reply, j) => (
										<div key={j} className='reply-box'>
											<div className='commenter-img'>
												<img
													src={reply.user.image}
													alt=''
												/>
											</div>
											<div className="comment-action-box comment-text">
												<h4>{reply.user.full_name} <span>{reply.at}</span></h4>
												<p>{reply.comment}</p>
											</div>
										</div>
									))}

									<div className="reply-box">
										<div className="commenter-img">
											{user ? <img
												src={user.data.image ? user.data.image : 'https://res.cloudinary.com/galaktik-nepal-p-ltd/image/upload/c_scale,w_140/v1543906497/no_user_available.png'}
												alt="" /> : null}
										</div>
										<div className="comment-action-box comment-text">
											<form action="" className={'evt-comment'}>
												<textarea
													spellCheck='false'
													placeholder={`Reply to "${comment.user.full_name}"`}
													className='w-full p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500'
												/>
											</form>
										</div>
									</div>
								</div>
							))}

							{exampleComments.length > 3 &&
								<a href="" className="load-more-events">Load more comments ....</a>}
						</div>
					</div>

				</div>
			</div>
		</div>
	);
};

export default Comment;

