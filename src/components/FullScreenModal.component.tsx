import { Modal } from 'antd';

interface FullScreenModalProps {
  show: boolean;
  onClose: () => void;
  className?: string;
  children: React.ReactNode;
}

const FullScreenModal: React.FC<FullScreenModalProps> = ({ show, onClose, className, children }) => {
	return (
		<Modal
			className={`full_screen_custom_modal ${className}`}
			width={'100%'}
			style={{ width: '100%' }}
			onCancel={() => {
				onClose();
			}}
			open={show}
			footer={false}
		>
			{children}
		</Modal>
	)
}

export default FullScreenModal
