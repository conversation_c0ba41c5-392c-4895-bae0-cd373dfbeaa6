import { FunctionType } from '@/types/general.types';
import { User, Company } from '@/models';

declare global {
  namespace Express {
    export interface Request {
      user?: Partial<User>;
      company?: Partial<Company>;
    }

    export interface Response {
      success: FunctionType,
      created: FunctionType,
      updated: FunctionType,
      deleted: FunctionType,
      paginated: FunctionType,
      single: FunctionType,
      file: FunctionType,
      failure: FunctionType,
      invalid: FunctionType,
      forbidden: FunctionType,
      notFound: FunctionType,
      unauthorized: FunctionType,
      collection: FunctionType,
    }
  }
}
