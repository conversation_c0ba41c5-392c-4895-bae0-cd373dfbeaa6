'use client';

import { createContext, useContext, ReactNode } from 'react';
import { useJsApiLoader } from '@react-google-maps/api';

type GoogleMapsContextType = {
  isLoaded: boolean;
};

const GoogleMapsContext = createContext<GoogleMapsContextType>({ isLoaded: false });

export const GoogleMapsProvider = ({ children }: { children: ReactNode }) => {
	const { isLoaded } = useJsApiLoader({
		id: 'google-map-script',
		googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_API_KEY as string,
	});

	return (
		<GoogleMapsContext.Provider value={{ isLoaded }}>
			{children}
		</GoogleMapsContext.Provider>
	);
};

export const useGoogleMaps = () => useContext(GoogleMapsContext);
