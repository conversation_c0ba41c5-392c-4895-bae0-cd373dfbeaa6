import env from '@/utils/env.util';
import { Sequelize } from 'sequelize-typescript';
import * as models from '@/models';

const dbName = env.DB_NAME as string;
const dbUser = env.DB_USER as string;
const dbHost = env.DB_HOST as string;
const dbPort = env.DB_PORT as number;
const dbPassword = env.DB_PASSWORD as string;

const sequelize = new Sequelize(dbName, dbUser, dbPassword, {
	dialect: 'postgres',
	host: dbHost,
	port: typeof dbPort === 'string' ? parseInt(dbPort) : dbPort,
	replication: {
		read: [
			{
				host: dbHost,
				username: db<PERSON><PERSON>,
				password: dbPassword,
			},
		],
		write: {
			host: dbHost,
			username: db<PERSON><PERSON>,
			password: dbPassword,
		},
	},
	dialectOptions: {
		supportBigNumbers: true,
		parseInt: true,
		decimalNumbers: true,
		useUTC: false,
	},
	timezone: '+5:45',
	logging: false,
});

sequelize.addModels(Object.values(models));

export default sequelize;
