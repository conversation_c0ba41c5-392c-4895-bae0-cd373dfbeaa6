/* eslint-disable @typescript-eslint/no-explicit-any */
const baseURL = process.env.NEXT_PUBLIC_APP_BASE_API;
import { cookies } from 'next/headers';

async function fetchRequest(url: string, options: any = {}) {
	const cookieStore = cookies();
	const token = cookieStore.get('_eticket_at_');
	const headers = {
		'Content-Type': 'application/json',
		'Access-Control-Allow-Origin': '*',
		...options.headers,
	};
	if (token && token.value) {
		// headers.Authorization = `Bearer ${token.value}`;
		headers.cookie = `_eticket_at_=${token.value}`;
	}

	try {
		const response = await fetch(`${baseURL}${url}`, {
			...options,
			headers,
		});
		if (!response.ok) {
			const errorData = await response.json();
			// const errorMessage = errorData.message || 'Something went wrong';
			return Promise.reject(errorData);
		}

		const data = await response.json();
		return data;

	} catch (error: any) {
		const message = error.message || 'Unknown Error';
		if (error.name === 'TokenExpiredError') {
			// Handle token expiration case
		} else if (error.name === 'RenewSessionError' || message === 'JsonWebTokenError') {
			window.location.reload(); // Reload page on token-related issues
		}
		// notification.error({ message: 'Failed', description: message });
		return Promise.reject(error);
	}
}

export default fetchRequest;
