import { NextResponse, type NextRequest } from 'next/server'
import { decryptData } from './utils/cookie.util'
import { UserModel } from './types/models/user.model'

const PUBLIC_FILE = /\.(.*)$/
const AUTH_ROUTES = new Set(['/user/signin', '/user/signup', '/partner/register', '/user/verify', '/user/reset'])
const ADMIN_DASHBOARD_ROUTES = [
	'/user/dashboard/artists',
	'/user/dashboard/venues',
	'/user/dashboard/categories',
	'/user/dashboard/topics',
	'/user/dashboard/userlog',
	'/user/dashboard/general-info'
]
const PARTNER_DASHBOARD_ROUTES = [
	'/user/dashboard/events',
	'/user/dashboard/company',
]

// Helper function to check if a pathname matches any base route
const matchesBaseRoute = (pathname: string, baseRoutes: string[]): boolean => {
	return baseRoutes.some(route => {
		// Match exact route or route with parameters
		// e.g., '/user/dashboard/artists' or '/user/dashboard/artists/123'
		return pathname === route || pathname.startsWith(route + '/')
	})
}

export function middleware(request: NextRequest) {
	const { pathname } = request.nextUrl
	const token = request.cookies.get('_eticket_at_')?.value
	const userInfo = request.cookies.get('_eticket_user_')?.value
	let user;
	if (userInfo) user = decryptData(userInfo) as UserModel;

	// Redirect non-admin users away from admin dashboard routes
	if (user?.user_type !== 'admin' &&  matchesBaseRoute(pathname, ADMIN_DASHBOARD_ROUTES)) {
		return NextResponse.redirect(new URL('/', request.url))
	}

	// Redirect non-partner users away from partner dashboard routes
	if (!user?.is_partner &&  matchesBaseRoute(pathname, PARTNER_DASHBOARD_ROUTES)) {
		return NextResponse.redirect(new URL('/', request.url))
	}

	// Allow public files
	if (PUBLIC_FILE.test(pathname)) {
		return NextResponse.next()
	}

	// Redirect authenticated users away from auth routes
	if (AUTH_ROUTES.has(pathname) && token) {
		return NextResponse.redirect(new URL('/', request.url))
	}

	// Protect user routes
	if (pathname.startsWith('/user/') && !AUTH_ROUTES.has(pathname) && !token) {
		return NextResponse.redirect(new URL('/user/signin', request.url))
	}

	// Redirect away from buy page if not authenticated
	if (pathname.startsWith('/events/buy') && !token) {
		return NextResponse.redirect(new URL('/user/signin', request.url))
	}

	// Allow all other routes.
	return NextResponse.next()
}

export const config = {
	matcher: ['/((?!api|_next/static|_next/image|static|.*\\.png$).*)'],
}
