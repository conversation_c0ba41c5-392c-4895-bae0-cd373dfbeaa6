import NextAuth, { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
// import FacebookProvider from "next-auth/providers/facebook";
import axios from "axios";
import { encryptData } from "@/utils/cookie.util";
import { cookies } from "next/headers";

const secret = process.env.NEXT_AUTH_SECRET;
const GOOGLE_CLIENT_ID = process.env.NEXT_GOOGLE_CLIENT_ID;
const GOOGLE_CLIENT_SECRET = process.env.NEXT_GOOGLE_CLIENT_SECRET;
const FACEBOOK_CLIENT_ID = process.env.NEXT_PUBLIC_FACEBOOK_CLIENT_ID;
const FACEBOOK_CLIENT_SECRET = process.env.NEXT_FACEBOOK_CLIENT_SECRET;

if (!secret) {
	throw new Error("Missing NextAuth Secret");
}

if (!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) {
	throw new Error("Missing Google Client ID or Secret");
}

if (!FACEBOOK_CLIENT_ID || !FACEBOOK_CLIENT_SECRET) {
	throw new Error("Missing Facebook Client ID or Secret");
}

const authOptions: NextAuthOptions = {
	providers: [
		GoogleProvider({
			clientId: GOOGLE_CLIENT_ID,
			clientSecret: GOOGLE_CLIENT_SECRET,
			authorization: {
				params: {
					prompt: "consent",
				}
			}
		}),
		// FacebookProvider({
		// 	clientId: FACEBOOK_CLIENT_ID,
		// 	clientSecret: FACEBOOK_CLIENT_SECRET,
		// 	// authorization: {
		// 	// 	url: "https://www.facebook.com/v23.0/dialog/oauth",
		// 	// 	params: {
		// 	// 		auth_type: "reauthenticate",
		// 	// 		prompt: "login",
		// 	// 	}
		// 	// }
		// })
	],
	secret,
	callbacks: {
		async signIn({ account }) {
			try {
				const response = await axios.post(
					`${process.env.NEXT_PUBLIC_APP_BASE_API}/api/login/user/provider`,
					{
						accessToken: account?.access_token,
						provider: account?.provider,
					},
					{
						headers: {
							"Content-Type": "application/json",
						},
					}
				);

				// Check the response status
				if (response.status === 200) {
					const { tokens, userInfo } = response.data.data;
					const isProduction = process.env.NODE_ENV === "production";
					const cookiesStore = cookies();

					// Set Access Token
					cookiesStore.set("_eticket_at_", tokens.accessToken, {
						secure: isProduction,
						// httpOnly: true,
						domain: isProduction ? '.eticketnepal.com' : undefined,
						sameSite: isProduction ? "none" : "lax",
						path: "/",
						maxAge: 365 * 24 * 60 * 60, // 1 year
					});

					// Set Refresh Token
					cookiesStore.set("_eticket_rt_", tokens.refreshToken, {
						secure: isProduction,
						// httpOnly: true,
						domain: isProduction ? '.eticketnepal.com' : undefined,
						sameSite: isProduction ? "none" : "lax",
						path: "/",
						maxAge: 365 * 24 * 60 * 60, // 1 year
					});

					// Encrypt and set User Info
					const encryptedUserInfo = encryptData(userInfo);
					cookiesStore.set("_eticket_user_", encryptedUserInfo, {
						secure: isProduction,
						// httpOnly: true,
						domain: isProduction ? '.eticketnepal.com' : undefined,
						sameSite: isProduction ? "none" : "lax",
						path: "/",
						maxAge: 365 * 24 * 60 * 60, // 1 year
					});

					return true; // Allow sign-in
				}
			} catch {
				// placeholder
			}

			return false;
		},
		async redirect({ baseUrl, url }) {
			if (url.split('/').at(-1) !== 'signin') return url;
			return `${baseUrl}/user/dashboard`;
		},
	},
	pages: {
		signIn: '/user/signin',
		error: '/user/signin',
	}
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
