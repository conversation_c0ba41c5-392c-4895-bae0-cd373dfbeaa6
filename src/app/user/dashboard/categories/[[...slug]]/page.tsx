'use client'

import AddCategory from "@/components/category/AddCategory.component";
import TableSkeleton from "@/components/common/TableSkeleton";
import { AppDispatch, RootState } from "@/store";
import { triggerConfirmation } from "@/store/slices/app.slice";
import { categoryStateChange, deleteCategoryAction, fetchAllCategoriesAction, fetchCategoryAction, updateCatStatusOrNavListAction } from "@/store/slices/category.slice";
import { PaginationMeta, ServerStatus } from "@/types/index.types";
import { CategoryModel } from "@/types/models/category.model";
import { getFullImageUrl } from "@/utils/general.util";
import { CheckCircleFilled, DeleteFilled, EditFilled, FileImageOutlined, GroupOutlined, MoreOutlined, PlusOutlined, StopFilled, UngroupOutlined } from "@ant-design/icons";
import { TableProps, Tag, Button, Table, Dropdown, Pagination } from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

interface CategoriesPageProps {
  params: {
    slug: string[]
  }
}

const CategoriesPage = ({ params }: CategoriesPageProps) => {
	const categoryId = params.slug ? params.slug.pop() : null;
	const dispatch = useDispatch<AppDispatch>();
	const [showModal, setShowModal] = useState<boolean>(false);
	const { rows, meta, status } = useSelector((state: RootState) => state.category) as { rows: CategoryModel[], meta: PaginationMeta | null, status: ServerStatus };

	const columns: TableProps<CategoryModel>['columns'] = [
		{
			title: '#',
			key: 'index',
			width: '1%',
			align: 'center',
			render: (_text, _record, index) => index + 1,
		},
		{
			title: 'Name',
			dataIndex: 'name',
			key: 'name',
			width: '25%',
			render: (text: string, record: CategoryModel) => (
				<div className="event-item">
					<div className="event-item__image-container">
						{record.image ? (
							<img
								src={getFullImageUrl(record.image)}
								alt={text || 'Event'}
								className="event-item__image"
							/>
						) : (
							<div className="event-item__placeholder">
								<FileImageOutlined size={20} />
							</div>
						)}
					</div>
					<div className="event-item__content">
						<span className="event-item__title">{text || '-'}</span>
						<div className="event-item__categories">
							<Tag key={'category-type'} color={record.is_main ? 'pink' : 'blue'} className="event-item__category-tag">{record.is_main ? 'Main-Category' : 'Sub-Category'}</Tag>
							{record.navigation_list && (
								<Tag key={'navigation-list'} color={'green'} className="event-item__category-tag">Navigation List</Tag>
							)}
						</div>
					</div>
				</div>
			)
		},
		{
			title: 'Icon',
			dataIndex: 'icon_name',
			key: 'icon',
			width: '10%',
			align: 'left',
			render: (text: string) => (
				<div>
					{text ? <i className={text} /> : '-'}
				</div>
			)
		},
		{
			title: 'Status',
			dataIndex: 'status',
			key: 'status',
			width: '10%',
			align: 'left',
			render: (text) => (
				<Tag color={text ? 'green' : 'red'}>{text ? 'Active' : 'Inactive'}</Tag>
			)
		},
		{
			title: 'Created At',
			dataIndex: 'created_at',
			key: 'created_at',
			width: '10%',
			align: 'left',
			render: (text: string) => (
				<div>
					<p>{dayjs(text).format('Do MMM YYYY')}</p>
					<span style={{ fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)' }}>
						{dayjs(text).fromNow()}
					</span>
				</div>
			)
		},
		{
			title: 'Actions',
			key: 'actions',
			width: '5%',
			align: 'right',
			render: (_text, record: CategoryModel) => (
				<Dropdown
					overlayStyle={{ width: 250 }}
					trigger={['click']}
					menu={{
						items: [
							{
								label: <span className="d-flex align-items-center gap-2">
									<EditFilled /> <span>Edit</span>
								</span>,
								key: 'edit',
								disabled: !record.status,
								onClick: async () => {
									await dispatch(categoryStateChange({ key: 'record', value: record }));
									setShowModal(true);
									window.history.replaceState(
										null,
										'',
										`/user/dashboard/categories/${record.id}`
									);
								},
							},
							{
								label: record.status ? (
									<span className="d-flex align-items-center gap-2">
										<StopFilled /> <span>Deactivate</span>
									</span>
								) : (
									<span className="d-flex align-items-center gap-2">
										<CheckCircleFilled /> <span>Activate</span>
									</span>
								),
								key: 'status',
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: `${record.status ? 'Deactivate' : 'Activate'} Category`,
										subTitle: <>Are you sure you want to {record.status ? 'deactivate' : 'activate'} <strong>{record.name}</strong> category?</>,
										additionalInfo: `${record.status ? 'Deactivated' : 'Activated'} category will ${record.status ? 'not be' : 'be'} visible to other users.`,
										btnLabel: `${record.status ? 'Deactivate' : 'Activate'} Now`,
										onBtnClick: async () => {
											const res = await dispatch(updateCatStatusOrNavListAction(record.id, { status: !record.status }));
											if (res && res.status === 200) {
												await dispatch(fetchAllCategoriesAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							},
							{
								label: record.navigation_list ? (
									<span className="d-flex align-items-center gap-2">
										<UngroupOutlined /> <span>Remove from Navigation List</span>
									</span>
								) : (
									<span className="d-flex align-items-center gap-2">
										<GroupOutlined /> <span>Use in Navigation List</span>
									</span>
								),
								key: 'navigation_list',
								disabled: !record.status,
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: `${record.navigation_list ? 'Remove from' : 'Use in'} Navigation List`,
										subTitle: <>Are you sure you want to {record.navigation_list ? 'remove' : 'use'} <strong>{record.name}</strong> category {record.navigation_list ? 'from' : 'in'} navigation list?</>,
										additionalInfo: `${record.navigation_list ? 'Removed' : 'Used'} category will ${record.navigation_list ? 'not be' : 'be'} visible to other users.`,
										btnLabel: `${record.navigation_list ? 'Remove' : 'Use'} Now`,
										onBtnClick: async () => {
											const res = await dispatch(updateCatStatusOrNavListAction(record.id, { navigation_list: !record.navigation_list }));
											if (res && res.status === 200) {
												dispatch(fetchAllCategoriesAction({ getNavCategories: true, type: 'active' }));
												dispatch(fetchAllCategoriesAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							},
							{
								label: <span className="d-flex align-items-center gap-2">
									<DeleteFilled /> <span>Delete</span>
								</span>,
								key: 'delete',
								danger: true,
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: 'Delete Category',
										subTitle: <>Are you sure you want to delete <strong>{record.name}</strong> category?</>,
										additionalInfo: 'Deleted Category will not be visible to other users.',
										btnLabel: 'Delete Now',
										onBtnClick: async () => {
											const res = await dispatch(deleteCategoryAction(record.id));
											if (res && res.status === 200) {
												await dispatch(fetchAllCategoriesAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							}
						]
					}}
				>
					<MoreOutlined />
				</Dropdown>
			)
		},
	];

	useEffect(() => {
		if (!showModal) dispatch(fetchAllCategoriesAction({ page: meta?.currentPage || 1, perPage: 10 }));
		if (categoryId) {
			dispatch(fetchCategoryAction(Number(categoryId)))
				.then((res) => {
					if (res && res.status === 200) setShowModal(true);
				});
		}
	}, [dispatch, categoryId, showModal])

	return (
		<>
			<div className="page-intro-header">
				<div className="header-title">
					<h1 className="titled-info">Categories List</h1>
					<p>Manage your categories here</p>
				</div>

				<div className="action-button-container">
					<Button onClick={() => setShowModal(true)} icon={<PlusOutlined />}>Add Category</Button>
				</div>
			</div>

			<div className="dashboard-main-content">
				<div className="tabled-content">
					{status === 'fetching' ? (
						<Table<CategoryModel>
							rowKey="id"
							columns={columns}
							dataSource={[]}
							rowClassName={'custom-table-row'}
							className="dashboard-table"
							size='small'
							pagination={false}
							locale={{
								emptyText: <TableSkeleton length={10} />
							}}
						/>
					) : (
						<Table<CategoryModel>
							rowKey="id"
							columns={columns}
							dataSource={rows}
							rowClassName={'custom-table-row'}
							size='small'
							pagination={false}
							locale={{
								emptyText: <div>No Categories Found. Add Categories to get started.</div>
							}}
						/>
					)}
					{(meta && meta.total > 10) && (
						<Pagination
							align="center"
							total={meta?.total}
							showSizeChanger={false}
							disabled={status === 'fetching'}
							style={{ marginTop: '20px' }}
							current={meta?.currentPage}
							onChange={(page) => dispatch(fetchAllCategoriesAction({ page, perPage: meta?.perPage }))}
						/>
					)}
				</div>
			</div>

			<AddCategory showModal={showModal} setShowModal={setShowModal} />
		</>
	)
}

export default CategoriesPage
