'use client';

import { useEffect } from 'react';
import { <PERSON><PERSON>, Divider, Button, Skeleton } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store';
import { CategoryModel } from '@/types/models/category.model';
import { fetchAllCategoriesAction } from '@/store/slices/category.slice';
import { ServerStatus } from '@/types/index.types';
import { Formik } from 'formik';
import { UserModel } from '@/types/models/user.model';
import { fetchUserPreferencesAction, updateUserPreferencesAction } from '@/store/slices/user.slice';

type PreferencesFormValues = {
	selectedCategories: number[];
	notification_email: boolean;
	notification_whatsapp: boolean;
};


const Preferences = () => {
	const dispatch = useDispatch<AppDispatch>();

	const { rows: categories, status: catStatus } = useSelector((state: RootState) => state.category) as {
		rows: CategoryModel[],
		status: ServerStatus
	};
	const { preferences, status: prefStatus } = useSelector((state: RootState) => state.user) as {
		preferences: Pick<UserModel, "user_preference" | "user_category_preferences"> | null,
		status: ServerStatus
	};

	useEffect(() => {
		dispatch(fetchAllCategoriesAction({ getMainCategories: true, type: 'active' }));
		dispatch(fetchUserPreferencesAction());
	}, [dispatch]);

	return (
		<div>
			<ul>
				<li>
					<Formik<PreferencesFormValues>
						enableReinitialize
						initialValues={{
							selectedCategories: preferences?.user_category_preferences?.map((pref) => pref.category_id) || [],
							notification_email: preferences?.user_preference?.notification_email || false,
							notification_whatsapp: preferences?.user_preference?.notification_whatsapp || false,
						}}
						onSubmit={(values) => {
							dispatch(updateUserPreferencesAction<PreferencesFormValues>(values));
						}}
					>
						{({ values, setFieldValue, handleSubmit, isSubmitting, dirty }) => (
							<div className="user-preferences">
								<h1>Update your email preferences!</h1>
								<Divider />
								<p>You can choose to personalize your emails based on your interests! Your favorite events.</p>
								<p>This one’s all about YOU!</p>
								<p>Feel free to tell us everything you love, we promise your inbox will not be spammed.</p>

								<br />
								<h4>Interested Events</h4>
								<br />
								<div className="row event-options">
									{catStatus === 'fetching' || prefStatus === 'fetching' ? (
										<div className="col-4 checkbox-opt">
											<Skeleton.Input active size='small' />
											<Skeleton.Input active size='small' />
											<Skeleton.Input active size='small' className="mt-2" />
										</div>
									) : (
										<>
											{categories.map((cat) => (
												<div key={cat.id} className="col-4 checkbox-opt">
													<Checkbox
														checked={values.selectedCategories.includes(cat.id)}
														onChange={(e) => {
															const updated = e.target.checked
																? [...values.selectedCategories, cat.id]
																: values.selectedCategories.filter(id => id !== cat.id);
															setFieldValue('selectedCategories', updated);
														}}
													>
														{cat.name}
													</Checkbox>
												</div>
											))}
											<div className="col-4 checkbox-opt">
												<Checkbox
													checked={values.selectedCategories.length === categories.length}
													onChange={(e) => {
														setFieldValue('selectedCategories', e.target.checked ? categories.map(cat => cat.id) : []);
													}}
												>
													I enjoy all the above events
												</Checkbox>
											</div>
										</>
									)}
								</div>

								<br />
								<h4>I am fine receiving communication over:</h4>
								<br />
								{prefStatus === 'fetching' ? (
									<div className='event-options d-flex flex-column gap-2'>
										<Skeleton.Input active size='small' style={{ width: 500 }} />
										<Skeleton.Input active size='small' style={{ width: 500 }} />
									</div>
								) : (
									<div className="event-options">
										<div className="checkbox-opt">
											<Checkbox
												checked={values.notification_email}
												onChange={(e) => setFieldValue('notification_email', e.target.checked)}
											>
												Email asking for Feedback on my experience with ETicketNepal.
											</Checkbox>
										</div>
										<div className="checkbox-opt">
											<Checkbox
												checked={values.notification_whatsapp}
												onChange={(e) => setFieldValue('notification_whatsapp', e.target.checked)}
											>
												Message on WhatsApp
											</Checkbox>
										</div>
									</div>
								)}

								<br />
								<Divider />
								<Button
									type="primary"
									size="large"
									onClick={() => handleSubmit()}
									loading={isSubmitting}
									disabled={!dirty}
								>
									Submit my preferences
								</Button>
								<br />
							</div>
						)}
					</Formik>
				</li>
			</ul>
		</div>
	);
}

export default Preferences;
