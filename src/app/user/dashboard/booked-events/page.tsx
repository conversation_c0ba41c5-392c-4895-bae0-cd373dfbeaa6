'use client';

import Link from 'next/link';
import { FilePdfFilled } from '@ant-design/icons';
import { PaginationMeta, ServerStatus } from '@/types/index.types';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store';
import { useEffect, useState } from 'react';
import { downloadEventTicketAction, fetchBookedEventsAction } from '@/store/slices/user.slice';
import { getFullImageUrl, stringLimit } from '@/utils/general.util';
import { message, Pagination, Skeleton, Tabs } from 'antd';
import dayjs from 'dayjs';
import { PurchaseInfoModel } from '@/types/models/purchase-info.model';

const BookedEvents = () => {
	const dispatch = useDispatch<AppDispatch>();
	const { purchaseInfos, status, meta } = useSelector((state: RootState) => state.user) as {
		purchaseInfos: PurchaseInfoModel[], status: ServerStatus, meta: PaginationMeta
	};
	const [messageApi, contextHolder] = message.useMessage();
	const [activeTab, setActiveTab] = useState<string>('upcoming');

	useEffect(() => {
		dispatch(fetchBookedEventsAction({ type: activeTab, page: 1, perPage: 10 }));
	}, [dispatch, activeTab]);

	return (
		<>
			{contextHolder}
			<Tabs
				activeKey={activeTab}
				items={[
					{
						key: 'upcoming',
						label: 'Upcoming Events',
					},
					{
						key: 'ongoing',
						label: 'Ongoing Events',
					},
					{
						key: 'past',
						label: 'Past Events',
					},
				]}
				onChange={(key) => {
					if (status === 'fetching') return;
					setActiveTab(key);
				}}
			/>
			{status == 'fetching' ? (
				<ul className='list-container'>
					{[...Array(4)].map((_, i) => (
						<li key={i}>
							<Skeleton.Image style={{ width: 140, height: 80, margin: 10 }} active />

							<div className="title-content booked-event-content">
								<h2>
									<Skeleton.Input style={{ width: 250 }} active size="small" />
								</h2>
								<p><Skeleton.Input style={{ width: 500, marginTop: 8 }} active size="small" /></p>
								<p><Skeleton.Input style={{ width: 500, marginTop: 4 }} active size="small" /></p>
							</div>
						</li>
					))}
				</ul>
			) : (
				<ul className="list-container booked-events-list">
					{purchaseInfos.length === 0 ? (
						<div className="no-data-found">
							<img
								style={{ width: 300, marginLeft: 100 }}
								src="/images/not-found.png"
								alt="No Subscriptions"
							/>
							<h3 style={{ marginLeft: -350 }}>
              No {activeTab} events found
							</h3>
						</div>
					) : (
						purchaseInfos.map((pInfo, i) => (
							<li key={i}>
								<img src={pInfo?.event?.image ? getFullImageUrl(pInfo.event.image) : "https://placehold.co/130x90/png?text=No+Image"} alt={pInfo?.event?.title || ""} />
								<div className="title-content booked-event-content">
									<h2>
										<Link
											href={`/events/${pInfo?.event?.slug}-${pInfo?.event?.id}`}
										>
											<span>{pInfo?.event?.title}</span>
										</Link>
									</h2>
									{pInfo?.event?.about && (
										<p>{stringLimit(pInfo.event.about, 60)}</p>
									)}
									<div className="add-info">
										<p><strong>Venue:</strong> {pInfo?.venue?.name}</p>
										<p>
											<strong>Date:</strong>{' '}
											{
												`${dayjs(pInfo?.event?.start_date_time).format('MMMM D, YYYY h:mm A')} - 
											${dayjs(pInfo?.event?.end_date_time).format('MMMM D, YYYY h:mm A')}`
											}
										</p>
										{/* <p>
										<strong>Ticket Count:</strong> 0
									</p> */}
									</div>
								</div>
								<div className="action-bar">
									<a
										href=""
										onClick={async (e) => {
											e.preventDefault();
											if (status === 'downloading') return;
											try {
												messageApi.loading('Downloading ticket...');
												const res = await dispatch(downloadEventTicketAction(pInfo.id));
												if (res && res.status === 200) {
													const filename = res.headers['x-filename'] || `download at ${dayjs().format('YYYY-MM-DD h:mm a')}.pdf`;
													const url = window.URL.createObjectURL(new Blob([res.data]));
													const link = document.createElement('a');
													link.href = url;
													link.setAttribute('download', filename);
													document.body.appendChild(link);
													link.click();
													link.parentNode?.removeChild(link);
												}
											} catch {
												messageApi.error('Error downloading ticket');
											} finally {
												messageApi.destroy();
											}
										}}
										className="ab_foot"
									>
										<FilePdfFilled />&nbsp;&nbsp;Download Ticket
									</a>
								</div>
							</li>
						))
					)}
				</ul>
			)}

			{meta && meta.total > 10 && (
				<Pagination
					align="center"
					total={meta?.total}
					showSizeChanger={false}
					style={{ marginTop: '20px' }}
					current={meta?.currentPage}
					onChange={(page) => dispatch(fetchBookedEventsAction({ type: activeTab, page, perPage: meta?.perPage }))}
				/>
			)}
		</>
	);
}

export default BookedEvents;
