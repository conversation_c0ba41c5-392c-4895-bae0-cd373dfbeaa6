'use client'

import { AppInputDate<PERSON>ield, AppInputField, AppInputRadioField } from "@/components/common/AppInputField.component";
import { AppDispatch } from "@/store";
import { triggerConfirmation } from "@/store/slices/app.slice";
import { UserModel } from "@/types/models/user.model";
import { getEncryptedCookie } from "@/utils/cookie.util";
import { CheckCircleFilled, CloseCircleFilled, LockFilled, MailFilled } from "@ant-design/icons";
import { But<PERSON>, Modal } from "antd";
import dayjs from "dayjs";
import { Formik } from "formik";
import { useState } from "react";
import { useDispatch } from "react-redux";
import relativeTime from 'dayjs/plugin/relativeTime';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { ChangeEmailType, ChangePasswordType, UpdatePersonalInfoType } from "@/types/forms/update-user-info.type";
import { changeEmailAction, changePasswordAction, sendTokenAction, updatePersonalInfoAction } from "@/store/slices/user.slice";
import { Errors } from "@/types/index.types";
import { formatNameInput, getFullImageUrl, isEmail, isPhone } from "@/utils/general.util";
import ImageCropper from "@/components/ImageCropper.component";
import { capitalize } from "lodash";

dayjs.extend(relativeTime);
dayjs.extend(advancedFormat);

const Settings = () => {
	const dispatch = useDispatch<AppDispatch>();
	const [showEditEmail, setShowEditEmail] = useState<boolean>(false);
	const [showEditPassword, setShowEditPassword] = useState<boolean>(false);
	const [showEditPersonalInfo, setShowEditPersonalInfo] = useState<boolean>(false);
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const [showTokenInputModal, setShowTokenInputModal] = useState<boolean>(false);
	const [countdown, setCountdown] = useState(0)
	const user = getEncryptedCookie('_eticket_user_') as UserModel;

	const startCountdown = () => {
		setCountdown(30)
		const timer = setInterval(() => {
			setCountdown((prevCount) => {
				if (prevCount <= 1) {
					clearInterval(timer)
					return 0
				}
				return prevCount - 1
			})
		}, 1000)
	}

	const sendToken = async (email: string) => {
		setIsLoading(true);
		const res = await dispatch(sendTokenAction({ old_email: user?.email, new_email: email }));

		if (res && res.status === 200) {
			startCountdown();
			dispatch(triggerConfirmation({ modal: false }));
			if (!showTokenInputModal) setShowTokenInputModal(true);
		}
		setIsLoading(false);
	}

	return <div>
		{showEditPersonalInfo ? (
			<Formik<UpdatePersonalInfoType>
				initialValues={{
					f_name: user?.f_name || '',
					l_name: user?.l_name || '',
					phone: user?.phone || '',
					image: user?.image || '',
					dob: user?.dob || '',
					gender: user?.gender || '',
				}}
				validate={(values) => {
					const errors: Errors<UpdatePersonalInfoType> = {};

					if (values.phone && !isPhone(values.phone, 'mobile')) errors.phone = "Invalid phone number";
					if (values.l_name && !values.f_name) errors.f_name = "First name is required";

					return errors;
				}}
				onSubmit={async (values) => {
					const res = await dispatch(updatePersonalInfoAction(values));
					if (res && res.status === 200) setShowEditPersonalInfo(false);
				}}
			>
				{({ values, handleChange, handleSubmit, errors, isValid, isSubmitting, isValidating }) => (
					<form onSubmit={handleSubmit} className="section-box">
						<div className="header">
							<span>Update Personal Info</span>
						</div>
						<div className="section-form">
							<div className="input-container">
								<label>Profile Image</label>
								<div className="profile-image-section">
									<ImageCropper
										value={values.image as string}
										aspectRatio={1}
										isCircular={true}
										renderUploadTrigger={(openUpload) => (
											<div className="profile-image upload-trigger" onClick={openUpload}>
												<span>Upload</span>
											</div>
										)}
										renderPreview={(resetImage) => (
											<div className="image-preview-container">
												<div className="profile-image">
													<img
														src={getFullImageUrl(values.image || '')}
														alt="User Profile Image"
													/>
												</div>
												<CloseCircleFilled className="clear-icon" onClick={resetImage} />
											</div>
										)}
										onCropComplete={(croppedImageUrl) => {
											handleChange({ target: { value: croppedImageUrl.split('/').at(-1), name: "image" }})
										}}
									/>
								</div>
							</div>

							<AppInputField
								value={values.f_name}
								onChangeInput={(value: string) => {
									const formattedValue = formatNameInput(value);
									handleChange({ target: { value: formattedValue, name: "f_name" }})
								}}
								id="f_name"
								label="First Name"
								placeholder="First Name"
								errorMessage={errors.f_name}
							/>

							<AppInputField
								value={values.l_name}
								onChangeInput={(value: string) => {
									const formattedValue = formatNameInput(value);
									handleChange({ target: { value: formattedValue, name: "l_name" }})
								}}
								id="l_name"
								label="Last Name"
								placeholder="Last Name"
								errorMessage={errors.l_name}
							/>

							<AppInputField
								value={values.phone}
								onChangeInput={(value: string) => handleChange({ target: { value, name: "phone" }})}
								id="phone"
								label="Phone Number"
								placeholder="9845585858"
								errorMessage={errors.phone}
								type="number"
							/>

							<AppInputRadioField
								id="gender"
								label="Gender"
								value={values.gender}
								onChangeInput={(value) => {
									handleChange({ target: { value, name: "gender" }})
								}}
								radioOptions={[
									{ label: 'Male', value: 'male' },
									{ label: 'Female', value: 'female' },
									{ label: 'Others', value: 'others' },
								]}
							/>

							<AppInputDateField
								id="dob"
								label="Date of Birth"
								value={values.dob}
								onChangeInput={(value) => {
									handleChange({ target: { value, name: "dob" }})
								}}
								placeholder="Select Date of Birth"
								errorMessage={errors.dob}
								disabledDate={(current) => current && current.isAfter(dayjs(), "day")}
							/>

							<div className="form-actions">
								<Button
									type="primary"
									disabled={!isValid || isValidating}
									loading={isSubmitting}
									htmlType="submit"
								>
									Save
								</Button>
								<Button
									type="primary"
									disabled={isSubmitting || isValidating}
									onClick={() => {
										setShowEditPersonalInfo(false);
									}}
								>
									Cancel
								</Button>
							</div>
						</div>
					</form>
				)}
			</Formik>
		) : (
			<div className="section-box">
				<div className="header">
					<span>Personal Info</span>
					<Button onClick={() => setShowEditPersonalInfo(true)}>
						<span>Edit</span>
					</Button>
				</div>
				<div className="section-view">
					<div className="section-main-content">
						{user?.f_name && (
							<p>
								<strong>Full Name:</strong> {user?.f_name} {user?.l_name || ''}
							</p>
						)}
						{user?.phone && (
							<p>
								<strong>Phone Number:</strong> {user?.phone}
							</p>
						)}
						{user?.dob && (
							<p>
								<strong>Date of Birth:</strong> {dayjs(user?.dob).format('MMMM Do YYYY')}
							</p>
						)}
						{user?.gender && (
							<p>
								<strong>Gender:</strong> {capitalize(user?.gender)}
							</p>
						)}
					</div>
				</div>
			</div>
		)}

		{showEditEmail ? (
			<Formik<ChangeEmailType>
				initialValues={{
					old_email: user?.email || '',
					new_email: '',
					code: '',
				}}
				validate={(values) => {
					const errors: Errors<ChangeEmailType> = {};

					if (!values.new_email) errors.new_email = 'Please enter your new email'
					else if (!isEmail(values.new_email)) errors.new_email = 'Please enter a valid email'

					if (!values.code && showTokenInputModal) errors.code = 'Please enter the verification code'
					return errors;
				}}
				onSubmit={async (values, actions) => {
					if (!values.old_email) return;

					const res = await dispatch(
						changeEmailAction<ChangeEmailType>(values)
					);
					if (res && res.status === 200) {
						actions.resetForm();
						setShowEditEmail(false);
						setShowTokenInputModal(false);
					}
				}}
			>
				{({ values, handleChange, handleSubmit, errors, resetForm, isSubmitting, isValidating }) => (
					<form className="section-box">
						<div className="header">
							<span>Change Email</span>
						</div>
						<div className="section-form">
							<AppInputField
								value={values.new_email}
								onChangeInput={(value: string) => handleChange({ target: { value, name: "new_email" }})}
								id="new_email"
								label="New Email"
								placeholder="<EMAIL>"
								errorMessage={errors.new_email}
							/>
							<div className="form-actions">
								<Button
									type="primary"
									disabled={isValidating || isSubmitting || !values.new_email || values.new_email === user?.email}
									onClick={() => {
										dispatch(triggerConfirmation({
											modal: true,
											title: 'Change Email?',
											subTitle: <>
												You will need to verify the new email to change your email.
												We will send a verification code to: <strong className="d-block">({values.new_email})</strong>
											</>,
											additionalInfo: "Are you sure you want to continue?",
											btnLabel: 'Continue',
											onBtnClick: () => sendToken(values.new_email),
										}))
									}}
								>
									Save
								</Button>
								<Button
									type="primary"
									disabled={isSubmitting || isValidating}
									onClick={() => {
										setShowEditEmail(false);
										resetForm();
									}}
								>
									Cancel
								</Button>
							</div>
							<Modal
								open={showTokenInputModal}
								closable={false}
								onCancel={() => setShowTokenInputModal(false)}
								title="Enter email verification code"
								okText="Confirm"
								onOk={() => {
									handleSubmit();
								}}
							>
								<div className="d-flex flex-column gap-2">
									<p>
									A verification code has been sent to your new email address <strong>({values.new_email})</strong>.
									Please input the verification code to replace the old email.
									</p>

									<AppInputField
										value={values.code}
										placeholder="verification code"
										onChangeInput={(value) => handleChange({ target: { value, name: "code" }})}
										id="verification_code"
										label={"Verification Code"}
										errorMessage={errors.code}
									/>

									<div>
										<p>Didn't received verification link? {!isLoading && 'You can '}
											{countdown > 0 ? (
												<span className="text-danger">Try again in {countdown}s</span>
											) : isLoading ? (
												<span className="text-primary">Sending...</span>
											) : (
												<span className="text-primary" style={{ cursor: "pointer" }} onClick={() => sendToken(values.new_email)}>
												Try again.
												</span>
											)}
										</p>
									</div>
								</div>
							</Modal>
						</div>
					</form>
				)}
			</Formik>
		) : (
			<div className="section-box">
				<div className="header">
					<span>Email</span>
					<Button onClick={() => setShowEditEmail(true)}>
						<span>Edit</span>
					</Button>
				</div>
				<div className="section-view">
					<div className="section-title-content">
						<div className="icon-avatar">
							<MailFilled />
						</div>
						<strong>{user?.email} <CheckCircleFilled /></strong>
					</div>
					{user?.email_updated_at && (
						<div className="section-main-content">
							<p>
								<strong>Email last updated:</strong> {dayjs(user?.email_updated_at).format('dddd, MMMM Do YYYY, h:mm A')} | {dayjs(user?.email_updated_at).fromNow()}
							</p>
						</div>
					)}
				</div>
			</div>
		)}

		{showEditPassword ? (
			<Formik<ChangePasswordType>
				initialValues={{
					old_password: "",
					new_password: "",
					confirm_password: "",
					email: user?.email || '',
				}}
				validate={(values) => {
					const errors: Errors<ChangePasswordType> = {};
					if (!values.old_password) errors.old_password = "Old password is required";
					if (!values.new_password) errors.new_password = "New password is required";
					if (!values.confirm_password) errors.confirm_password = "Confirm password is required";
					else if (values.new_password !== values.confirm_password)
						errors.confirm_password = "Passwords do not match";
					return errors;
				}}
				onSubmit={async (values, actions) => {
					if (!values.email) return;
					const res = await dispatch(
						changePasswordAction<ChangePasswordType>(values)
					);
					if (res && res.status === 200) {
						actions.resetForm();
						setShowEditPassword(false);
					}
				}}
			>
				{({ values, handleChange, handleSubmit, errors, resetForm, isValid, isSubmitting, isValidating }) => (
					<form onSubmit={handleSubmit} className="section-box">
						<div className="header">
							<span>Change Password</span>
						</div>
						<div className="section-form">
							<AppInputField
								value={values.old_password}
								onChangeInput={(value: string) => handleChange({ target: { value, name: "old_password" }})}
								id="old_password"
								label="Old Password"
								placeholder="Old Password"
								errorMessage={errors.old_password}
							/>
							<AppInputField
								value={values.new_password}
								onChangeInput={(value: string) => handleChange({ target: { value, name: "new_password" }})}
								id="new_password"
								label="New Password"
								placeholder="New Password"
								errorMessage={errors.new_password}
							/>
							<AppInputField
								value={values.confirm_password}
								onChangeInput={(value: string) => handleChange({ target: { value, name: "confirm_password" }})}
								id="confirm_password"
								label="Confirm Password"
								placeholder="Confirm Password"
								errorMessage={errors.confirm_password}
							/>
							<div className="form-actions">
								<Button
									type="primary"
									disabled={!isValid || isValidating}
									loading={isSubmitting}
									htmlType="submit"
								>
									Save
								</Button>
								<Button
									type="primary"
									disabled={isSubmitting || isValidating}
									onClick={() => {
										setShowEditPassword(false);
										resetForm();
									}}
								>
									Cancel
								</Button>
							</div>
						</div>
					</form>
				)}
			</Formik>
		) : (
			<div className="section-box">
				<div className="header">
					<span>Password</span>
					<Button onClick={() => setShowEditPassword(true)}>
						<span>Edit</span>
					</Button>
				</div>
				<div className="section-view">
					<div className="section-title-content">
						<div className="icon-avatar">
							<LockFilled />
						</div>
						<strong>*******************</strong>
					</div>
					{user?.password_updated_at && (
						<div className="section-main-content">
							<p>
								<strong>Password last updated:</strong> {dayjs(user?.password_updated_at).format('dddd, MMMM Do YYYY, h:mm A')} | {dayjs(user?.password_updated_at).fromNow()}
							</p>
						</div>
					)}
				</div>
			</div>
		)}

	</div>
}

export default Settings
