"use client";
import { useState } from "react";
import { UserOutlined, InboxOutlined, PhoneOutlined, LockOutlined } from '@ant-design/icons';
import { Button, DatePicker, Divider, Input, Radio } from "antd";

const Profile = () => {
	const [selectedTab, setSelectedTab] = useState<string>('tab1')
	return <>
		<div className="dashboard-user" style={{ backgroundColor: '#eee' }}>
			<div className="container-fluid-navigation">
				<div className="row">
					<div className="col-sm-9 user-profile-content">
						<div className="dashboard-content">
							<div className="content-spacing">
							</div>
							<div className="content-list full-width-page">
								<div className="tabs-switcher">
									<div onClick={() => {
										setSelectedTab('tab1')
									}} className={'mtab tab-1 ' + (selectedTab === 'tab1' ? ' active' : '')}>
										<i className={'fa fa-user-edit'}/> &nbsp;&nbsp;Update Info
									</div>
									<div onClick={() => {
										setSelectedTab('tab2')
									}} className={'mtab tab-2 ' + (selectedTab === 'tab2' ? ' active' : '')}>
										<i className={'fa fa-lock'}/> &nbsp;&nbsp;Update Password
									</div>
								</div>
								<ul>
									<li>
										<div className="tab-content-info">
											{ selectedTab === 'tab1' ? <div>
												<div className="row">
													<div className="col-6 form-group">
														<label htmlFor="f_name" className={'input-l'}>First name</label>
														<Input
															id={'f_name'}
															className={'app-input-theme'}
															placeholder={'Enter your last name'}
															prefix={<UserOutlined style={{ color: 'rgba(0,0,0,.25)' }}/>}
															// value={data.f_name}
															// onChange={(e) => this.props.userInputChange({ props: 'f_name', value: e.target.value })}
															// ref={node => this.userNameInput = node}
															autoComplete={'off'}
														/>
													</div>
													<div className="col-6 form-group">
														<label htmlFor="l_name" className={'input-l'}>Last name</label>
														<Input
															id={'l_name'}
															placeholder={'Enter your last name'}
															className={'app-input-theme'}
															prefix={<UserOutlined style={{ color: 'rgba(0,0,0,.25)' }}/>}
															// value={data.l_name}
															// onChange={(e) => this.props.userInputChange({ props: 'l_name', value: e.target.value })}
															// ref={node => this.userNameInput = node}
															autoComplete={'off'}
														/>
													</div>

													<div className="col-6 form-group">
														<label htmlFor="email" className={'input-l'}>Email</label>
														<Input
															id={'email'}
															placeholder={'<EMAIL>'}
															className={'app-input-theme'}
															prefix={<InboxOutlined style={{ color: 'rgba(0,0,0,.25)' }}/>}
															// value={data.email}
															// onChange={(e) => this.props.userInputChange({ props: 'email', value: e.target.value })}
															// ref={node => this.userNameInput = node}
															autoComplete={'off'}
														/>
													</div>

													<div className="col-6 form-group">
														<label htmlFor="phone" className={'input-l'}>Phone</label>
														<Input
															id={'phone'}
															placeholder={'98########'}
															className={'app-input-theme'}
															prefix={<PhoneOutlined style={{ color: 'rgba(0,0,0,.25)' }}/>}
															// value={data.phone}
															// onChange={(e) => this.props.userInputChange({ props: 'phone', value: e.target.value })}
															// ref={node => this.userNameInput = node}
															autoComplete={'off'}
														/>
													</div>
													<Divider orientation="right"/>
													<div className="col-6 form-group">
														<label className={'input-l'}>Gender</label>
														<Radio.Group options={['Male', 'Female']} />
													</div>
													<div className="col-6 form-group">
														<label htmlFor="date" className="input-l">Select DOB</label>
														<DatePicker
															style={{ width: '100%' }}
															size='large'
															format='YYYY-MM-DD'
														/>
														{/* <DatePicker id={'date'}
															defaultValue={moment(data.dob, 'YYYY-MM-DD')}
															disabledDate={(currentDate) => {
																return currentDate > new Date()
																// return currentDate && currentDate < moment();
															}}
															onChange={(date, dateString) => {
																this.props.userInputChange({ props: 'dob', value: dateString })
															}}
                                                        /> */}

														<small>Please select your date of birth. The above date is a dummy default value.</small>
														<br/>
													</div>
													<Divider orientation="right"/>
													<div className="col-12">
														<Button type="primary" style={{ float: 'right' }} size={'large'}>Save</Button>
													</div>
												</div>
											</div> : <div>
												<div className="row">
													<div className="col-6 form-group">
														<Input
															id={'old_password'}
															type={'password'}
															className={'app-input-theme'}
															placeholder={'Old password'}
															prefix={<LockOutlined style={{ color: 'rgba(0,0,0,.25)' }}/>}
															// onChange={(e) => this.props.userInputChange({
															// 	props: 'old_password',
															// 	value: e.target.value,
															// 	type: 'password'
															// })}
															autoComplete={'off'}
														/>
													</div>
												</div>
												<div className="row">
													<div className="col-6 form-group">
														<Input
															id={'password'}
															type={'password'}
															className={'app-input-theme'}
															placeholder={'New password'}
															prefix={<LockOutlined style={{ color: 'rgba(0,0,0,.25)' }}/>}
															// onChange={(e) => this.props.userInputChange({
															//     props: 'password',
															//     value: e.target.value,
															//     type: 'password'
															// })}
															autoComplete={'off'}
														/>
													</div>
												</div>
												<div className="row">
													<div className="col-6 form-group">
														<Input
															id={'old_password'}
															type={'password'}
															className={'app-input-theme'}
															placeholder={'Confirm new password'}
															prefix={<LockOutlined style={{ color: 'rgba(0,0,0,.25)' }}/>}
															// onChange={(e) => this.props.userInputChange({
															// 	props: 'password_confirmation',
															// 	value: e.target.value,
															// 	type: 'password'
															// })}
															autoComplete={'off'}
														/>
													</div>
												</div>
												<div className="row">
													<Divider orientation="right"/>
													<div className="col-12">
														<Button type="primary"
															// disabled={!password.old_password || !password.password_confirmation || !password.password}
															// onClick={this.props.updateOldPassword}
															// loading={this.props.user_info.savingState}
															size={'large'}>Update</Button>
													</div>
												</div>
											</div>}
										</div>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

	</>
}
export default Profile;
