'use client'

import AddEvent from "@/components/event/AddEvent.component"
import TableSkeleton from "@/components/common/TableSkeleton"
import { AppDispatch, RootState } from "@/store"
import { triggerConfirmation } from "@/store/slices/app.slice"
import { delayEventAction, deleteEventAction, eventStateChange, fetchAllProtectedEventsAction, fetchEventAction } from "@/store/slices/event.slice"
import { PaginationMeta, ServerStatus } from "@/types/index.types"
import { EventModel } from "@/types/models/event.model"
import { VenueModel } from "@/types/models/venue.model"
import { AccountBookFilled, ClockCircleFilled, DeleteFilled, EditFilled, EyeFilled, FileImageOutlined, MoreOutlined } from "@ant-design/icons"
import { Button, DatePicker,  Dropdown,  Modal,  Pagination,  Table, TableProps, Tabs, Tag } from "antd"
import dayjs from "dayjs"
import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useRouter } from "next-nprogress-bar"
import { getFullImageUrl } from "@/utils/general.util"

interface EventDashboardProps {
  params: {
    slug: string[]
  }
}

const EventDashboard = ({ params }: EventDashboardProps) => {
	const eventId = params.slug ? params.slug.pop() : null;
	const dispatch = useDispatch<AppDispatch>();
	const router = useRouter();

	const { rows, status, meta } = useSelector((state: RootState) => state.event) as { rows: EventModel[], status: ServerStatus, meta: PaginationMeta | null };
	const [showModal, setShowModal] = useState<boolean>(false);
	const [activeTab, setActiveTab] = useState<string>('published');
	const [event, setEvent] = useState<EventModel | null>(null);
	const [showDelayEventModal, setShowDelayEventModal] = useState<boolean>(false);
	const [startDate, setStartDate] = useState<string | null>(null);
	const [endDate, setEndDate] = useState<string | null>(null);

	const getEventPermissions = (record: EventModel) => {
		const now = dayjs();

		const isDeleted = !!record.deleted_at;
		const isPublished = !!record.published_at;
		const isExpired = record.end_date_time ? dayjs(record.end_date_time).isBefore(now) : false;
		const isOnReview = !record.published_at && !record.approved_at && !record.rejected_at && record.status;

		const canEdit = !isPublished && !isDeleted && !isOnReview;

		const canDelete =
		(!isPublished && !isDeleted) ||
		(isPublished && isExpired && !isDeleted);

		return { canEdit, canDelete };
	};

	const columns: TableProps<EventModel>['columns'] = [
		{
			title: '#',
			key: 'index',
			width: '1%',
			align: 'center',
			render: (_text, _record, index) => index + 1,
		},
		{
			title: 'Name',
			dataIndex: 'title',
			key: 'title',
			width: '25%',
			render: (text: string, record: EventModel) => (
				<div className="event-item">
					<div className="event-item__image-container">
						{record.image ? (
							<img
								src={getFullImageUrl(record.image)}
								alt={text || 'Event'}
								className="event-item__image"
							/>
						) : (
							<div className="event-item__placeholder">
								<FileImageOutlined size={20} />
							</div>
						)}
					</div>
					<div className="event-item__content">
						<span className="event-item__title">{text || '-'}</span>
						{record.categories && record.categories.length > 0 && (
							<div className="event-item__categories">
								{record.categories.map((category, index) => (
									<Tag key={index} color="pink" className="event-item__category-tag">{category.name}</Tag>
								))}
							</div>
						)}
					</div>
				</div>
			)
		},
		{
			title: 'Status',
			dataIndex: 'published_at',
			key: 'status',
			width: '15%',
			align: 'left',
			render: (_, record: EventModel) => (
				<>
					{record.published_at && (
						<Tag color="green">Published</Tag>
					)}
					{record.approved_at && (
						<Tag color="blue">Approved</Tag>
					)}
					{record.rejected_at && (
						<Tag color="red">Rejected</Tag>
					)}
					{record.is_delayed && (
						<Tag color="orange">Delayed</Tag>
					)}
					{record.status ? (
						<>
							<Tag color="green">Active</Tag>
							{!record.published_at && !record.approved_at && !record.rejected_at && (
								<Tag color="blue">On Review</Tag>
							)}
						</>
					) : (
						<>
							<Tag color="red">Inactive</Tag>
							{!record.published_at && !record.approved_at && (
								<Tag color="red">Draft</Tag>
							)}
						</>
					)}
					{record.feature_from && record.feature_expiry && (
						<Tag color="purple">Featured</Tag>
					)}
					{record.sliders && record.sliders.length > 0 && (
						<Tag color="orange">Slider</Tag>
					)}
				</>
			)
		},
		{
			title: 'Location',
			dataIndex: 'venues',
			key: 'location',
			width: '15%',
			align: 'left',
			render: (text: VenueModel[]) => (
				<div>
					{text.map((venue, index) => (
						<Tag key={index} color="blue">{venue.name}</Tag>
					))}
				</div>
			)
		},
		{
			title: 'Starts at',
			dataIndex: 'start_date_time',
			key: 'start_date_time',
			width: '10%',
			align: 'right',
			render: (text: string) => (
				<div>{text ? dayjs(text).format('Do MMM YYYY hh:mm A') : '-'}</div>
			)
		},
		{
			title: 'Ends at',
			dataIndex: 'end_date_time',
			key: 'end_date_time',
			width: '10%',
			align: 'right',
			render: (text: string) => (
				<div>{text ? dayjs(text).format('Do MMM YYYY hh:mm A') : '-'}</div>
			)
		},
		{
			title: 'Actions',
			key: 'actions',
			width: '5%',
			align: 'right',
			render: (_text, record: EventModel) => {
				const { canEdit, canDelete } = getEventPermissions(record);

				return (
					<Dropdown
						overlayStyle={{ width: 200 }}
						trigger={['click']}
						menu={{
							items: [
								{
									label: <span className="d-flex align-items-center gap-2"><EyeFilled /> Preview</span>,
									key: 'preview',
									onClick: () => {
										window.open(`/events/${record.slug}-${record.id}`, '_blank', 'noopener,noreferrer');
									},
								},
								{
									label: <span className="d-flex align-items-center gap-2"><EditFilled /> Edit</span>,
									key: 'edit',
									onClick: async () => {
										dispatch(eventStateChange({ key: 'record', value: record }));
										dispatch(eventStateChange({ key: 'processingId', value: record.id }));
										setShowModal(true);
										window.history.replaceState(
											null,
											'',
											`/user/dashboard/events/${record.id}`
										);
									},
									disabled: !canEdit,
								},
								{
									label: <span className="d-flex align-items-center gap-2"><DeleteFilled /> Delete</span>,
									key: 'delete',
									danger: true,
									disabled: !canDelete,
									onClick: () => {
										dispatch(triggerConfirmation({
											modal: true,
											title: 'Delete Event',
											subTitle: <>Are you sure you want to delete <strong>{record.title}</strong> event?</>,
											additionalInfo: 'Deleted Event will not be visible to other users.',
											btnLabel: 'Delete Now',
											onBtnClick: async () => {
												await dispatch(deleteEventAction(record.id));
												await dispatch(fetchAllProtectedEventsAction({ type: activeTab }));
											}
										}))
									}
								},
								...((record.published_at && record.approved_at) ? [
									{
										label: <span className="d-flex align-items-center gap-2"><AccountBookFilled /> View Purchases</span>,
										key: 'view-purchases',
										onClick: () => {
											dispatch(eventStateChange({ key: 'record', value: record }));
											router.push(`/user/dashboard/purchases/${record.id}`);
										},
									},
									{
										label: <span className="d-flex align-items-center gap-2"><ClockCircleFilled /> Delay Event</span>,
										key: 'delay-event',
										onClick: () => {
											setEvent(record);
											setShowDelayEventModal(true);
										},
									}
								] : []),
							]
						}}
					>
						<MoreOutlined />
					</Dropdown>
				);
			}
		},
	];

	useEffect(() => {
		if (!showModal) dispatch(fetchAllProtectedEventsAction({ type: activeTab, page: meta?.currentPage || 1, perPage: 10 }));
		if (eventId) {
			dispatch(fetchEventAction(Number(eventId)))
				.then((res) => {
					if (res && res.status === 200) {
						if (res.data.published_at) {
							window.history.replaceState(
								null,
								'',
								'/user/dashboard/events'
							);
						} else setShowModal(true);
					}
				});
		}
	}, [dispatch, eventId, activeTab, showModal])

	return <>
		<div className="page-intro-header">
			<div className="header-title">
				<h1 className="titled-info">Events List</h1>
				<p>Your all events</p>
			</div>

			<div className="action-button-container">
				<Button onClick={() => {
					dispatch(eventStateChange({ key: 'record', value: null }));
					dispatch(eventStateChange({ key: 'processingId', value: null }));
					setShowModal(true)
				}}>
					Add Event
				</Button>
			</div>
		</div>

		<div className="dashboard-main-content">
			<Tabs
				activeKey={activeTab}
				items={[
					{
						key: 'published',
						label: 'Published Events',
					},
					{
						key: 'on-review',
						label: 'On Review',
					},
					{
						key: 'draft',
						label: 'Draft',
					},
					{
						key: 'expired',
						label: 'Expired',
					},
					{
						key: 'deleted',
						label: 'Deleted',
					},
				]}
				onChange={(key) => {
					if (status === 'fetching') return;
					setActiveTab(key);
				}}
			/>
			<div className="tabled-content">
				{status === 'fetching' ? (
					<Table<EventModel>
						rowKey="id"
						columns={columns}
						dataSource={[]}
						rowClassName={() => 'custom-table-row'}
						className="dashboard-table"
						size='small'
						pagination={false}
						locale={{
							emptyText: <TableSkeleton length={10} />
						}}
					/>
				) : (
					<Table<EventModel>
						rowKey="id"
						columns={columns}
						dataSource={rows}
						rowClassName={() => 'custom-table-row'}
						size='small'
						pagination={false}
						locale={{
							emptyText: <div>No {activeTab} events found. Add events to get started.</div>
						}}
					/>
				)}
				{(meta && meta.total > 10) && (
					<Pagination
						align="center"
						total={meta?.total}
						showSizeChanger={false}
						disabled={status === 'fetching'}
						style={{ marginTop: '20px' }}
						current={meta?.currentPage}
						onChange={(page) => dispatch(fetchAllProtectedEventsAction({ page, perPage: meta?.perPage }))}
					/>
				)}
			</div>

		</div>

		<AddEvent showModal={showModal} setShowModal={setShowModal} />

		{event && (
			<Modal
				title={
					<div style={{ color: '#c71782', fontSize: 24 }}>
						Delay Your Event
					</div>
				}
				open={showDelayEventModal}
				onCancel={() => {
					setStartDate(null);
					setEndDate(null);
					setEvent(null);
					setShowDelayEventModal(false);
				}}
				footer={[
					<Button key="cancel"
						onClick={() => {
							setStartDate(null);
							setEndDate(null);
							setEvent(null);
							setShowDelayEventModal(false)
						}}
					>
						Cancel
					</Button>,
					<Button
						type="primary"
						disabled={!startDate || !endDate}
						onClick={() => {
							if (!startDate || !endDate) return;
							dispatch(triggerConfirmation({
								modal: true,
								title: 'Delay Event',
								subTitle: <>Are you sure you want to delay <strong>{event.title}</strong> event?</>,
								additionalInfo: 'This action cannot be undone.',
								btnLabel: 'Confirm',
								onBtnClick: async () => {
									const res = await dispatch(delayEventAction(event.id, startDate, endDate));
									if (res && res.status === 200) {
										setShowDelayEventModal(false);
										setEvent(null);
										setStartDate(null);
										setEndDate(null);
										dispatch(fetchAllProtectedEventsAction({ type: activeTab, page: meta?.currentPage, perPage: meta?.perPage }));
									}
									return res;
								}
							}))
						}}
					>
						Delay Event
					</Button>,
				]}
				zIndex={999}
			>
				<p>Please select the new start and end date time for the event:</p>
				<p style={{ color: '#c71782', fontWeight: 'bold' }}>{event.title}</p>
				<div className="container mt-4">
					<div className="row">
						<div className="col-md-12">
							<div className="mb-3">
								<label className="font-weight-bold" htmlFor="startDate">
									Start Date Time
								</label>
								<DatePicker
									id="startDate"
									className="w-100"
									showTime={true}
									value={startDate ? dayjs(startDate) : null}
									onChange={(value: dayjs.Dayjs | null) => {
										if (value) {
											setStartDate(value.toISOString());
											if (endDate && value.isAfter(dayjs(endDate))) {
												setEndDate(null);
											}
										} else {
											setStartDate(null);
										}
									}}
									disabledDate={(current) => {
										if (!current) return false;

										const eventStart = event?.start_date_time ? dayjs(event.start_date_time).endOf('day') : null;
										const selectedEndDate = endDate ? dayjs(endDate).endOf('day') : null;

										return (
											current.isBefore(dayjs().startOf('day')) ||
											(!!eventStart && current.isBefore(eventStart)) ||
											(!!selectedEndDate && current.isAfter(selectedEndDate))
										);
									}}
									disabledTime={(current) => {
										const selectedEndDate = endDate ? dayjs(endDate) : null;
										if (!current || !selectedEndDate) return {};

										if (current.isSame(endDate, 'day')) {
											// Disable hours >= end time hour
											return {
												disabledHours: () => Array.from({ length: 24 }, (_, i) => i).filter(hour => hour > selectedEndDate.hour()),
												disabledMinutes: (selectedHour) => {
													if (selectedHour === selectedEndDate.hour()) {
														return Array.from({ length: 60 }, (_, i) => i).filter(min => min >= selectedEndDate.minute());
													}
													return [];
												},
											};
										}
										return {};
									}}
									format={'MMM D, YYYY - h:mm A'}
									aria-describedby="startDateHelp"
								/>
								<small id="startDateHelp" className="form-text text-muted">
									Date from which the event will start
								</small>
							</div>
							<div className="mb-3">
								<label className="font-weight-bold" htmlFor="endDate">
									End Date Time
								</label>
								<DatePicker
									id="endDate"
									className="w-100"
									showTime={true}
									value={endDate ? dayjs(endDate) : null}
									onChange={(value: dayjs.Dayjs | null) => {
										if (value) {
											setEndDate(value.toISOString());
											if (startDate && value.isBefore(dayjs(startDate))) {
												setStartDate(null); // Reset fromDate if toDate is before it
											}
										} else {
											setEndDate(null);
										}
									}}
									disabledDate={(current) => {
										if (startDate) {
											return current && current.isBefore(dayjs(startDate).startOf('day'));
										}
										return current && current.isBefore(dayjs().startOf('day'));
									}}
									disabledTime={(current) => {
										const selectedStartDate = startDate ? dayjs(startDate) : null;
										if (!current || !selectedStartDate) return {};

										if (current.isSame(startDate, 'day')) {
											// Disable hours < start time hour
											return {
												disabledHours: () => Array.from({ length: 24 }, (_, i) => i).filter(hour => hour < selectedStartDate.hour()),
												disabledMinutes: (selectedHour) => {
													if (selectedHour === selectedStartDate.hour()) {
														return Array.from({ length: 60 }, (_, i) => i).filter(min => min <= selectedStartDate.minute());
													}
													return [];
												},
											};
										}
										return {};
									}}
									format={'MMM D, YYYY - h:mm A'}
									aria-describedby="endDateHelp"
								/>
								<small id="endDateHelp" className="form-text text-muted">
									Date at which the event will end
								</small>
							</div>
						</div>
					</div>
				</div>
			</Modal>
		)}
	</>

}

export default EventDashboard
