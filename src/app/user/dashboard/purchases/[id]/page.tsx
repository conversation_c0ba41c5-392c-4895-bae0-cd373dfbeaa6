'use client'

import TableSkeleton from "@/components/common/TableSkeleton"
import { AppDispatch, RootState } from "@/store"
import { fetchEventAction } from "@/store/slices/event.slice"
import { downloadPurchasesExcelAction, fetchEventPurchasesAction } from "@/store/slices/purchases.slice"
import { ServerStatus, PaginationMeta } from "@/types/index.types"
import { EventModel } from "@/types/models/event.model"
import { Button, message, Pagination, Table, TableProps } from "antd"
import dayjs from "dayjs"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { capitalize } from "lodash"
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { TicketBuyerModel } from "@/types/models/ticket-buyer.model"
import { DownloadOutlined } from "@ant-design/icons"

dayjs.extend(advancedFormat);

type PurchasesPageProps = {
  params: {
    id: string
  }
}

const PurchasesPage = ({ params }: PurchasesPageProps) => {
	const { id: eventId } = params;

	const dispatch = useDispatch<AppDispatch>();
	const [messageApi, contextHolder] = message.useMessage();

	const { status, rows, meta } = useSelector((state: RootState) => state.purchases) as {
		status: ServerStatus;
		rows: TicketBuyerModel[];
		meta: PaginationMeta;
	}
	const { record: eventRecord } = useSelector((state: RootState) => state.event) as {
		record: EventModel;
	}

	const columns: TableProps<TicketBuyerModel>['columns'] = [
		{
			title: '#',
			key: 'index',
			width: '1%',
			align: 'center',
			render: (_text, _record, index) => index + 1,
		},
		{
			title: 'Buyer Name',
			key: 'buyer_name',
			dataIndex: 'full_name',
			width: '10%',
			render: (text: string) => (
				<div>
					{text || '-'}
				</div>
			)
		},
		{
			title: 'Buyer Phone',
			key: 'buyer_phone',
			dataIndex: 'phone_number',
			width: '10%',
			render: (text: string) => (
				<div>
					{text || '-'}
				</div>
			)
		},
		{
			title: 'Buyer Email',
			key: 'buyer_email',
			dataIndex: 'email',
			width: '10%',
			render: (text: string) => (
				<div>
					{text || '-'}
				</div>
			)
		},
		{
			title: 'Payment Method',
			dataIndex: 'payment_method',
			key: 'payment_method',
			width: '5%',
			render: (text: string) => (
				<div>
					{text ? capitalize(text) : '-'}
				</div>
			)
		},
		{
			title: 'Ticket Count',
			dataIndex: 'ticket_count',
			key: 'ticket_count',
			width: '5%',
			render: (text: string) => (
				<div>
					{text || '-'}
				</div>
			)
		},
		{
			title: 'Paid Amount',
			dataIndex: 'total_amount',
			key: 'paid_amount',
			width: '10%',
			render: (text: string) => (
				<div>
					{text ? `NRs. ${text}` : '-'}
				</div>
			)
		},
		{
			title: 'Bought At',
			dataIndex: 'created_at',
			key: 'bought_at',
			width: '15%',
			align: 'left',
			render: (text: string) => (
				<div>
					<p>{dayjs(text).format('Do MMM YYYY h:mm A')}</p>
				</div>
			)
		},
	];

	useEffect(() => {
		if (eventId) {
			if (!eventRecord) dispatch(fetchEventAction(Number(eventId)));
			dispatch(fetchEventPurchasesAction({ id: Number(eventId), page: 1, perPage: 10 }));
		}
	}, [eventId, dispatch]);

	return (
		<>
			{contextHolder}
			<div className="page-intro-header">
				<div className="header-title">
					<h1 className="titled-info">Event Ticket Purchases List - <strong style={{ color: '#c71782' }}>{eventRecord?.title}</strong></h1>
				</div>

				<div className="action-button-container">
					<Button
						type="primary"
						onClick={async () => {
							if (status === 'downloading') return;
							try {
								messageApi.loading({ content: 'Downloading Report...', key: 'download-report', duration: 0 });
								const res = await dispatch(downloadPurchasesExcelAction(Number(eventId)));
								if (res && res.status === 200) {
									const filename = res.headers['x-filename'] || `${eventRecord?.title} Purchases Report.xlsx`;
									const url = window.URL.createObjectURL(new Blob([res.data]));
									const link = document.createElement('a');
									link.href = url;
									link.setAttribute('download', filename);
									document.body.appendChild(link);
									link.click();
									link.parentNode?.removeChild(link);
									messageApi.success({ content: 'Report downloaded successfully', key: 'download-report' });
								}
							} catch {
								messageApi.error({ content: 'Error downloading report', key: 'download-report' });
							}
						}}
						icon={<DownloadOutlined />}
						loading={status === 'downloading'}
					>
						Download Report
					</Button>
				</div>
			</div>

			<div className="dashboard-main-content">
				<div className="tabled-content">
					{status === 'fetching' ? (
						<Table<TicketBuyerModel>
							rowKey="id"
							columns={columns}
							dataSource={[]}
							rowClassName={() => 'custom-table-row'}
							className="dashboard-table"
							size='small'
							pagination={false}
							locale={{
								emptyText: <TableSkeleton length={10} />
							}}
						/>
					) : (
						<Table<TicketBuyerModel>
							rowKey="id"
							columns={columns}
							dataSource={rows}
							rowClassName={() => 'custom-table-row'}
							size='small'
							pagination={false}
							locale={{
								emptyText: <div>No ticket purchases available for this event.</div>
							}}
						/>
					)}
					{(meta && meta.total > 10) && (
						<Pagination
							align="center"
							total={meta?.total}
							showSizeChanger={false}
							disabled={status === 'fetching'}
							style={{ marginTop: '20px' }}
							current={meta?.currentPage}
							onChange={(page) => dispatch(fetchEventPurchasesAction({ id: Number(eventId), page, perPage: meta?.perPage }))}
						/>
					)}
				</div>
			</div>

		</>
	);
}

export default PurchasesPage;
