'use client';

import {
	DashOutlined, FileFilled, HomeFilled, InfoCircleFilled,
	LayoutFilled, ProductFilled, ProfileFilled, SettingFilled,
	UsergroupAddOutlined
} from "@ant-design/icons";
import { Menu, MenuProps } from "antd";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";
import { getEncryptedCookie } from "@/utils/cookie.util";
import { UserModel } from "@/types/models/user.model";
import dayjs from "dayjs";
import AuthLink from "@/components/common/AuthLink.component";
import FullscreenFallback from "@/components/FullScreenFallback.component";
import { NormalUserInfo } from "@/components/dashboard/NormalUserInfo.component";
import { NormalUserNav } from "@/components/dashboard/NormalUserNav.component";
import { getFullImageUrl } from "@/utils/general.util";

const routeToKeyMap: Record<string, string> = {
	categories: 'category',
	artists: 'artist',
	venues: 'venue',
	topics: 'topic',
	userlog: 'userLogs',
	'general-info': 'general-info',
	events: 'events',
	settings: 'settings',
};

export default function DashboardLayout({ children }: { children: ReactNode }) {
	const pathname = usePathname();
	const user = getEncryptedCookie('_eticket_user_') as UserModel;
	const company = user?.is_partner ? user?.companies?.[0] : null;

	const getSelectedKeyFromPath = (): string => {
		const segments = pathname.split('/').filter(Boolean);
		const route = segments[segments.length - 1];
		return routeToKeyMap[route] || 'dashboard';
	};

	const menuItems: MenuProps['items'] = [
		{
			key: 'dashboard',
			icon: <LayoutFilled />,
			label: <AuthLink href="/user/dashboard">Dashboard</AuthLink>,
		},
		...(user?.user_type === 'admin' ? [
			{
				key: 'category',
				icon: <ProductFilled />,
				label: <AuthLink href="/user/dashboard/categories">Categories</AuthLink>,
			},
			{
				key: 'artist',
				icon: <UsergroupAddOutlined />,
				label: <AuthLink href="/user/dashboard/artists">Artists</AuthLink>,
			},
			{
				key: 'venue',
				icon: <HomeFilled />,
				label: <AuthLink href="/user/dashboard/venues">Venues</AuthLink>,
			},
			{
				key: 'topic',
				icon: <ProfileFilled />,
				label: <AuthLink href="/user/dashboard/topics">Topics</AuthLink>,
			},
			{
				key: 'userLogs',
				icon: <FileFilled />,
				label: <AuthLink href="/user/dashboard/userlog">User Logs</AuthLink>,
			},
			{
				key: 'general-info',
				icon: <InfoCircleFilled />,
				label: <AuthLink href="/user/dashboard/general-info">General Info</AuthLink>,
			},
		] : []),
		...(user?.user_type === 'normal' ? [
			{
				key: 'events',
				icon: <ProductFilled />,
				label: <AuthLink href="/user/dashboard/events">Events</AuthLink>,
			},
			{
				key: 'settings',
				icon: <SettingFilled />,
				label: <AuthLink href="/user/dashboard/settings">Settings</AuthLink>,
			},
		] : [])
	];

	if (!user) return <FullscreenFallback />;

	if (user?.user_type === 'normal' && !user?.is_partner) {
		return (
			<div>
				<div className="dashboard-cover">
					<div className="notify-popover">
						<div className="arrow" style={{ top: '16px' }} />
					</div>
				</div>
				<div className="dashboard-user" style={{ backgroundColor: '#eee' }}>
					<div className="container-fluid-navigation">
						<div className="row">
							<NormalUserInfo user={user} />
							<div className="col-sm-9 user-profile-content">
								<NormalUserNav />
								<div className="dashboard-content">
									<div className="content-spacing" />
									<div className="content-list">
										{children}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Admin or Partner layout
	return (
		<div className="container-fluid-navigation">
			<div className="dashboard_user_partner">
				<div className="dash_side_nav">
					<div className="intro_text">
						<h1>Welcome</h1>
						<p>{user.user_type === 'admin' ? 'Admin' : 'Partner'} Dashboard</p>
					</div>
					<div className="nav_container">
						<Menu
							style={{ width: 256 }}
							mode="inline"
							items={menuItems}
							selectedKeys={[getSelectedKeyFromPath()]}
						/>
					</div>
					<div className="user_profile_present">
						{!user?.is_partner ? (
							<>
								<div className="profile_img">
									<img
										src={user?.image ? getFullImageUrl(user.image) : '/images/user_avatar.jpg'}
										alt="Profile Image"
									/>
								</div>
								<div className="user_info">
									<h1>{user.f_name ? `${user.f_name} ${user.l_name || ''}` : <DashOutlined />}</h1>
									<p>
										<strong>Last Login:</strong> {!user?.last_logged_in_at ? '-' : (
											<>
												<br />
												{dayjs(user.last_logged_in_at).format('DD MMM YYYY, hh:mm A')}
											</>
										)}
									</p>
								</div>
							</>
						) : (
							<>
								<div className="profile_img">
									<img
										src={company?.logo ? getFullImageUrl(company.logo) : '/images/user_avatar.jpg'}
										alt="Company Logo"
									/>
								</div>
								<div className="user_info">
									<h1>{company?.name || <DashOutlined />}</h1>
									<p>
										<strong>Last Login:</strong> {!user?.last_logged_in_at ? '-' : (
											<>
												<br />
												{dayjs(user.last_logged_in_at).format('DD MMM YYYY, hh:mm A')}
											</>
										)}
									</p>
								</div>
							</>
						)}
					</div>
				</div>
				<div className="dashboard-content">
					{children}
				</div>
			</div>
		</div>
	);
}
