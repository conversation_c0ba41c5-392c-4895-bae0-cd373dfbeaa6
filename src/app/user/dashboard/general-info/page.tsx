'use client'

import { AppInputField, AppInputTagField, AppInputTextField } from "@/components/common/AppInputField.component";
import { AppDispatch, RootState } from "@/store";
import { fetchGeneralInfoAction, updateGeneralInfoAction } from "@/store/slices/general-info.slice";
import { UpdateGeneralInfoType } from "@/types/forms/update-general-info.type";
import { GeneralInfoModal } from "@/types/models/general-info.model";
import { updateGeneralInfoValidation } from "@/utils/validation.util";
import { Button } from "antd";
import { Formik } from "formik";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

const GeneralInfo = () => {
	const dispatch = useDispatch<AppDispatch>();
	const { record: generalInfo } = useSelector((state: RootState) => state.generalInfo) as { record: GeneralInfoModal };

	useEffect(() => {
		dispatch(fetchGeneralInfoAction());
	}, [dispatch]);

	return (
		<Formik<UpdateGeneralInfoType>
			enableReinitialize={true}
			initialValues={{
				name: generalInfo?.name || '',
				app_info: generalInfo?.app_info || '',
				address_one: generalInfo?.address_one || '',
				address_two: generalInfo?.address_two || '',
				phone_one: generalInfo?.phone_one || '',
				phone_two: generalInfo?.phone_two || '',
				mobile_one: generalInfo?.mobile_one || '',
				mobile_two: generalInfo?.mobile_two || '',
				email_one: generalInfo?.email_one || '',
				email_two: generalInfo?.email_two || '',
				site_url: generalInfo?.site_url || '',
				copyright: generalInfo?.copyright || '',
				yt_channel: generalInfo?.yt_channel || '',
				facebook: generalInfo?.facebook || '',
				twitter: generalInfo?.twitter || '',
				linkedin: generalInfo?.linkedin || '',
				instagram: generalInfo?.instagram || '',
				google: generalInfo?.google || '',
				pinterest: generalInfo?.pinterest || '',
				meta_desc: generalInfo?.meta_desc || '',
				meta_keywords: generalInfo?.meta_keywords ? generalInfo.meta_keywords.split(',') : [],
			}}
			validate={(data) => {
				return updateGeneralInfoValidation(data);
			}}
			onSubmit={async (values, actions) => {
				actions.setSubmitting(false);
				const res = await dispatch(updateGeneralInfoAction<UpdateGeneralInfoType>(values));
				if (res && res.status === 200) {
					actions.setSubmitting(false);
					dispatch(fetchGeneralInfoAction());
				}
			}}
		>
			{({ values, errors, handleSubmit, setFieldValue, isValid, isValidating, isSubmitting, dirty }) => (
				<div className="company-info-form">
					<div className="form-header">
						<h1>General Info</h1>
					</div>

					<form>
						<div className="container">
							<div className="row">
								<div className="col-md-12">
									<AppInputField
										id="app_name"
										value={values.name}
										label={'App Name'}
										autoFocus={true}
										onChangeInput={(value: string) => setFieldValue('name', value)}
										placeholder={'My Awesome App'}
									/>
								</div>

								<div className="col-md-12">
									<AppInputTextField
										id="app_info"
										value={values.app_info}
										onChangeInput={(value: string) => setFieldValue('app_info', value)}
										label={'App Info'}
										placeholder={'App info about your company'}
										rows={6}
										maxLength={500}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="address_one"
										value={values.address_one}
										label={'Address One'}
										onChangeInput={(value: string) => setFieldValue('address_one', value)}
										placeholder={'123, Main Street, Anytown, USA'}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="address_two"
										value={values.address_two}
										label={'Address two'}
										onChangeInput={(value: string) => setFieldValue('address_two', value)}
										placeholder={'123, Main Street, Anytown, USA'}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="phone_one"
										value={values.phone_one}
										type="number"
										label={'Phone One'}
										onChangeInput={(value: string) => setFieldValue('phone_one', value)}
										placeholder={'014444444'}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="phone_two"
										value={values.phone_two}
										type="number"
										label={'Phone Two'}
										onChangeInput={(value: string) => setFieldValue('phone_two', value)}
										placeholder={'014444444'}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="mobile_one"
										value={values.mobile_one}
										type="number"
										label={'Mobile One'}
										onChangeInput={(value: string) => setFieldValue('mobile_one', value)}
										placeholder={'9818000000'}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="mobile_two"
										value={values.mobile_two}
										type="number"
										label={'Mobile Two'}
										onChangeInput={(value: string) => setFieldValue('mobile_two', value)}
										placeholder={'9818000000'}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="email_one"
										value={values.email_one}
										onChangeInput={(value: string) => setFieldValue('email_one', value)}
										label={'Email One'}
										placeholder={'<EMAIL>'}
										errorMessage={errors.email_one as string}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="email_two"
										value={values.email_two}
										onChangeInput={(value: string) => setFieldValue('email_two', value)}
										label={'Email Two'}
										placeholder={'<EMAIL>'}
										errorMessage={errors.email_two as string}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="site_url"
										value={values.site_url}
										onChangeInput={(value: string) => setFieldValue('site_url', value)}
										label={'Site URL'}
										placeholder={'https://www.example.com'}
										errorMessage={errors.site_url as string}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="copyright"
										value={values.copyright}
										onChangeInput={(value: string) => setFieldValue('copyright', value)}
										label={'Copyright'}
										placeholder={'Copyright © 2025 Your Company'}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="yt_channel"
										value={values.yt_channel}
										onChangeInput={(value: string) => setFieldValue('yt_channel', value)}
										label={'Youtube Channel'}
										placeholder={'https://www.youtube.com/channel/channel-id'}
										errorMessage={errors.yt_channel as string}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="facebook"
										value={values.facebook}
										onChangeInput={(value: string) => setFieldValue('facebook', value)}
										label={'Facebook'}
										placeholder={'https://www.facebook.com/your-page'}
										errorMessage={errors.facebook as string}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="twitter"
										value={values.twitter}
										onChangeInput={(value: string) => setFieldValue('twitter', value)}
										label={'Twitter'}
										placeholder={'https://www.x.com/your-page'}
										errorMessage={errors.twitter as string}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="linkedin"
										value={values.linkedin}
										onChangeInput={(value: string) => setFieldValue('linkedin', value)}
										label={'Linkedin'}
										placeholder={'https://www.linkedin.com/in/your-page'}
										errorMessage={errors.linkedin as string}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="instagram"
										value={values.instagram}
										onChangeInput={(value: string) => setFieldValue('instagram', value)}
										label={'Instagram'}
										placeholder={'https://www.instagram.com/your-page'}
										errorMessage={errors.instagram as string}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="google"
										value={values.google}
										onChangeInput={(value: string) => setFieldValue('google', value)}
										label={'Google'}
										placeholder={'https://www.google.com/your-page'}
										errorMessage={errors.google as string}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="pinterest"
										value={values.pinterest}
										onChangeInput={(value: string) => setFieldValue('pinterest', value)}
										label={'Pinterest'}
										placeholder={'https://www.pinterest.com/your-page'}
										errorMessage={errors.pinterest as string}
									/>
								</div>

								<div className='col-md-12'>
									<AppInputTagField
										label='Meta Keywords (minimum 3 keywords)'
										id='meta_keywords'
										value={values.meta_keywords}
										onChangeInput={(value: string[]) => {
											setFieldValue('meta_keywords', value);
										}}
										placeholder={'New Keyword'}
										errorMessage={errors.meta_keywords as string}
									/>
								</div>

								<div className="col-md-12">
									<AppInputTextField
										id="meta_description"
										value={values.meta_desc}
										onChangeInput={(value: string) => {
											setFieldValue('meta_desc', value);
										}}
										label={'Meta Description (min 50 characters / max 500 characters)'}
										placeholder={'Meta Description for the app ....'}
										rows={6}
										errorMessage={errors.meta_desc}
										maxLength={500}
									/>
								</div>
							</div>

							<div className="submit-btn">
								<Button
									type="primary"
									size="large"
									onClick={() => handleSubmit()}
									disabled={!isValid || isValidating || !dirty}
									loading={isSubmitting}
								>
									Update Info
								</Button>
							</div>
						</div>
					</form>
				</div>

			)}
		</Formik>
	)
}

export default GeneralInfo
