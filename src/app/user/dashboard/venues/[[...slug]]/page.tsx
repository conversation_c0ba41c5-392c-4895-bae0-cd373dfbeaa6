'use client'

import AddEditVenue from "@/components/venue/AddEditVenue.component";
import TableSkeleton from "@/components/common/TableSkeleton";
import { AppDispatch, RootState } from "@/store";
import { triggerConfirmation } from "@/store/slices/app.slice";
import { venueStateChange, deleteVenueAction, fetchAllVenuesAction, fetchVenueAction, toggleVenueStatusAction } from "@/store/slices/venue.slice";
import { PaginationMeta, ServerStatus } from "@/types/index.types";
import { VenueModel } from "@/types/models/venue.model";
import { AddressData } from "@/types/index.types";
import { CheckCircleFilled, DeleteFilled, EditFilled, FileImageOutlined, MoreOutlined, PlusOutlined, StopFilled } from "@ant-design/icons";
import { TableProps, Tag, Button, Table, Dropdown, Pagination } from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getFullImageUrl } from "@/utils/general.util";

interface VenuesPageProps {
  params: {
    slug: string[]
  }
}

const VenuesPage = ({ params }: VenuesPageProps) => {
	const venueId = params.slug ? params.slug.pop() : null;
	const dispatch = useDispatch<AppDispatch>();
	const [showModal, setShowModal] = useState<boolean>(false);
	const { rows, meta, status } = useSelector((state: RootState) => state.venue) as { rows: VenueModel[], meta: PaginationMeta | null, status: ServerStatus };

	const columns: TableProps<VenueModel>['columns'] = [
		{
			title: '#',
			key: 'index',
			width: '1%',
			align: 'center',
			render: (_text, _record, index) => index + 1,
		},
		{
			title: 'Name',
			dataIndex: 'name',
			key: 'name',
			width: '25%',
			render: (text: string, record: VenueModel) => (
				<div className="event-item">
					<div className="event-item__image-container">
						{record.image ? (
							<img
								src={getFullImageUrl(record.image)}
								alt={text || 'Venue'}
								className="event-item__image"
							/>
						) : (
							<div className="event-item__placeholder">
								<FileImageOutlined size={20} />
							</div>
						)}
					</div>
					<div className="event-item__content">
						<span className="event-item__title">{text || '-'}</span>
					</div>
				</div>
			)
		},
		{
			title: 'Address',
			dataIndex: 'address',
			key: 'address',
			width: '15%',
			align: 'left',
			render: (text: AddressData) => (
				<div>
					{text?.description || '-'}
				</div>
			)
		},
		{
			title: 'Status',
			dataIndex: 'status',
			key: 'status',
			width: '10%',
			align: 'left',
			render: (text) => (
				<Tag color={text ? 'green' : 'red'}>{text ? 'Active' : 'Inactive'}</Tag>
			)
		},
		{
			title: 'Created At',
			dataIndex: 'created_at',
			key: 'created_at',
			width: '10%',
			align: 'left',
			render: (text: string) => (
				<div>
					<p>{dayjs(text).format('Do MMM YYYY')}</p>
					<span style={{ fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)' }}>
						{dayjs(text).fromNow()}
					</span>
				</div>
			)
		},
		{
			title: 'Actions',
			key: 'actions',
			width: '5%',
			align: 'right',
			render: (_text, record: VenueModel) => (
				<Dropdown
					overlayStyle={{ width: 200 }}
					trigger={['click']}
					menu={{
						items: [
							{
								label: <span className="d-flex align-items-center gap-2">
									<EditFilled /> <span>Edit</span>
								</span>,
								key: 'edit',
								onClick: async () => {
									await dispatch(venueStateChange({ key: 'record', value: record }));
									setShowModal(true);
									window.history.replaceState(
										null,
										'',
										`/user/dashboard/venues/${record.id}`
									);
								},
							},
							{
								label: record.status ? (
									<span className="d-flex align-items-center gap-2">
										<StopFilled /> <span>Deactivate</span>
									</span>
								) : (
									<span className="d-flex align-items-center gap-2">
										<CheckCircleFilled /> <span>Activate</span>
									</span>
								),
								key: 'status',
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: `${record.status ? 'Deactivate' : 'Activate'} Venue`,
										subTitle: <>Are you sure you want to {record.status ? 'deactivate' : 'activate'} <strong>{record.name}</strong> venue?</>,
										additionalInfo: `${record.status ? 'Deactivated' : 'Activated'} venue will ${record.status ? 'not be' : 'be'} visible to other users.`,
										btnLabel: `${record.status ? 'Deactivate' : 'Activate'} Now`,
										onBtnClick: async () => {
											const res = await dispatch(toggleVenueStatusAction(record.id, !record.status));
											if (res && res.status === 200) {
												await dispatch(fetchAllVenuesAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							},
							{
								label: <span className="d-flex align-items-center gap-2">
									<DeleteFilled /> <span>Delete</span>
								</span>,
								key: 'delete',
								danger: true,
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: 'Delete Venue',
										subTitle: <>Are you sure you want to delete <strong>{record.name}</strong> venue?</>,
										additionalInfo: 'Deleted Venue will not be visible to other users.',
										btnLabel: 'Delete Now',
										onBtnClick: async () => {
											const res = await dispatch(deleteVenueAction(record.id));
											if (res && res.status === 200) {
												await dispatch(fetchAllVenuesAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							}
						]
					}}
				>
					<MoreOutlined />
				</Dropdown>
			)
		},
	];

	useEffect(() => {
		if (!showModal) dispatch(fetchAllVenuesAction({ page: meta?.currentPage || 1, perPage: 10 }));
		if (venueId) {
			dispatch(fetchVenueAction(Number(venueId)))
				.then((res) => {
					if (res && res.status === 200) setShowModal(true);
				});
		}
	}, [dispatch, venueId, showModal])

	return (
		<>
			<div className="page-intro-header">
				<div className="header-title">
					<h1 className="titled-info">Venues List</h1>
					<p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Alias cupiditate et facere</p>
				</div>

				<div className="action-button-container">
					<Button onClick={() => setShowModal(true)} icon={<PlusOutlined />}>Add Venue</Button>
				</div>
			</div>

			<div className="dashboard-main-content">
				<div className="tabled-content">
					{status === 'fetching' ? (
						<Table<VenueModel>
							rowKey="id"
							columns={columns}
							dataSource={[]}
							rowClassName={'custom-table-row'}
							className="dashboard-table"
							size='small'
							pagination={false}
							locale={{
								emptyText: <TableSkeleton length={10} />
							}}
						/>
					) : (
						<Table<VenueModel>
							rowKey="id"
							columns={columns}
							dataSource={rows}
							rowClassName={'custom-table-row'}
							size='small'
							pagination={false}
							locale={{
								emptyText: <div>No Venues Found. Add Venues to get started.</div>
							}}
						/>
					)}
					{(meta && meta.total > 10) && (
						<Pagination
							align="center"
							total={meta?.total}
							showSizeChanger={false}
							disabled={status === 'fetching'}
							style={{ marginTop: '20px' }}
							current={meta?.currentPage}
							onChange={(page) => dispatch(fetchAllVenuesAction({ page, perPage: meta?.perPage }))}
						/>
					)}
				</div>
			</div>

			<AddEditVenue showModal={showModal} setShowModal={setShowModal} />
		</>
	)
}

export default VenuesPage
