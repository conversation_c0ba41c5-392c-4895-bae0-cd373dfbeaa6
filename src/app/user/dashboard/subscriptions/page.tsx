'use client';

import Link from 'next/link';
import { CloseCircleOutlined } from '@ant-design/icons';
import { EventSubscriptionModel } from '@/types/models/event-subscription.model';
import { PaginationMeta, ServerStatus } from '@/types/index.types';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store';
import { useEffect } from 'react';
import { fetchSubscribedEventsAction, subscribeOrUnsubscribeEventAction } from '@/store/slices/user.slice';
import { getFullImageUrl, stringLimit } from '@/utils/general.util';
import { triggerConfirmation } from '@/store/slices/app.slice';
import { Pagination, Skeleton } from 'antd';

const Subscriptions = () => {
	const dispatch = useDispatch<AppDispatch>();
	const { subscribedEvents, status, meta } = useSelector((state: RootState) => state.user) as {
		subscribedEvents: EventSubscriptionModel[], status: ServerStatus, meta: PaginationMeta
	};

	useEffect(() => {
		dispatch(fetchSubscribedEventsAction({ page: 1, perPage: 10 }));
	}, [dispatch]);

	if (status === 'fetching') {
		return (
			<ul className='list-container'>
				{[...Array(4)].map((_, i) => (
					<li key={i}>
						<Skeleton.Image style={{ width: 140, height: 80, margin: 10 }} active />

						<div className="title-content">
							<h2>
								<Skeleton.Input style={{ width: 250 }} active size="small" />
							</h2>
							<p><Skeleton.Input style={{ width: 500, marginTop: 8 }} active size="small" /></p>
						</div>
					</li>
				))}
			</ul>
		)
	}

	return (
		<>
			<ul className='list-container'>
				{subscribedEvents.length === 0 ? (
					<div className="no-data-found">
						<img
							style={{ width: 300, marginLeft: 100 }}
							src="/images/not-found.png"
							alt="No Subscriptions"
						/>
						<h3 style={{ marginLeft: -350 }}>
              You have not subscribed to any events
						</h3>
					</div>
				) : (
					subscribedEvents.map((sub, i) => (
						<li key={i}>
							<img src={sub?.event?.image ? getFullImageUrl(sub.event.image) : "https://placehold.co/130x90/png?text=No+Image"} alt={sub?.event?.title || ""} />
							<div className="title-content">
								<h2>
									<Link
										href={`/events/${sub?.event?.slug}-${sub?.event?.id}`}
									>
										<span>{sub?.event?.title}</span>
									</Link>
								</h2>
								{sub?.event?.about && (
									<p>{stringLimit(sub.event.about, 60)}</p>
								)}
							</div>
							<div className="action-bar">
								<a
									href=""
									onClick={(e) => {
										e.preventDefault();
										dispatch(triggerConfirmation({
											modal: true,
											title: 'Unsubscribe',
											subTitle: 'Do you really want to unsubscribe?',
											additionalInfo: 'You may not get notified about this event anymore.',
											btnLabel: 'Unsubscribe',
											onBtnClick: async () => {
												const res = await dispatch(subscribeOrUnsubscribeEventAction(sub?.event!.id, false));
												if (res && res.status === 200) {
													await dispatch(fetchSubscribedEventsAction({ page: meta.currentPage, perPage: 10 }));
													return res;
												}
											}
										}))
									}}
									className="ab_foot"
								>
									<CloseCircleOutlined /> &nbsp;&nbsp;Unsubscribe
								</a>
							</div>
						</li>
					))
				)}
			</ul>
			{meta && meta.total > 10 && (
				<Pagination
					align="center"
					total={meta?.total}
					showSizeChanger={false}
					style={{ marginTop: '20px' }}
					current={meta?.currentPage}
					onChange={(page) => dispatch(fetchSubscribedEventsAction({ page, perPage: meta?.perPage }))}
				/>
			)}
		</>
	);
}

export default Subscriptions;
