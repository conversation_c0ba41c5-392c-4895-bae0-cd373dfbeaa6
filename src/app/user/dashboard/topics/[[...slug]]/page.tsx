'use client'

import TableSkeleton from "@/components/common/TableSkeleton";
import AddEditTopic from "@/components/topic/AddEditTopic.component";
import AddEditTopicQAs from "@/components/topic/AddEditTopicQAs.component";
import { AppDispatch, RootState } from "@/store";
import { triggerConfirmation } from "@/store/slices/app.slice";
import { deleteTopicAction, deleteTopicQaAction, fetchAllTopicsAction, fetchTopicAction, toggleTopicQaStatusAction, toggleTopicStatusAction, topicStateChange } from "@/store/slices/topic.slice";
import { PaginationMeta, ServerStatus } from "@/types/index.types";
import { TopicQasModel } from "@/types/models/topic-qas.model";
import { TopicModel } from "@/types/models/topic.model";
import { CheckCircleFilled, DeleteFilled, EditFilled, MoreOutlined, PlusOutlined, StopFilled } from "@ant-design/icons";
import { TableProps, Tag, Button, Table, Dropdown, Pagination } from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

interface TopicsPageProps {
	params: {
		slug: string[]
	}
}

const TopicsPage = ({ params }: TopicsPageProps) => {
	const topicId = params.slug ? params.slug.pop() : null;

	const dispatch = useDispatch<AppDispatch>();
	const { rows, meta, status } = useSelector((state: RootState) => state.topic) as { rows: TopicModel[], meta: PaginationMeta | null, status: ServerStatus };

	const [showTopicModal, setShowTopicModal] = useState<boolean>(false);
	const [showTopicQaModal, setShowTopicQaModal] = useState<boolean>(false);

	const columns: TableProps<TopicModel>['columns'] = [
		{
			title: '#',
			key: 'index',
			align: 'center',
			width: 50,
			render: (_text, _record, index) => index + 1,
		},
		{
			title: 'Title',
			dataIndex: 'title',
			key: 'title',
			width: 400,
			render: (text: string) => (
				<div>
					{text}
				</div>
			)
		},
		{
			title: 'Status',
			dataIndex: 'status',
			key: 'status',
			width: 150,
			align: 'left',
			render: (text) => (
				<Tag color={text ? 'green' : 'red'}>{text ? 'Active' : 'Inactive'}</Tag>
			)
		},
		{
			title: 'Order',
			dataIndex: 'order',
			key: 'order',
			align: 'center',
			render: (text: number | null) => (
				<div>
					{text || '-'}
				</div>
			)
		},
		{
			title: 'Created At',
			dataIndex: 'created_at',
			key: 'created_at',
			width: 250,
			align: 'left',
			render: (text: string) => (
				<div>
					<p>{dayjs(text).format('Do MMM YYYY')}</p>
					<span style={{ fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)' }}>
						{dayjs(text).fromNow()}
					</span>
				</div>
			)
		},
		{
			title: 'Actions',
			key: 'actions',
			width: '5%',
			align: 'right',
			render: (_text, record: TopicModel) => (
				<Dropdown
					overlayStyle={{ width: 200 }}
					trigger={['click']}
					menu={{
						items: [
							{
								label: <span className="d-flex align-items-center gap-2">
									<EditFilled /> <span>Edit</span>
								</span>,
								key: 'edit',
								onClick: () => {
									dispatch(topicStateChange({ key: 'topicRecord', value: record }));
									setShowTopicModal(true);
									window.history.replaceState(
										null,
										'',
										`/user/dashboard/topics/${record.id}`
									);
								},
							},
							{
								label: record.status ? (
									<span className="d-flex align-items-center gap-2">
										<StopFilled /> <span>Deactivate</span>
									</span>
								) : (
									<span className="d-flex align-items-center gap-2">
										<CheckCircleFilled /> <span>Activate</span>
									</span>
								),
								key: 'status',
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: `${record.status ? 'Deactivate' : 'Activate'} Topic`,
										subTitle: <>Are you sure you want to {record.status ? 'deactivate' : 'activate'} <strong>{record.title}</strong> topic?</>,
										additionalInfo: `${record.status ? 'Deactivated' : 'Activated'} topic will ${record.status ? 'not be' : 'be'} visible to other users.`,
										btnLabel: `${record.status ? 'Deactivate' : 'Activate'} Now`,
										onBtnClick: async () => {
											const res = await dispatch(toggleTopicStatusAction(record.id, !record.status));
											if (res && res.status === 200) {
												await dispatch(fetchAllTopicsAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							},
							{
								label: <span className="d-flex align-items-center gap-2">
									<DeleteFilled /> <span>Delete</span>
								</span>,
								key: 'delete',
								danger: true,
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: 'Delete Topic',
										subTitle: <>Are you sure you want to delete <strong>{record.title}</strong> topic?</>,
										additionalInfo: 'Deleting a topic will also delete all the topic QAS associated with it.',
										btnLabel: 'Delete Now',
										onBtnClick: async () => {
											const res = await dispatch(deleteTopicAction(record.id));
											if (res && res.status === 200) {
												await dispatch(fetchAllTopicsAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							}
						]
					}}
				>
					<MoreOutlined />
				</Dropdown>
			)
		},
	];

	const expandedColumns: TableProps<TopicQasModel>['columns'] = [
		{
			title: '#',
			key: 'index',
			align: 'center',
			width: 50,
			render: (_text, _record, index) => index + 1,
		},
		{
			title: 'Question',
			dataIndex: 'question',
			key: 'question',
			width: 200,
			render: (text: string) => (
				<div>
					{text}
				</div>
			)
		},
		{
			title: 'Answer',
			dataIndex: 'answer',
			key: 'answer',
			width: 400,
			render: (text: string) => (
				<div>
					{text}
				</div>
			)
		},
		{
			title: 'Status',
			dataIndex: 'status',
			key: 'status',
			width: 100,
			align: 'left',
			render: (text) => (
				<Tag color={text ? 'green' : 'red'}>{text ? 'Active' : 'Inactive'}</Tag>
			)
		},
		{
			title: 'Views',
			dataIndex: 'view',
			key: 'view',
			align: 'center',
			render: (text: number | null) => (
				<div>
					{text || '-'}
				</div>
			)
		},
		{
			title: 'Order',
			dataIndex: 'order',
			key: 'order',
			align: 'center',
			render: (text: number | null) => (
				<div>
					{text || '-'}
				</div>
			)
		},
		{
			title: 'Created At',
			dataIndex: 'created_at',
			key: 'created_at',
			width: 200,
			align: 'left',
			render: (text: string) => (
				<div>
					<p>{dayjs(text).format('Do MMM YYYY')}</p>
					<span style={{ fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)' }}>
						{dayjs(text).fromNow()}
					</span>
				</div>
			)
		},
		{
			title: 'Actions',
			key: 'actions',
			width: '5%',
			align: 'right',
			render: (_text, record: TopicQasModel) => (
				<Dropdown
					overlayStyle={{ width: 200 }}
					trigger={['click']}
					menu={{
						items: [
							{
								label: <span className="d-flex align-items-center gap-2">
									<EditFilled /> <span>Edit</span>
								</span>,
								key: 'edit',
								onClick: () => {
									dispatch(topicStateChange({ key: 'topicQaRecord', value: record }));
									setShowTopicQaModal(true);
								},
							},
							{
								label: record.status ? (
									<span className="d-flex align-items-center gap-2">
										<StopFilled /> <span>Deactivate</span>
									</span>
								) : (
									<span className="d-flex align-items-center gap-2">
										<CheckCircleFilled /> <span>Activate</span>
									</span>
								),
								key: 'status',
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: `${record.status ? 'Deactivate' : 'Activate'} Topic QA`,
										subTitle: <>Are you sure you want to {record.status ? 'deactivate' : 'activate'} <strong>{record.question}</strong> topic QA?</>,
										additionalInfo: `${record.status ? 'Deactivated' : 'Activated'} topic QA will ${record.status ? 'not be' : 'be'} visible to other users.`,
										btnLabel: `${record.status ? 'Deactivate' : 'Activate'} Now`,
										onBtnClick: async () => {
											const res = await dispatch(toggleTopicQaStatusAction(record.id, !record.status));
											if (res && res.status === 200) {
												await dispatch(fetchAllTopicsAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							},
							{
								label: <span className="d-flex align-items-center gap-2">
									<DeleteFilled /> <span>Delete</span>
								</span>,
								key: 'delete',
								danger: true,
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: 'Delete Topic',
										subTitle: <>Are you sure you want to delete <strong>{record.question}</strong> topic QA?</>,
										additionalInfo: 'Deleted topic QA will be completely removed and cannot be recovered.',
										btnLabel: 'Delete Now',
										onBtnClick: async () => {
											const res = await dispatch(deleteTopicQaAction(record.id));
											if (res && res.status === 200) {
												await dispatch(fetchAllTopicsAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							}
						]
					}}
				>
					<MoreOutlined />
				</Dropdown>
			)
		},
	];

	useEffect(() => {
		if (!showTopicModal || !showTopicQaModal) dispatch(fetchAllTopicsAction({ page: meta?.currentPage || 1, perPage: 10 }));
		if (topicId) {
			dispatch(fetchTopicAction({ id: Number(topicId) }))
				.then((res) => {
					if (res && res.status === 200) setShowTopicModal(true);
				});
		}
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [dispatch, topicId, showTopicModal, showTopicQaModal])

	return (
		<>
			<div className="page-intro-header">
				<div className="header-title">
					<h1 className="titled-info">Topics List</h1>
					<p>Topics that users see on the "Help & Support" page.</p>
				</div>

				<div className="action-button-container">
					<Button
						onClick={() => {
							dispatch(topicStateChange({ key: 'topicRecord', value: null }));
							setShowTopicModal(true);
						}}
						icon={<PlusOutlined />}
					>
						Add Topic
					</Button>
					<Button
						onClick={() => {
							dispatch(topicStateChange({ key: 'topicQaRecord', value: null }));
							setShowTopicQaModal(true)
						}}
						icon={<PlusOutlined />}
					>
						Add QA
					</Button>
				</div>
			</div>

			<div className="dashboard-main-content">
				<div className="tabled-content">
					{status === 'fetching' ? (
						<Table<TopicModel>
							rowKey="id"
							columns={columns}
							dataSource={[]}
							rowClassName={'custom-table-row'}
							className="dashboard-table"
							size='small'
							pagination={false}
							locale={{
								emptyText: <TableSkeleton length={10} />
							}}
						/>
					) : (
						<Table<TopicModel>
							rowKey="id"
							columns={columns}
							dataSource={rows}
							rowClassName={'custom-table-row'}
							size='small'
							pagination={false}
							locale={{
								emptyText: <div>No Topics Found. Add Topics to get started.</div>
							}}
							expandable={{
								expandedRowRender: (record) => (
									<Table<TopicQasModel>
										size="small"
										rowKey="id"
										title={() => <strong>Topic QAs</strong>}
										columns={expandedColumns}
										dataSource={record.topicQas}
										pagination={false}
										locale={{
											emptyText: <div>No Topic QAs. Add Topic QAs to get started.</div>
										}}
									/>
								),
								defaultExpandedRowKeys: ['0']
							}}
						/>
					)}

					{(meta && meta.total > 10) && (
						<Pagination
							align="center"
							total={meta?.total}
							showSizeChanger={false}
							disabled={status === 'fetching'}
							style={{ marginTop: '20px' }}
							current={meta?.currentPage}
							onChange={(page) => dispatch(fetchAllTopicsAction({ page, perPage: meta?.perPage }))}
						/>
					)}
				</div>

			</div>

			<AddEditTopic showModal={showTopicModal} setShowModal={setShowTopicModal} />
			<AddEditTopicQAs showModal={showTopicQaModal} setShowModal={setShowTopicQaModal} />
		</>
	)
}

export default TopicsPage
