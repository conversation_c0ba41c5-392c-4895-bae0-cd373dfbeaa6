'use client'

import AddEditArtist from "@/components/artist/AddEditArtist.component";
import TableSkeleton from "@/components/common/TableSkeleton";
import { AppDispatch, RootState } from "@/store";
import { triggerConfirmation } from "@/store/slices/app.slice";
import { artistStateChange, deleteArtistAction, fetchAllArtistsAction, fetchArtistAction, toggleArtistStatusAction } from "@/store/slices/artist.slice";
import { PaginationMeta, ServerStatus } from "@/types/index.types";
import { ArtistModel } from "@/types/models/artist.model";
import { getFullImageUrl } from "@/utils/general.util";
import { CheckCircleFilled, DeleteFilled, EditFilled, FileImageOutlined, MoreOutlined, PlusOutlined, StopFilled } from "@ant-design/icons";
import { TableProps, Tag, Button, Table, Dropdown, Pagination } from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

interface ArtistsPageProps {
  params: {
    slug: string[]
  }
}

const ArtistsPage = ({ params }: ArtistsPageProps) => {
	const artistId = params.slug ? params.slug.pop() : null;
	const dispatch = useDispatch<AppDispatch>();
	const [showModal, setShowModal] = useState<boolean>(false);
	const { rows, meta, status } = useSelector((state: RootState) => state.artist) as { rows: ArtistModel[], meta: PaginationMeta | null, status: ServerStatus };

	const columns: TableProps<ArtistModel>['columns'] = [
		{
			title: '#',
			key: 'index',
			width: '1%',
			align: 'center',
			render: (_text, _record, index) => index + 1,
		},
		{
			title: 'Name',
			dataIndex: 'name',
			key: 'name',
			width: '25%',
			render: (text: string, record: ArtistModel) => (
				<div className="event-item">
					<div className="event-item__image-container">
						{record.image ? (
							<img
								src={getFullImageUrl(record.image)}
								alt={text || 'Artist'}
								className="event-item__image"
							/>
						) : (
							<div className="event-item__placeholder">
								<FileImageOutlined size={20} />
							</div>
						)}
					</div>
					<div className="event-item__content">
						<span className="event-item__title">{text || '-'}</span>
						<div className="event-item__categories">
							<Tag key={'category-type'} color={'pink'} className="event-item__category-tag">{record.title}</Tag>
						</div>
					</div>
				</div>
			)
		},
		{
			title: 'Stage Name',
			dataIndex: 'stage_name',
			key: 'stage_name',
			width: '15%',
			align: 'left',
			render: (text: string) => (
				<div>
					{text || '-'}
				</div>
			)
		},
		{
			title: 'Band Name',
			dataIndex: 'band_name',
			key: 'band_name',
			width: '15%',
			align: 'left',
			render: (text: string) => (
				<div>
					{text || '-'}
				</div>
			)
		},
		{
			title: 'Home Town',
			dataIndex: 'home_town',
			key: 'home_town',
			width: '15%',
			align: 'left',
			render: (text: string) => (
				<div>
					{text || '-'}
				</div>
			)
		},
		{
			title: 'Status',
			dataIndex: 'status',
			key: 'status',
			width: '10%',
			align: 'left',
			render: (text) => (
				<Tag color={text ? 'green' : 'red'}>{text ? 'Active' : 'Inactive'}</Tag>
			)
		},
		{
			title: 'DOB',
			dataIndex: 'dob',
			key: 'dob',
			width: '10%',
			align: 'left',
			render: (text: string) => (
				<div>
					<p>{dayjs(text).format('Do MMM YYYY')}</p>
				</div>
			)
		},
		{
			title: 'Actions',
			key: 'actions',
			width: '5%',
			align: 'right',
			render: (_text, record: ArtistModel) => (
				<Dropdown
					overlayStyle={{ width: 200 }}
					trigger={['click']}
					menu={{
						items: [
							{
								label: <span className="d-flex align-items-center gap-2">
									<EditFilled /> <span>Edit</span>
								</span>,
								key: 'edit',
								onClick: async () => {
									await dispatch(artistStateChange({ key: 'record', value: record }));
									setShowModal(true);
									window.history.replaceState(
										null,
										'',
										`/user/dashboard/artists/${record.id}`
									);
								},
							},
							{
								label: record.status ? (
									<span className="d-flex align-items-center gap-2">
										<StopFilled /> <span>Deactivate</span>
									</span>
								) : (
									<span className="d-flex align-items-center gap-2">
										<CheckCircleFilled /> <span>Activate</span>
									</span>
								),
								key: 'status',
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: `${record.status ? 'Deactivate' : 'Activate'} Artist`,
										subTitle: <>Are you sure you want to {record.status ? 'deactivate' : 'activate'} <strong>{record.name}</strong> artist?</>,
										additionalInfo: `${record.status ? 'Deactivated' : 'Activated'} artist will ${record.status ? 'not be' : 'be'} visible to other users.`,
										btnLabel: `${record.status ? 'Deactivate' : 'Activate'} Now`,
										onBtnClick: async () => {
											const res = await dispatch(toggleArtistStatusAction(record.id, !record.status));
											if (res && res.status === 200) {
												await dispatch(fetchAllArtistsAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							},
							{
								label: <span className="d-flex align-items-center gap-2">
									<DeleteFilled /> <span>Delete</span>
								</span>,
								key: 'delete',
								danger: true,
								onClick: () => {
									dispatch(triggerConfirmation({
										modal: true,
										title: 'Delete Artist',
										subTitle: <>Are you sure you want to delete <strong>{record.name}</strong> artist?</>,
										additionalInfo: 'Deleted Artist will not be visible to other users.',
										btnLabel: 'Delete Now',
										onBtnClick: async () => {
											const res = await dispatch(deleteArtistAction(record.id));
											if (res && res.status === 200) {
												await dispatch(fetchAllArtistsAction({ page: meta?.currentPage, perPage: meta?.perPage }));
												return res;
											}
										}
									}))
								}
							}
						]
					}}
				>
					<MoreOutlined />
				</Dropdown>
			)
		},
	];

	useEffect(() => {
		if (!showModal) dispatch(fetchAllArtistsAction({ page: meta?.currentPage || 1, perPage: 10 }));
		if (artistId) {
			dispatch(fetchArtistAction(Number(artistId)))
				.then((res) => {
					if (res && res.status === 200) setShowModal(true);
				});
		}
	}, [dispatch, artistId, showModal])

	return (
		<>
			<div className="page-intro-header">
				<div className="header-title">
					<h1 className="titled-info">Artists List</h1>
					<p>Manage your artists here</p>
				</div>

				<div className="action-button-container">
					<Button onClick={() => setShowModal(true)} icon={<PlusOutlined />}>Add Artist</Button>
				</div>
			</div>

			<div className="dashboard-main-content">
				<div className="tabled-content">
					{status === 'fetching' ? (
						<Table<ArtistModel>
							rowKey="id"
							columns={columns}
							dataSource={[]}
							rowClassName={'custom-table-row'}
							className="dashboard-table"
							size='small'
							pagination={false}
							locale={{
								emptyText: <TableSkeleton length={10} />
							}}
						/>
					) : (
						<Table<ArtistModel>
							rowKey="id"
							columns={columns}
							dataSource={rows}
							rowClassName={'custom-table-row'}
							size='small'
							pagination={false}
							locale={{
								emptyText: <div>No Artists Found. Add Artists to get started.</div>
							}}
						/>
					)}
					{(meta && meta.total > 10) && (
						<Pagination
							align="center"
							total={meta?.total}
							showSizeChanger={false}
							disabled={status === 'fetching'}
							style={{ marginTop: '20px' }}
							current={meta?.currentPage}
							onChange={(page) => dispatch(fetchAllArtistsAction({ page, perPage: meta?.perPage }))}
						/>
					)}
				</div>
			</div>

			<AddEditArtist showModal={showModal} setShowModal={setShowModal} />
		</>
	)
}

export default ArtistsPage
