'use client';

import { getEncryptedCookie } from "@/utils/cookie.util";
import { UserModel } from "@/types/models/user.model";
import { AdminDashboard } from "@/components/dashboard/AdminDashboard.component";
import { PartnerDashboard } from "@/components/dashboard/PartnerDashboard.component";
import { NormalUserDashboard } from "@/components/dashboard/NormalUserDashboard.component";


interface DashboardProps {
  params: {
    slug: string[]
  }
}

export default function DashboardPage({ params }: DashboardProps) {
	const user = getEncryptedCookie('_eticket_user_') as UserModel;

	if (!user) return null;

	if (user.user_type === 'admin') {
		return <AdminDashboard params={params} />;
	}

	if (user.user_type === 'normal') {
		if (user.is_partner) {
			return <PartnerDashboard user={user} />;
		} else {
			return <NormalUserDashboard user={user} />;
		}
	}

	return null;
}
