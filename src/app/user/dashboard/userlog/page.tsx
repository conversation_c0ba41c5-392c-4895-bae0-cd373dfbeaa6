"use client";

import TableSkeleton from "@/components/common/TableSkeleton";
import { AppDispatch, RootState } from "@/store";
import { fetchAllUserLogs } from "@/store/slices/user.slice";
import { PaginationMeta, ServerStatus } from "@/types/index.types";
import { UserLogModel } from "@/types/models/user-log.model";
import { CaretDownFilled } from "@ant-design/icons";
import { Col, Pagination, Row, Select, Table, Tag } from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

type LogNameType = {
  value: string;
  label: string;
};

type ActionType = {
  value: string;
  label: string;
};

const UserLogs = () => {
	const dispatch = useDispatch<AppDispatch>();
	const { userLogs, meta, status } = useSelector((state: RootState) => state.user) as { userLogs: UserLogModel[], meta: PaginationMeta | null, status: ServerStatus };

	const [selectedLogName, setSelectedLogName] = useState<string | null>(null);
	const [selectedAction, setSelectedAction] = useState<string | null>(null);

	const logNames: LogNameType[] = [
		{ value: "AUTH", label: "AUTH" },
		{ value: "USER", label: "USER" },
		{ value: "COMPANY", label: "COMPANY" },
		{ value: "CATEGORY", label: "CATEGORY" },
		{ value: "ARTIST", label: "ARTIST" },
		{ value: "EVENT", label: "EVENT" },
		{ value: "VENUE", label: "VENUE" },
		{ value: "NEWSLETTER", label: "NEWSLETTER" },
		{ value: "TICKET", label: "TICKET" },
		{ value: "PAYMENT", label: "PAYMENT" },
		{ value: "TOPIC", label: "TOPIC" },
	];

	const actionMap: Record<string, ActionType[]> = {
		AUTH: [
			{ value: "AUTH-REGISTER-USER", label: "User Register" },
			{ value: "AUTH-REGISTER-PARTNER", label: "Partner Register" },
			{ value: "AUTH-LOGIN", label: "Auth Login" },
			{ value: "AUTH-GOOGLE-LOGIN", label: "Google Login" },
			{ value: "AUTH-FB-LOGIN", label: "Facebook Login" },
			{ value: "AUTH-EMAIL-UPDATE", label: "Email Update" },
			{ value: "AUTH-PASSWORD-CHANGE", label: "Change Password" },
			{ value: "AUTH-PASSWORD-RESET-LINK", label: "Password Reset Link" },
			{ value: "AUTH-RESET-PASSWORD", label: "Reset Password" },
			{ value: "AUTH-SEND-VERIFICATION-TOKEN", label: "Send Verification Token" },
			{ value: "AUTH-RESEND-VERIFICATION-LINK", label: "Verification Link Resend" },
			{ value: "AUTH-VERIFY-EMAIL", label: "Verify Email" },
		],
		CATEGORY: [
			{ value: "CATEGORY-CREATE", label: "Category Created" },
			{ value: "CATEGORY-UPDATE", label: "Category Updated" },
			{ value: "CATEGORY-DELETED", label: "Category Deleted" },
		],
		COMPANY: [{ value: "COMPANY-UPDATE", label: "Company Updated" }],
		USER: [{ value: "USER-UPDATE", label: "User Updated" }],
		TICKET: [{ value: "TICKET-GENERATED", label: "Ticket Generated" }],
		NEWSLETTER: [{ value: "NEWSLETTER-CREATE", label: "Newsletter Created" }],
		ARTIST: [
			{ value: "ARTIST-CREATE", label: "Artist Created" },
			{ value: "ARTIST-UPDATE", label: "Artist Updated" },
			{ value: "ARTIST-DELETE", label: "Artist Deleted" },
		],
		VENUE: [
			{ value: "VENUE-CREATE", label: "Venue Created" },
			{ value: "VENUE-UPDATE", label: "Venue Updated" },
			{ value: "VENUE-DELETE", label: "Venue Deleted" },
		],
		EVENT: [
			{ value: "EVENT-CREATE", label: "Event Created" },
			{ value: "EVENT-UPDATE", label: "Event Updated" },
			{ value: "EVENT-DELETE", label: "Event Deleted" },
			{ value: "EVENT-FEATURED-UPDATE", label: "Feature Event" },
			{ value: "EVENT-SLIDER-UPDATE", label: "Slider Event" },
			{ value: "EVENT-LIKE-UPDATE", label: "Liked Event" },
			{ value: "EVENT-SUBSCRIPTION-UPDATE", label: "Event Subscribed" },
		],
		PAYMENT: [
			{ value: "PAYMENT-KHALTI-INITIATE", label: "Initiate Khalti Payment" },
			{ value: "PAYMENT-BY-KHALTI", label: "Payment by Khalti" },
			{ value: "PAYMENT-BY-ESEWA", label: "Payment by Esewa" },
		],
		TOPIC: [
			{ value: "TOPIC-CREATE", label: "Topic Created" },
			{ value: "TOPIC-UPDATE", label: "Topic Updated" },
			{ value: "TOPIC-DELETE", label: "Topic Deleted" },
		],
	};

	useEffect(() => {
		const filters = {
			page: 1,
			perPage: 10,
			name: selectedLogName,
			log_action: selectedAction,
		};

		dispatch(fetchAllUserLogs(filters));
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [selectedLogName, selectedAction, dispatch]);

	const columns = [
		{
			title: "Logged By",
			dataIndex: "user",
			key: "user",
			render: (user: any) => (user ? <span>{user.f_name}  {user.l_name}</span> : "-"),
		},
		{
			title: "Name",
			dataIndex: "event_type",
			key: "event_type",
		},
		{
			title: "Action",
			dataIndex: "action",
			key: "action",
		},
		{
			title: "Result",
			dataIndex: "type",
			key: "type",
			render: (val: boolean) => (
				<Tag color={val ? "green" : "red"}>{val ? "Success" : "Failed"}</Tag>
			),
		},
		{
			title: "Details",
			dataIndex: "details",
			key: "details",
		},
		{
			title: "Created At",
			dataIndex: "created_at",
			key: "created_at",
			render: (text: string) => (
				<>{dayjs(text).format('Do MMM, YYYY, h:mm A')}</>
			)
		},
	];

	return (
		<>
			<div className="page-intro-header">
				<div className="header-title">
					<h1 className="titled-info">User Logs</h1>
					<p>View all the actions performed by users.</p>
				</div>

			</div>
			<div className="dashboard-main-content">
				<div className="tabled-content">
					<Row gutter={16}>
						<Col span={6}>
							<label>Search By Log Name</label>
							<Select
								options={logNames}
								style={{ width: "100%", marginTop: 4 }}
								value={selectedLogName}
								onChange={(name) => {
									setSelectedLogName(name);
									setSelectedAction(null);
								}}
								suffixIcon={<CaretDownFilled />}
							/>
						</Col>

						<Col span={6}>
							<label>Search By Action</label>
							<Select
								options={selectedLogName ? actionMap[selectedLogName] || [] : []}
								style={{ width: "100%", marginTop: 4 }}
								value={selectedAction}
								disabled={!selectedLogName}
								onChange={setSelectedAction}
								suffixIcon={<CaretDownFilled />}
							/>
						</Col>
					</Row>
					<br />

					<Table
						dataSource={userLogs}
						columns={columns}
						pagination={false}
						loading={{
							spinning: status === 'fetching' && userLogs.length > 0,
							tip: 'Loading User Logs...',
						}}
						locale={{
							emptyText: status === 'fetching' ?
								<TableSkeleton length={10} /> :
								<div>No Logs Found.</div>
						}}
					/>
					{(meta && meta.total > 10) && (
						<Pagination
							align="center"
							total={meta?.total}
							showSizeChanger={false}
							disabled={status === 'fetching'}
							style={{ marginTop: '20px' }}
							current={meta?.currentPage}
							onChange={(page) =>
								dispatch(fetchAllUserLogs({ page, perPage: meta?.perPage, name: selectedLogName, log_action: selectedAction }))
							}
						/>
					)}
					<br />
					<br />
					<br />
				</div>
			</div>

		</>
	);
}

export default UserLogs;
