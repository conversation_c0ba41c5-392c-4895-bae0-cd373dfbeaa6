'use client'

import AddressAutocomplete from "@/components/AddressAutocomplete.component";
import { AppInputField, AppInputTextField } from "@/components/common/AppInputField.component";
import ImageCropper from "@/components/ImageCropper.component";
import { AppDispatch } from "@/store";
import { updateCompanyInfoAction } from "@/store/slices/partner.slice";
import { UpdateCompanyInfoType } from "@/types/forms/update-company-info.type";
import { Errors } from "@/types/index.types";
import { UserModel } from "@/types/models/user.model";
import { getEncryptedCookie } from "@/utils/cookie.util";
import { getFullImageUrl, isEmail, isPhone, isValidUrl } from "@/utils/general.util";
import { CloseCircleFilled } from "@ant-design/icons";
import { Button } from "antd";
import { Formik } from "formik";
import { useDispatch } from "react-redux";

const CompanyInfo = () => {
	const dispatch = useDispatch<AppDispatch>();
	const user = getEncryptedCookie('_eticket_user_') as UserModel;
	const company = user?.companies?.[0];

	return (
		<Formik<UpdateCompanyInfoType>
			key={company?.id}
			initialValues={{
				name: company?.name || '',
				domain: company?.domain || '',
				logo: company?.logo || '',
				email: company?.email || '',
				phone: company?.phone || '',
				address: company?.address || null,
				about: company?.about || '',
			}}
			validate={(data) => {
				const errors: Omit<Errors<UpdateCompanyInfoType>, 'address'> & {
						address?: string
					} = {};

				if (!data.name) errors.name = 'Please enter the name of the company';
				if (data.address && !data.address.city) errors.address = 'Please select an address with city';
				if (data.about && data.about.length < 30) errors.about = 'About must be at least 30 characters';
				if (data.about && data.about.length > 500) errors.about = 'About should not exceed 500 characters';
				if (data.email && !isEmail(data.email)) errors.email = 'Please enter a valid email address';
				if (data.phone && !isPhone(data.phone, 'mobile')) errors.phone = 'Please enter a valid phone number';
				if (data.domain && !isValidUrl(data.domain)) errors.domain = 'Please enter a valid domain';

				return errors;
			}}
			onSubmit={async (values, actions) => {
				if (!company?.id) return;
				actions.setSubmitting(false);
				const res = await dispatch(
					updateCompanyInfoAction<UpdateCompanyInfoType>(
						values,
							company?.id as number
					)
				)
				if (res && res.status === 200) {
					actions.setSubmitting(false);
				}
			}}
		>
			{({ values, errors, handleSubmit, setFieldValue, isValid, isValidating, isSubmitting, dirty }) => (
				<div className="company-info-form">
					<div className="form-header">
						<h1>Update Company Info</h1>
					</div>

					<form>
						<div className="container">
							<div className="row">
								<div className="col-md-6">
									<div className="logo-upload">
										<label>Company Logo</label>
										<ImageCropper
											value={values.logo as string}
											aspectRatio={1}
											isCircular={true}
											renderUploadTrigger={(openUpload) => (
												<div className="company-logo upload-trigger" onClick={openUpload}>
													<span>Upload</span>
												</div>
											)}
											renderPreview={(resetImage) => (
												<div className="image-preview-container">
													<div className="company-logo">
														<img
															src={getFullImageUrl(values.logo || '')}
															alt="Company Logo"
														/>
													</div>
													<CloseCircleFilled className="clear-icon" onClick={resetImage} />
												</div>
											)}
											onCropComplete={(croppedImageUrl) => {
												setFieldValue('logo', croppedImageUrl.split('/').at(-1));
											}}
										/>
									</div>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="company_name"
										value={values.name}
										label={'Company Name'}
										required={true}
										autoFocus={true}
										// info={'Name of your company'}
										onChangeInput={(value: string) => setFieldValue('name', value)}
										placeholder={'My Awesome Company'}
										errorMessage={errors.name}
									/>
								</div>

								<div className="col-md-12">
									<div className="input-container">
										<label htmlFor="address">Company Address</label>
										<AddressAutocomplete
											placeholder="Select Company Address"
											value={
												values.address
													? {
														value: {
															place_id: values.address.place_id,
															description: values.address.description,
														},
														label: values.address.description,
													}
													: null
											}
											onAddressSelect={(addressData) => setFieldValue('address', addressData)}
											error={errors?.address}
										/>
									</div>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="domain"
										value={values.domain}
										onChangeInput={(value: string) => setFieldValue('domain', value)}
										label={'Domain'}
										// info={'Domain of your company'}
										placeholder={'example.com'}
										errorMessage={errors.domain}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="email"
										value={values.email}
										onChangeInput={(value: string) => setFieldValue('email', value)}
										label={'Email'}
										// info={'Email of your company'}
										placeholder={'<EMAIL>'}
										errorMessage={errors.email}
									/>
								</div>

								<div className="col-md-12">
									<AppInputField
										id="phone"
										value={values.phone}
										onChangeInput={(value: string) => setFieldValue('phone', value)}
										label={'Phone'}
										// info={'Phone number of your company'}
										placeholder={'9845585858'}
										errorMessage={errors.phone}
									/>
								</div>

								<div className="col-md-12">
									<AppInputTextField
										id="about_company"
										value={values.about}
										onChangeInput={(value: string) => setFieldValue('about', value)}
										label={'About your company (min 30 characters / max 500 characters)'}
										placeholder={'Description about your company ....'}
										rows={6}
										// info={'Short info about your company (minimum 30 characters / max 500 characters)'}
										errorMessage={errors.about}
										maxLength={500}
									/>
								</div>
							</div>

							<div className="submit-btn">
								<Button
									type="primary"
									size="large"
									onClick={() => handleSubmit()}
									disabled={!isValid || isValidating || !dirty}
									loading={isSubmitting}
								>
									Update Info
								</Button>
							</div>
						</div>
					</form>
				</div>

			)}
		</Formik>
	)
}

export default CompanyInfo
