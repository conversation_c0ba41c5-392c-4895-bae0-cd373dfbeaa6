'use client'

import { AppDispatch, RootState } from "@/store";
import { fetchGeneralInfoAction } from "@/store/slices/general-info.slice";
import { fetchTopicQaAction } from "@/store/slices/topic.slice";
import { ServerStatus } from "@/types/index.types";
import { GeneralInfoModal } from "@/types/models/general-info.model";
import { TopicQasModel } from "@/types/models/topic-qas.model";
import { convertNewLinesToBr } from "@/utils/general.util";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { Skeleton } from "antd";
import { useRouter } from "next-nprogress-bar";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

type SupportQaPageProps = {
  params: {
    slug: string
  }
}

const SupportQa = ({ params }: SupportQaPageProps) => {
	const { slug } = params;

	const router = useRouter();
	const dispatch = useDispatch<AppDispatch>();
	const { status, topicQaRecord } = useSelector((state: RootState) => state.topic) as { status: ServerStatus, topicQaRecord: TopicQasModel | null };
	const { record: generalInfo } = useSelector((state: RootState) => state.generalInfo) as { record: GeneralInfoModal };

	const [loading, setLoading] = useState<boolean>(false);

	const fetchTopicQa = async () => {
		setLoading(true);
		await dispatch(fetchTopicQaAction(slug));
		setLoading(false);
	}

	useEffect(() => {
		if (slug && (!topicQaRecord || topicQaRecord?.slug !== slug)) {
			fetchTopicQa();
		}
		dispatch(fetchGeneralInfoAction());
	// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [dispatch, slug]);

	if (loading || status === 'fetching') {
		return (
			<section className="help-support">
				<div className="container-fluid-navigation">
					<div className="search-content">
						<div className="row">
							<div className="col-sm-9">
								<Skeleton active title paragraph={{ rows: 1 }} />
								<hr/>
								<Skeleton active paragraph={{ rows: 4 }} />
							</div>

							<div className="col-md-3 contact-section-support">
								<h2 className="search_browse">Contact Us</h2>
								<hr/>
								<Skeleton active paragraph={{ rows: 1 }} />
							</div>
						</div>
					</div>
				</div>
			</section>
		)
	}

	return (
		<section className="help-support">
			<div className="container-fluid-navigation">
				<div className="search-content">
					<div className="row">
						<div className="col-sm-9 qa-container">
							<div className="back-button" onClick={() => router.push('/support')}>
								<ArrowLeftOutlined /> Back
							</div>
							{topicQaRecord ? (
								<>
									<h2>{topicQaRecord.topic.title}</h2>
									<hr />
									<div className="qa-content">
										<h3 className="question"><i className="fa-solid fa-circle-question"></i> {topicQaRecord.question}</h3>
										<div className="answer">
											<p dangerouslySetInnerHTML={{ __html: convertNewLinesToBr(topicQaRecord.answer) }} />
										</div>
									</div>
								</>
							) : (
								<p>No Question found.</p>
							)}
						</div>

						<div className="col-md-3 contact-section-support">
							<h2 className="search_browse">Contact Us</h2>
							<hr/>
							<p>Email Us</p>
							<br/>

							<ul>
								<li>
									<a
										href={"mailto:" + (generalInfo?.email_one || '<EMAIL>')}
										target="_blank"
										rel="noopener noreferrer">{generalInfo?.email_one || '<EMAIL>'}
									</a>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</section>
	)
}

export default SupportQa
