'use client'

import { AppDispatch, RootState } from "@/store";
import { fetchGeneralInfoAction } from "@/store/slices/general-info.slice";
import { fetchTopicAction } from "@/store/slices/topic.slice";
import { ServerStatus } from "@/types/index.types";
import { GeneralInfoModal } from "@/types/models/general-info.model";
import { TopicModel } from "@/types/models/topic.model";
import { convertNewLinesToBr } from "@/utils/general.util";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { Skeleton } from "antd";
import { useRouter } from "next-nprogress-bar";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

type TopicQasPageProps = {
  params: {
    id: string
  }
}

const TopicQas = ({ params }: TopicQasPageProps) => {
	const { id } = params;

	const dispatch = useDispatch<AppDispatch>();
	const router = useRouter();
	const { status, topicRecord } = useSelector((state: RootState) => state.topic) as {
    status: ServerStatus,
    topicRecord: TopicModel | null
  };
	const { record: generalInfo } = useSelector((state: RootState) => state.generalInfo) as { record: GeneralInfoModal };

	const [loading, setLoading] = useState<boolean>(false);

	const fetchTopic = async () => {
		setLoading(true);
		await dispatch(fetchTopicAction({ id: Number(id), getQas: true }));
		setLoading(false);
	}

	useEffect(() => {
		if (id && (!topicRecord || topicRecord.id !== Number(id))) {
			fetchTopic();
		}
		dispatch(fetchGeneralInfoAction());
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [dispatch, id]);

	if (loading || status === 'fetching') {
		return (
			<section className="help-support">
				<div className="container-fluid-navigation">
					<div className="search-content">
						<div className="row">
							<div className="col-sm-9">
								<Skeleton active title paragraph={{ rows: 1 }} />
								<hr />
								<Skeleton active paragraph={{ rows: 4 }} />
							</div>
							<div className="col-md-3 contact-section-support">
								<h2 className="search_browse">Contact Us</h2>
								<hr />
								<Skeleton active paragraph={{ rows: 1 }} />
							</div>
						</div>
					</div>
				</div>
			</section>
		)
	}

	return (
		<section className="help-support">
			<div className="container-fluid-navigation">
				<div className="search-content">
					<div className="row">
						<div className="col-sm-9 qa-container">
							<div className="back-button" onClick={() => router.push('/support')}>
								<ArrowLeftOutlined /> Back
							</div>
							{topicRecord && topicRecord.topicQas && topicRecord.topicQas.length > 0 ? (
								<>
									<h2>{topicRecord.title}</h2>
									<hr />
									<div className="qa-list">
										{topicRecord.topicQas.map((qa, index) => (
											<div key={qa.id} className="qa-content">
												<h3 className="question">
													<i className="fa-solid fa-circle-question"></i> {qa.question}
												</h3>
												<div className="answer">
													<p dangerouslySetInnerHTML={{ __html: convertNewLinesToBr(qa.answer) }} />
												</div>
												{index < topicRecord.topicQas.length - 1 && <hr />}
											</div>
										))}
									</div>
								</>
							) : (
								<p>No Questions found.</p>
							)}
						</div>

						<div className="col-md-3 contact-section-support">
							<h2 className="search_browse">Contact Us</h2>
							<hr />
							<p>Email Us</p>
							<br />
							<ul>
								<li>
									<a
										href={"mailto:" + (generalInfo?.email_one || '<EMAIL>')}
										target="_blank"
										rel="noopener noreferrer">{generalInfo?.email_one || '<EMAIL>'}
									</a>
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
}

export default TopicQas;
