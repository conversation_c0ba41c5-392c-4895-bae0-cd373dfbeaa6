'use client'

import { AppDispatch, RootState } from "@/store";
import { fetchGeneralInfoAction } from "@/store/slices/general-info.slice";
import { fetchAllTopicsAction, topicStateChange, updateTopicQaViewAction } from "@/store/slices/topic.slice";
import { ServerStatus } from "@/types/index.types";
import { GeneralInfoModal } from "@/types/models/general-info.model";
import { TopicModel } from "@/types/models/topic.model";
import { stringLimit } from "@/utils/general.util";
import { Skeleton } from "antd";
import { useRouter } from "next-nprogress-bar";
import pluralize from "pluralize";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

const Support = () => {
	const dispatch = useDispatch<AppDispatch>();
	const { rows, status } = useSelector((state: RootState) => state.topic) as { rows: TopicModel[], status: ServerStatus };
	const { record: generalInfo } = useSelector((state: RootState) => state.generalInfo) as { record: GeneralInfoModal };

	const router = useRouter();
	const [search, setSearch] = useState<string>('');
	const [loading, setLoading] = useState<boolean>(false);

	const fetchTopics = async () => {
		setLoading(true);
		await dispatch(fetchAllTopicsAction({ type: 'active' }));
		setLoading(false);
	}

	useEffect(() => {
		fetchTopics();
		dispatch(fetchGeneralInfoAction());
	}, [dispatch]);

	if (loading || status === 'fetching') {
		return (
			<section className="help-support">
				<div className="container-fluid-navigation">
					<div className="search-help">
						<div className="input-group mb-3">
							<Skeleton.Input active style={{ width: '100%' }} />
						</div>
					</div>

					<div className="search-content">
						<div className="row">
							<div className="col-sm-9">
								<h2 className="search_browse">Browse by topic.</h2>
								<hr/>
								<div className="row">
									{[...Array(4)].map((_, index) => (
										<div key={index} className="col-sm-6 q-list">
											<Skeleton active paragraph={{ rows: 2 }} />
										</div>
									))}
								</div>
							</div>

							<div className="col-md-3 contact-section-support">
								<h2 className="search_browse">Contact Us</h2>
								<hr/>
								<Skeleton active paragraph={{ rows: 1 }} />
							</div>
						</div>
					</div>
				</div>
			</section>
		)
	}

	const renderTopics = () => {
		return (
			<div className="row">
				{rows.map((topic, index) => (
					<div key={index} className="col-sm-6 q-list">
						<h3>{topic.title}</h3>
						<h5>
							<i className="fas fa-list" />
							&nbsp;&nbsp; {topic.topicQas.length}
							{pluralize(' ARTICLE', topic.topicQas.length)}
							{topic.topicQas.length > 1 && (
								<a
									href="#"
									onClick={(e) => {
										e.preventDefault();
										dispatch(topicStateChange({ key: 'topicRecord', value: topic }));
										router.push(`/support/topic/${topic.id}`);
									}}
								>
									VIEW ALL
								</a>
							)}
						</h5>
						<div className="article-line">
							<ul>
								{topic.topicQas.map((qa, i) => (
									<li key={i}>
										<a
											href="#"
											onClick={(e) => {
												e.preventDefault();
												if (status === 'saving') return;
												dispatch(updateTopicQaViewAction(qa.id));
												dispatch(topicStateChange({ key: 'topicQaRecord', value: qa }));
												router.push(`/support/${qa.slug}`);
											}}
										>
											{stringLimit(qa.question, 35)}
										</a>
									</li>
								))}
							</ul>
						</div>
					</div>
				))}
			</div>
		);
	}

	return (
		<section className="help-support">
			<div className="container-fluid-navigation">
				<div className="search-help">
					<div className="input-group mb-3">
						<input type="text" className="form-control" placeholder="Ask or search for query here."
							aria-label="Ask or search for query here." aria-describedby="basic-addon2"
							value={search}
							onChange={(e) => {
								const value = e.target.value;
								setSearch(value);

								if (value.trim() === '') {
									dispatch(fetchAllTopicsAction({ type: 'active' }));
								}
							}}
							onKeyDown={(e) => {
								if (e.key === 'Enter') {
									dispatch(fetchAllTopicsAction({ type: 'active', search }));
								}
							}}
						/>
						<div className="input-group-append">
							<button
								className="btn btn-primary" type="button"
								onClick={() => {
									dispatch(fetchAllTopicsAction({ type: 'active', search }));
								}}
							>
								Search
							</button>
						</div>
					</div>
				</div>

				<div className="search-content">
					<div className="row">
						<div className="col-sm-9">
							<h2 className="search_browse">Browse by topic.</h2>
							<hr/>
							{renderTopics()}
						</div>

						<div className="col-md-3 contact-section-support">
							<h2 className="search_browse">Contact Us</h2>
							<hr/>
							<p>Email Us</p>
							<br/>

							<ul>
								<li>
									<a
										href={"mailto:" + (generalInfo?.email_one || '<EMAIL>')}
										target="_blank"
										rel="noopener noreferrer">{generalInfo?.email_one || '<EMAIL>'}
									</a>
								</li>
							</ul>
						</div>
					</div>

				</div>
			</div>
		</section>
	)
}

export default Support
