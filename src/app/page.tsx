import AppSlider from '@/components/AppSlider.component';
import FeaturedEventsList from '@/components/event/FeaturedEventsList.component';
import HomeEventsList from '@/components/event/HomeEventsList.component';
import { CaretLeftFilled, CaretRightFilled } from '@ant-design/icons';

export default function Home() {
	return (
		<div>
			<AppSlider />
			{/* <div className='featured-events-list section-container-box'>
				<div className='container-fluid-navigation'>
					<h1 className='app-title-primary'>
            Featured <span className='highlighted-heading'>Events</span>
					</h1>

					<p>
            Most happening events with tons of entertainment in your city by
            authorized organizers, surely you don't want to miss this!{' '}
					</p>

					<div className='event-lists-grid'>
						<div className="row">
							<FeaturedEventsList />
						</div>
					</div>
				</div>

				<div className='view-more-events'>
					<Link href={'/events'}>
						<button className='ripple-eff btn btn-primary btn-lg'>
							<i className='fa fa-mouse-pointer' /> &nbsp;&nbsp; Load More ...
						</button>
					</Link>
				</div>
				<br />
			</div> */}
			<FeaturedEventsList />

			<HomeEventsList />

			{false && (
				<div className="container">
					<div className="category-browse">


						<div className="scroll-btn">
							<h2>Browse by <br /><span>Categories</span></h2>
							<div className='d-flex gap-4'>
								<a href=""><CaretLeftFilled /></a>

								<a href="" ><CaretRightFilled /></a>
							</div>
						</div>


						<div className="cat-content">
							{/* Carousel */}
						</div>
					</div>
				</div>
			)}

		</div>
	);
}
