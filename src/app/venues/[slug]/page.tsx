import AppCrumb from "@/components/common/AppCrumb.component";
import DirectionsMap from "@/components/common/DirectionsMap.component";
import ExpandableContent from "@/components/common/ExpandableContent.component";
import MapComponent from "@/components/common/Map.component";
import { NoResult } from "@/components/common/NoResult";
import EventGridView from "@/components/event/EventGridView";
import BookVenue from "@/components/venue/BookVenue";
import fetchRequest from "@/helpers/fetch.request";
import { VenueModel } from "@/types/models/venue.model";
import { getFullImageUrl, slugger } from "@/utils/general.util";
import { Metadata } from "next";
import pluralize from "pluralize";

async function getVenue(slug: string): Promise<VenueModel | null> {
	try {
		const eventId = slug.split('-').pop();
		const eventDetail = await fetchRequest(`/api/venues/${eventId}`, {
			cache: 'no-store',
		});

		if (!eventDetail) {
			throw new Error("Venue not found");
		}

		return eventDetail as VenueModel;
	} catch {
		return null;
	}
}

// eslint-disable-next-line react-refresh/only-export-components
export async function generateMetadata({
	params
}: {
	params: { slug: string }
}): Promise<Metadata> {
	const slug = params.slug;
	const venue = await getVenue(params.slug);
	if (!venue) {
		return { title: "Venue Not Found" };
	}

	let title = venue.name;
	title += ' | Find the best events, concerts, workshops and gatherings in Nepal';

	const description = venue.meta_desc || venue.about
		? `Explore ${venue.name} on eticketnepal. ${venue.meta_desc || venue.about}`
		: `View ${venue.name}, an exciting venue in Nepal. Book now on eticketnepal.`;

	const other: Record<string, string> = {
		"fb:app_id": process.env.NEXT_PUBLIC_FACEBOOK_CLIENT_ID as string,
	};

	if (venue.address) {
		if (venue.address?.zone) other["geo.region"] = String(venue.address.zone);
		if (venue.address?.city) other["geo.placename"] = String(venue.address.city);

		if (venue.address?.lat != null && venue.address?.lng != null) {
			other["geo.position"] = `${venue.address.lat};${venue.address.lng}`;
			other["ICBM"] = `${venue.address.lat},${venue.address.lng}`;
		}
	}

	return {
		title,
		openGraph: {
			title: `${venue.name} | eticketnepal`,
			url: `https://www.eticketnepal.com/venues/${slug}`,
			type: 'article',
			images: [
				{
					url: (venue.image) ? getFullImageUrl(venue.image) : 'https://placehold.co/600x250/png?text=Venue+Image',
					type: 'image/jpeg',
					width: 1200,
					height: 630,
					alt: venue.name
				},
			]
		},
		description,
		keywords: venue.meta_keywords,
		other,
		twitter: {
			card: 'summary_large_image',
			title: `${venue.name} | eticketnepal`,
			description,
			images: (venue.image) ? [
				getFullImageUrl(venue.image),
			] : ['https://placehold.co/600x250/png?text=Venue+Image'],
		},
	} as Metadata
}

export default async function VenueDetail({
	params
}: {
	params: { slug: string }
}) {
	const venue = await getVenue(params.slug);
	if (!venue) return <NoResult message='Venue Not Found' />;

	const destination = venue?.address?.lat && venue?.address?.lng
		? {
			lat: venue.address.lat,
			lng: venue.address.lng,
		}
		: null;

	return (
		<div>
			<div className="detail-main-img-options">
				<div className="row">
					<div className="col-sm-9 main-image">
						<AppCrumb looper={slugger(venue, "venue")} />
						<br />
						<div className="detail-cover-img">
							<img src={venue.image ? getFullImageUrl(venue.image) : 'https://placehold.co/600x250/png?text=Venue+Image'} />

							{venue.logo ? (
								<div className="logo-image">
									<img src={getFullImageUrl(venue.logo)} />
								</div>
							) : null}
						</div>
						<div className="event-options">
							<div className="row">
								<div className="col-md-9 options">
									{/* <div className="opt-three opt">
									</div>

									<div className="opt-one opt">
									</div> */}

									<div className="opt-two opt">
										<a href="" className="like-button">
											<i className="fa-brands fa-gripfire" />
										</a>
										<p>
											<i>{venue.total_events_hosted}</i> {pluralize("event", venue.total_events_hosted)} hosted
										</p>
									</div>

									<div className="opt-four opt">
										{destination && <DirectionsMap destination={destination} />}
									</div>
								</div>
								<div className="col-md-3 book-now">
									<BookVenue venue={venue} />
								</div>
							</div>
							<div className="upcoming-venue-events">
								<h1>Upcoming events on this venue.</h1>
								<div className="title-underline" />

								<EventGridView events={venue.events || []} />
							</div>
						</div>
					</div>
					<div className="col-sm-3 evt-detail-info">
						<div className="content-padding">
							<h1
								style={{
									overflowWrap: 'break-word',
									hyphens: 'auto',
									WebkitHyphens: 'auto',
									msHyphens: 'auto'
								}}
							>
								{venue.name}
							</h1>
							{venue.about && (
								<ExpandableContent content={venue.about} initialLimit={100} />
							)}

							{venue.attractions && (
								<div className="evt-attraction">
									<hr />

									<h2>Main attractions</h2>
									<ExpandableContent content={venue.attractions} initialLimit={100} />
								</div>
							)}
							<hr />
							<div>
								<h2>Booking Contact</h2>
								<ul className="booking-contact">
									{venue.website && (
										<li>
											<i className="fa-solid fa-globe" />
											{venue.website}
										</li>
									)}

									{venue.phone_number && (
										<li>
											<i className="fa-solid fa-phone" />
											{venue.phone_number}
										</li>
									)}

									{venue.mobile_number && (
										<li>
											<i className="fa-solid fa-mobile" />
											{venue.mobile_number}
										</li>
									)}

									{venue.email && (
										<li>
											<i className="fa-solid fa-envelope" />
											{venue.email}
										</li>
									)}
								</ul>
							</div>
							<hr />
							<div className="event-social-share">
								<h2>Socials</h2>
								<ul>
									{venue.facebook && (
										<li>
											<a href={venue.facebook} target={"_blank"}>
												<img
													src="/images/social/facebook.png"
													alt="facebook"
												/>
											</a>
										</li>
									)}

									{venue.twitter && (
										<li>
											<a href={venue.twitter} target={"_blank"}>
												<img
													src="/images/social/twitter.png"
													alt="twitter"
												/>
											</a>
										</li>
									)}

									{venue.instagram ? (
										<li>
											<a href={venue.instagram} target={"_blank"}>
												<img
													src="/images/social/instagram.png"
													alt="instagram"
												/>
											</a>
										</li>
									) : null}

									{venue.youtube ? (
										<li>
											<a href={venue.youtube} target={"_blank"}>
												<img
													src="/images/social/youtube.png"
													alt="youtube"
												/>
											</a>
										</li>
									) : null}
								</ul>
							</div>
							<br />
							<hr />
							{venue.address && (
								<div className='mt-4'>
									<MapComponent address={venue.address} showInfo={true} />
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
