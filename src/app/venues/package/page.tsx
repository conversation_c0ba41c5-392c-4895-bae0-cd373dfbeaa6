const packageDetails = () => {
	const isLiked = false;
	const isSubscribed = false;
	return <>
		<div className="detail-main-img-options">
			<div className="row">
				<div className="col-sm-9 main-image">
					{/* {this.generateAppSlug()} */}
					<div className={"detail-cover-img"}>
						{/* <img src={selected.image.full} /> */}
						{/* {selected.logo ? ( */}
						<div className="logo-image">
							<img src={'/images/logo-full.png'} />
							{/* <img src={selected.logo} /> */}
						</div>
						{/* ) : null} */}
					</div>
					<div className="event-options">
						<div className="row">
							<div className="col-md-9 options">
								<div className="opt-three opt">
									{/* <h2>23rd<span>&nbsp; June Onwards</span></h2> */}
								</div>
								<div className="opt-one opt">
									{isLiked ? (
										<a
											href=""
											// onClick={(e) => {
											// 	e.preventDefault();
											// 	this.props.likeOrSubscribeVenue();
											// }}
											className="like-button"
										>
											<i className="fas fa-heart" />
										</a>
									) : (
										<a
											href=""
											// onClick={(e) => {
											// 	e.preventDefault();
											// 	this.props.likeOrSubscribeVenue();
											// }}
											className="like-button"
										>
											<i className="far fa-heart" />
										</a>
									)}

									<p>
										{/* <i>{selected.like_count}</i>{" "} */}
										<i>5</i>{" "}
										{/* {pluralize("like", selected.like_count)} */}
									</p>
								</div>
								<div className="opt-two opt">
									<a href="" className="like-button">
										<i className="fab fa-gripfire" />
									</a>
									<p>
										<i>103</i> &nbsp; events hosted
									</p>
								</div>
								<div className="opt-four opt">
									{isSubscribed ? (
										<button
											// onClick={() =>
											// 	this.props.likeOrSubscribeVenue("subscribe")
											// }
											className="btn btn-light active"
										>
                                                    Unsubscribe
										</button>
									) : (
										<button
											// onClick={() =>
											// 	this.props.likeOrSubscribeVenue("subscribe")
											// }
											className="btn btn-light"
										>
                                                        Subscribe this venue
										</button>
									)}

									<p>
										<a href="">
											<i className="far fa-compass" />
                                                    &nbsp; Get directions
										</a>
									</p>
								</div>
							</div>
							<div className="col-md-3 book-now" />
						</div>
						<div className="upcoming-venue-events">
							<h1>Upcomming events on this venue.</h1>
							<div className="title-underline" />

							<div className="event-lists-grid">
								{/* <div className="row">{this.renderPackages()}</div> */}
							</div>
						</div>
					</div>

				</div>
			</div>
		</div>
	</>
}

export default packageDetails;
