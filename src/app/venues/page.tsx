"use client";

// import Category from "@/components/common/category";
import { NoResult } from "@/components/common/NoResult";

export default function Venues() {
	// Retrieve all cookies
	// const cookies = document.cookie;

	// // Find the `userInfo` cookie
	// const userInfoCookie = cookies
	// 	.split('; ')
	// 	.find(cookie => cookie.startsWith('userInfo='));

	// // Parse if it exists
	// if (userInfoCookie) {
	// 	const userInfoValue = userInfoCookie.split('=')[1];
	// 	const userInfo = JSON.parse(decodeURIComponent(userInfoValue));
	// 	console.log(userInfo);
	// } else {
	// 	console.log('userInfo cookie not found');
	// }
	// const categories = [
	// 	{
	// 		icon: "fas fa-laugh",
	// 		id: 1,
	// 		name: "comedy",
	// 		slug: "comedy",
	// 		sub_id: null,
	// 	},
	// 	{
	// 		icon: "fas fa-chair",
	// 		id: 2,
	// 		name: "Conference & Workshops",
	// 		slug: "conference-workshops",
	// 		sub_id: null,
	// 	},
	// 	{
	// 		icon: "fas fa-award",
	// 		id: 3,
	// 		name: "Exhibition & Performances",
	// 		slug: "exhibition-performances",
	// 		sub_id: null,
	// 	},

	return <>
		<div className="featured-events-list section-container-box">
			<div className="container-fluid-navigation">
				<div className="venues-header">
					<h1 className="app-title-primary">
						Choose <span className="highlighted-heading">Venues</span>
					</h1>
					<p className="venues-description">
						Most happening events with tons of entertainment in your city by authorized organizers,
						surely you don't want to miss this!
					</p>
				</div>

				<div className="all-events-container">
					<div className="search-filter-event">
						<div className="search-box-options">
							<div className="search-row">
								<div className="search-form-container">
									<form method={'post'} onSubmit={(e) => {
										e.preventDefault();
										// this.props.fetchFilteredVenues();
									}}>
										<div className="search-input-group">
											<div className="input-group">
												<div className="input-group-prepend">
													<div className="input-group-text">
														<i className="fa fa-search" />
													</div>
												</div>
												<input autoComplete="off" type="text"
													className="form-control search-event"
													spellCheck="false"
													id="inlineFormInputGroupUsername" placeholder="Venue name.."/>
											</div>
										</div>
									</form>
								</div>

								<div className="nearby-venues">
									<a href="">
										<i className="fa fa-compass" /> Near by venues
									</a>
								</div>
							</div>
						</div>
					</div>
					{/* <Category categories={categories}></Category> */}

					{/* <Category filterOptions={this.props.venue.filter} display={8} categories={categories}
						filterCategory={this.props.fetchFilteredVenues} /> */}
				</div>
			</div>
			<NoResult></NoResult>

		</div>
	</>
}
