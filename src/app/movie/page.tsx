"use client";
import { AutoComplete, Breadcrumb, Rate } from "antd";
import { HomeOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { Nav, NavItem, NavLink, TabContent, TabPane } from "reactstrap";
import { useState } from "react";

const Movie = () => {
	const [activeTab, setActiveTab] = useState<string>('1');
	return <>
		<div className="detail-main-img-options">
			<div className="row">
				<div className="col-sm-9 main-image">
					<Breadcrumb
						separator=">"
						items={[
							{
								title: <HomeOutlined />,
							},
							{
								title: 'Movies',
								href: '',
							},
							{
								title: 'Avenger - Age of ultron',
							},
						]}
					/>
					<br />
					<div className="main-image-container">
						<img src="/images/slider/Slider-4-Movies.jpeg"/>
						<img src="/images/play_icon.png" className={'play-icon'} alt=""/>
					</div>
					<div className="event-options">
						<div className="row">
							<div className="col-md-9 options">
								<div className="opt-three opt">
									<h2>27th<span>&nbsp; April 2018</span></h2>
								</div>
								<div className="opt-one opt">
									<a href="" className="like-button"><ClockCircleOutlined/></a>
									<p>2 hrs 29 mins</p>
								</div>
								<div className="opt-two opt">
									<div className="user-rating">
										<div className="ratings-posted">
											<h3>4.5</h3>
											<Rate allowClear={true} defaultValue={4.5} disabled={true}
												allowHalf={true}
												className={'rate-it-icon'}/>
										</div>
										<p>User reviews</p>.
									</div>
								</div>
								<div className="opt-four opt">
									<button className="btn btn-light">Subscribe this event</button>
									<p><a href=""><i className="far fa-compass"/>&nbsp; Get directions</a></p>
								</div>
							</div>
							<div className="col-md-3 rate-movie">
								<Rate allowClear={true} allowHalf={true} className={'rate-it-icon'}/>
								<span className={'rate-value'}>0.0</span>
								<br/>
								<span><u>RATE IT</u></span>
							</div>
						</div>
					</div>
					<div className="movies-info">
						<div className="row event-brief-tab">
							<div className="col-md-12">
								<Nav tabs>
									<NavItem>
										<NavLink className={activeTab === '1' ? 'active' : ''}
											onClick={() => setActiveTab('1')}
										>
                                            Casts
										</NavLink>
									</NavItem>
									<NavItem>
										<NavLink className={activeTab === '2' ? 'active' : ''}
											onClick={() => setActiveTab('2')}
										>
                                           User Reviews
										</NavLink>
									</NavItem>
								</Nav>
								<TabContent activeTab={activeTab}>
									<TabPane tabId="1">
										<div className="casts-list">
											<div className="row">
												<div className="col-md-2">
													<div className="cast-image">
														<img src='/static/img/casts/chris.jpeg' alt="chris"/>
													</div>
													<h4><a href="">Chris Hemsworth</a></h4>
													<p>Actor</p>
													<p>As THOR</p>
												</div>
												<div className="col-md-2">
													<div className="cast-image">
														<img src='/static/img/casts/tony.jpeg' alt=""/>
													</div>
													<h4><a href="">Chris Hemsworth</a></h4>
													<p>Actor</p>
													<p>As Tony Stark (Iron man)</p>
												</div>

												<div className="col-md-2">
													<div className="cast-image">
														<img src='/static/img/casts/ruffalo.jpeg' alt=""/>
													</div>
													<h4><a href="">Mark Ruffalo</a></h4>
													<p>Actor</p>
													<p>As Bruce (Hulk)</p>
												</div>

												<div className="col-md-2">
													<div className="cast-image">
														<img src='/static/img/casts/scarlet.jpeg' alt=""/>
													</div>
													<h4><a href="">Scarlett Johansson</a></h4>
													<p>Actress</p>
													<p>As Black Widow</p>
												</div>

												<div className="col-md-2">
													<div className="cast-image">
														<img src='/static/img/casts/chrisevan.jpeg' alt=""/>
													</div>
													<h4><a href="">Chris Evans</a></h4>
													<p>Actor</p>
													<p>As Captain America</p>
												</div>
											</div>
										</div>
										<ul className={'casts-list'}>
											<ul>
												<li>
												</li>
											</ul>
										</ul>
									</TabPane>
									<TabPane tabId="2">
										<div className="comment-section">
											<div className="comment-grid-view">
												<div className="commenter-img">
													<img src="/static/img/faces/2.jpeg" alt=""/>
												</div>
												<div className="comment-action-box">
													<h4>Kishor. <span>6 minutes ago</span></h4>
													<p>Lorem ipsum dolor sit amet, consectetur
                                                                adipisicing elit. Aliquam
                                                                culpa cumque
                                                                doloremque, error esse eum facilis ipsum</p>
													<Rate style={{ fontSize: 12 }} allowClear={true}
														defaultValue={5} disabled={true}
														allowHalf={true}
														className={'rate-it-icon'}
													/>
												</div>
											</div>

											<div className="comment-grid-view">
												<div className="commenter-img">
													<img src="/static/img/user_avatar.jpg" alt=""/>
												</div>
												<div className="comment-action-box">
													<h4>Junaid. <span>2 days ago</span></h4>
													<p>Lorem ipsum dolor sit amet, consectetur
                                                                adipisicing elit. Aliquam
                                                                culpa. Lorem ipsum dolor sit amet, consectetur
                                                                adipisicing elit. Assumenda aut beatae delectus
                                                                distinctio eaque esse.</p>
													<Rate style={{ fontSize: 12 }} allowClear={true}
														defaultValue={5} disabled={true}
														allowHalf={true}
														className={'rate-it-icon'}
													/>
												</div>
											</div>

											<div className="comment-grid-view">
												<div className="commenter-img">
													<img src="/static/img/faces/3.jpeg" alt=""/>
												</div>
												<div className="comment-action-box">
													<h4>Sagar. <span>2 minutes ago</span></h4>
													<p>Lorem ipsum dolor sit amet, consectetur
                                                                adipisicing elit. Aliquam
                                                                culpa.</p>
													<Rate style={{ fontSize: 12 }} allowClear={true}
														defaultValue={4.5} disabled={true}
														allowHalf={true}
														className={'rate-it-icon'}
													/>
												</div>
											</div>


											<a href="" className="load-more-events">Load more reviews....</a>
										</div>

									</TabPane>

								</TabContent>
							</div>
						</div>
					</div>

				</div>
				<div className="col-sm-3 evt-detail-info">
					<div className="content-padding">
						<h1>Avengers - Infinity war.</h1>
						<ul className={'category-list'}>
							<li><a href="">Action</a></li>
							<li><a href="">Drama</a></li>
							<li><a href="">Thriller</a></li>
						</ul>
						<br/>
						<p>As the Avengers and their allies have continued to protect the world from threats too
                                    large for any one hero to handle, a new danger has emerged from the cosmic shadows:
                                    Thanos. A despot of intergalactic infamy, his goal is to collect all six Infinity
                                    Stones, artifacts of unimaginable power, and use them to inflict his twisted will on
                                    all of reality.</p>
						<br/>
						<hr/>

						<h2>Location</h2>
						<p>Info's like Location, timing and tickets prices will be shown here.</p>
						<AutoComplete
							style={{ width: 200 }}
							placeholder="Search by City"
						/>
						<ul className="location-list">
							<li>
								<h2>QFX Kumari Hall <span>&lt; 1 KM</span></h2>
								<p>Tukucha River, 01-4442220</p>
								<span>Mon to Fri</span>
								{/* <ReactCSSTransitionGroup transitionName={'example'}
									transitionEnterTimeout={300}
									transitionLeaveTimeout={300}>
									{movieInfo}
								</ReactCSSTransitionGroup> */}
							</li>

							<li>
								<h2>Astanarayan Pictures <span>2.6 KM</span></h2>
								<p>Tukucha River, 01-4442220</p>
								<span>Mon to Fri</span>
							</li>

							<li>
								<h2>QFX Kumari Hall <span> 5.1 KM</span></h2>
								<p>Tukucha River, 01-4442220</p>
								<span>Mon to Fri</span>
							</li>

							<li>
								<h2>Jay Nepal Cinema <span>&lt; 2 KM</span></h2>
								<p>Tukucha River, 01-4442220</p>
								<span>Mon to Fri</span>
							</li>
						</ul>
						<div className="clearfix"/>
						<div className="event-social-share">
							<ul>
								<li><a href=""><img src="/images/social/facebook_2_3.png" alt=""/></a></li>
								<li><a href=""><img src="/images/social/58e9196deb97430e819064f6.png"
									alt=""/></a></li>
								<li><a href=""><img src="/images/social/googleplus.png" alt=""/></a></li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	</>
}

export default Movie;
