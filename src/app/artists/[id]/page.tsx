import { Tag } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import { ArtistModel } from '@/types/models/artist.model';
import { NoResult } from '@/components/common/NoResult';
import fetchRequest from '@/helpers/fetch.request';
import dayjs from 'dayjs';
import { EventModel } from '@/types/models/event.model';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { getFullImageUrl, stringLimit } from '@/utils/general.util';
import Link from 'next/link';

dayjs.extend(advancedFormat);

async function getArtistDetails(id: number): Promise<ArtistModel | null> {
	try {
		const artistDetail = await fetchRequest(`/api/artists/${id}`, {
			next: { revalidate: 600 },
		});

		if (!artistDetail) {
			throw new Error('Artist not found');
		}

		return artistDetail as ArtistModel;
	} catch {
		return null;
	}
}

export default async function ArtistPage({ params }: {params: {id: string}}) {
	const artist = await getArtistDetails(Number(params.id));
	if (!artist) return <NoResult message='Artist Not Found' />;

	return (
		<div>
			<div className="artist_banner">
				<div className="detail-main-img-options artist_intro">
					<div className="artist_title">
						<div className="artist_image">
							<img src={artist.image ? getFullImageUrl(artist.image) : 'https://placehold.co/200x200/png?text=Artist+Image'} alt={artist.name} />
						</div>
						<div className="artist_name">
							<h2>{artist.name}</h2>
							<p>
								{artist.title.split(',').map((art: string, i: number, arr: string[]) => (
									<span key={i}>
										{art.trim()}
										{i < arr.length - 1 ? ' / ' : ''}
									</span>
								))}
							</p>
							<div className="extra_info">
								{artist.dob && (
									<Tag>
										<strong>{dayjs(artist.dob).format('YYYY-MM-DD')}</strong>
									</Tag>
								)}
								{artist.home_town && (
									<Tag>
										<strong>{artist.home_town}</strong>
									</Tag>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>

			<div className="artist_tab">
				<div className="container">
					<div className="tab_item">
						<h3>About Artist</h3>
					</div>
				</div>
			</div>

			<div className="artist_details">
				<div className="container">
					<div className="row">
						<div className="col-lg-8 col-md-12 col-sm-12">
							<div className="details">
								<p>{artist?.about}</p>
							</div>
						</div>
						<div className="col-lg-4 col-md-12 col-sm-12">
							<div className="artist_relation">
								<h3 className='d-flex align-middle gap-2'>
                  Events <RightOutlined />
								</h3>
								<div className="events">
									{artist.events && artist.events.length > 0 ? (
										artist.events.map((event: EventModel, idx: number) => (
											<div className="evt_list_view" key={idx}>
												<div className="image-con">
													<img
														src={
															event.image
																? getFullImageUrl(event.image)
																: 'https://placehold.co/120x80/png?text=Event+Image'
														}
														alt={event.title || 'Event Image'}
														width={120}
														height={80}
													/>
												</div>
												<div className="event_det">
													<h3>
														<Link href={`/events/${event.slug}-${event.id}`}>
															{event.title}
														</Link>
													</h3>
													<p>
														{event.venues && event.venues.length > 0
															? stringLimit(event.venues[0].name as string, 20)
															: 'N/A'}
															,{' '}
														{event.start_date_time
															? dayjs(event.start_date_time).format('Do MMM YYYY')
															: 'N/A'}
													</p>
												</div>
												<div className="event_action" />
											</div>
										))
									) : (
										<p>No Events</p>
									)}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
