import fetchRequest from "@/helpers/fetch.request";
import { EventModel } from "@/types/models/event.model";
import { Metadata } from "next";
import EventBuyPage from "./eventBuyPage";
import { getFullImageUrl } from "@/utils/general.util";

async function getEvent(slug: string): Promise<EventModel | null> {
	try {
		const eventId = slug.split('-').pop(); // Extract event ID from slug
		const eventDetail = await fetchRequest(`/api/events/${eventId}`, {
			cache: "no-store",
		});

		if (!eventDetail) {
			throw new Error("Event not found");
		}

		return eventDetail as EventModel;
	} catch {
		return null;
	}
}

export async function generateMetadata({
	params,
}: {
	params: { id: string };
}): Promise<Metadata> {
	const { id } = params;
	const event = await getEvent(id); // Fetch event details

	if (!event) {
		return { title: "Event Not Found" };
	}

	const categoryText = event.categories?.map((category) => category.name).join(", ");
	const title = `${event.title} - ${categoryText} in Nepal | Find the best events, concerts, workshops, and gatherings in Nepal`;

	const description = event.meta_desc || event.about
		? `Explore ${event.title} on eticketnepal. ${event.meta_desc || event.about}`
		: `Join us for ${event.title}, an exciting ${categoryText} event in Nepal. Get your tickets now on eticketnepal.`;

	return {
		title,
		openGraph: {
			title: `${event.title} | eticketnepal`,
			url: `https://www.eticketnepal.com/events/${id}`,
			type: "article",
			images: [
				{
					url: event.image
						? getFullImageUrl(event.image)
						: "https://placehold.co/600x250/png?text=Event+Image",
					type: "image/jpeg",
					width: 1200,
					height: 630,
					alt: event.title,
				},
			],
		},
		description,
		keywords: event.meta_keywords,
		other: {
			"fb:app_id": process.env.NEXT_PUBLIC_FACEBOOK_CLIENT_ID as string,
		},
		twitter: {
			card: "summary_large_image",
			title: `${event.title} | eticketnepal`,
			description,
			images: event.image
				? [getFullImageUrl(event.image)]
				: ["https://placehold.co/600x250/png?text=Event+Image"],
		},
	} as Metadata;
}

export default function Buy({
	params,
	searchParams,
}: {
	params: { id: string };
	searchParams: { ticket?: string; venue?: string; ticketCount?: string };
}) {
	// const ticket = searchParams.ticket ?? "N/A";
	// const venue = searchParams.venue ?? "N/A";
	// const ticketCount = searchParams.ticketCount ?? "N/A";

	return (
		<>
			<EventBuyPage params={params} searchParams={searchParams} />
		</>
	);
}
