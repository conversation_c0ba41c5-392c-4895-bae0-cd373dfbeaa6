'use client'

import React, { useEffect, useRef, useState } from 'react'
import { Steps, Button, Form, Input, Table, Spin } from 'antd'
import { UserOutlined, CreditCardOutlined, SmileOutlined } from '@ant-design/icons'
import Image from 'next/image'
import AppModal from '@/components/common/AppModal'
import { ExpiryCountdown } from '@/components/ExpiryCountdown.component'
import { AppDispatch, RootState } from '@/store'
import { useDispatch, useSelector } from 'react-redux'
import { fetchEventAction, resendEventTicketEmailAction, khaltiPayment, verifyEsewaPayment, verifyKhaltiPayment } from '@/store/slices/event.slice'
import { ServerStatus } from '@/types/index.types'
import { EventModel } from '@/types/models/event.model'
import { useSearchParams } from 'next/navigation'
import dayjs from 'dayjs'
import { EventTicketModel } from '@/types/models/event-ticket.model'
import { VenueModel } from '@/types/models/venue.model'
import pluralize from 'pluralize'
import CryptoJS from "crypto-js";
import { v4 as uuidv4 } from 'uuid';
import { ActiveTicketSessionModel } from '@/types/models/active-ticket-session.model'
import { fetchActiveTicketSessionAction } from '@/store/slices/purchases.slice'
import { getEncryptedCookie } from '@/utils/cookie.util'
import { UserModel } from '@/types/models/user.model'
import { useRouter } from 'next-nprogress-bar'
import { getFullImageUrl, getFullName } from '@/utils/general.util'

interface BuyTicketsProps {
    params: {
		id: string; // Comes from URL, so it's a string
	};
	searchParams: {
		ticket?: string;
		venue?: string;
		ticketCount?: string;
	};
}

interface BuyState {
  steps: Record<'step1' | 'step2' | 'step3', { active: boolean; done: boolean }>;
  user: { full_name: string; email: string; phone: string };
  payment: { selectedGateway: string };
  // expiry: number;
}

interface EsewaFormData {
  amount: number;
  tax_amount: string;
  total_amount: number;
  transaction_uuid: string;
  product_code?: string;
  product_service_charge: string;
  product_delivery_charge: string;
  success_url: string;
  failure_url: string;
  signature: string;
  secret: string;
  signed_field_names: string;
}

export default function EventBuyPage({ params }: BuyTicketsProps) {
	const dispatch = useDispatch<AppDispatch>()
	const [form] = Form.useForm();
	const esewaFormRef = useRef<HTMLFormElement>(null);
	const buyParams = useSearchParams();
	const router = useRouter();

	const [searchParams, setSearchParams] = useState<URLSearchParams | null>(null);
	const [toggled, setToggled] = useState<boolean>(false);
	const [showExpiredModal, setShowExpiredModal] = useState<boolean>(false);
	const [buyerId, setBuyerId] = useState<number | null>(null); // for resending ticket email
	const [isVerifying, setIsVerifying] = useState<boolean>(false);

	const [formData, setFormData] = useState<EsewaFormData>({
		amount: 0,
		tax_amount: '0',
		total_amount: 0,
		transaction_uuid: '',
		product_code: process.env.NEXT_PUBLIC_ESEWA_PRODUCT_CODE,
		product_service_charge: '0',
		product_delivery_charge: '0',
		success_url: '',
		failure_url: '',
		signature: '',
		secret: '8gBm/:&EnhH.1/q',
		signed_field_names: 'total_amount,transaction_uuid,product_code',
	});

	const [buyState, setBuyState] = useState<BuyState>({
		steps: {
			step1: { active: true, done: false },
			step2: { active: false, done: false },
			step3: { active: false, done: false },
		},
		user: { full_name: '', email: '', phone: '' },
		payment: { selectedGateway: '' },
		// expiry: Date.now() + 900000,
	});

	const { record: selectedEvent, status } = useSelector((state: RootState) => state.event) as {record: EventModel, status: ServerStatus};
	const { ticketSession, status: purchasesStatus } = useSelector((state: RootState) => state.purchases) as {ticketSession: ActiveTicketSessionModel, status: ServerStatus};

	const ticketId = buyParams.get('ticket');
	const venueId = buyParams.get('venue');
	const ticketCount = buyParams.get('ticketCount');
	const sessionId = buyParams.get('sessionId');

	const selectedTicket = selectedEvent?.tickets?.find((t: EventTicketModel) => t.id === Number(ticketId));
	const selectedVenue = selectedEvent?.venues?.find((v: VenueModel) => v.id === Number(venueId));
	const selectedDate = selectedEvent?.start_date_time;

	const generateSignature = (
		total_amount: number,
		transaction_uuid: string,
		product_code: string,
		secret: string
	) => {
		const hashString = `total_amount=${total_amount},transaction_uuid=${transaction_uuid},product_code=${product_code}`;
		const hash = CryptoJS.HmacSHA256(hashString, secret);
		const hashedSignature = CryptoJS.enc.Base64.stringify(hash);
		return hashedSignature;
	};

	const updateStep = (step: number) => {
		setBuyState(prev => ({
			...prev,
			steps: {
				step1: { active: step === 1, done: step > 1 },
				step2: { active: step === 2, done: step > 2 },
				step3: { active: step === 3, done: false },
			},
		}));
	};

	const onFinish = (values: BuyState['user']) => {
		setBuyState(prev => ({ ...prev, user: values }));
		updateStep(2);
	};

	const selectGateway = (gateway: string) => {
		setBuyState(prev => ({
			...prev,
			payment: { ...prev.payment, selectedGateway: gateway },
		}));
	};

	const resendTicketEmail = () => {
		if (!buyerId) return;
		dispatch(resendEventTicketEmailAction(buyerId));
	}

	const confirmPayment = async () => {
		const amount = Number(selectedTicket?.price) * Number(ticketCount);

		if (buyState.payment.selectedGateway === 'khalti') {
			const res = await dispatch(
				khaltiPayment({
					amount,
					purchase_order_name: selectedEvent?.title,
					purchase_order_id: params.id,
					return_url: `${window.location.href}&payment_gateway=khalti`,
				})
			);
			const { payment_url } = res?.data?.data || {};
			if (payment_url) window.location.href = payment_url;
		}

		if (buyState.payment.selectedGateway === 'esewa') {
			const newUUID = uuidv4(); // generate fresh UUID
			const successUrl = `${window.location.href}&payment_gateway=esewa&paymentsuccess=true&uuid=${newUUID}`;
			const failureUrl = `${window.location.href}&payment_gateway=esewa&paymentsuccess=false&uuid=${newUUID}`;
			const signature = generateSignature(amount, newUUID, formData.product_code!, formData.secret);

			setFormData(prev => ({
				...prev,
				total_amount: amount,
				amount,
				signature,
				transaction_uuid: newUUID,
				success_url: successUrl,
				failure_url: failureUrl,
			}));
		}
	};

	useEffect(() => {
		if (sessionId) {
			if (!ticketSession) dispatch(fetchActiveTicketSessionAction(Number(sessionId)));
		} else {
			window.location.href = `/events`;
			return;
		}
		if (!selectedEvent) dispatch(fetchEventAction(params.id));
	}, [dispatch]);

	useEffect(() => {
		if (typeof window !== 'undefined') {
			const currentUrl = window.location.href;
			// Check if "?data=" is preceded by anything other than the first query parameter indicator
			if (currentUrl.indexOf('?data=') > 0) {
			// Replace '?data=' with '&data=' only when it's not the first query marker.
				const fixedUrl = currentUrl.replace('?data=', '&data=');
				window.history.replaceState(null, '', fixedUrl);
				setSearchParams(new URLSearchParams(new URL(fixedUrl).search));
			} else {
				// Otherwise just update searchParams
				setSearchParams(new URLSearchParams(window.location.search));
			}
		}
	}, []);

	useEffect(() => {
		const saved = localStorage.getItem('buyFormState');
		const parsed = saved ? JSON.parse(saved) : null;

		if (parsed) {
			setBuyState(parsed);
			form.setFieldsValue(parsed.user);
		} else {
			const user = getEncryptedCookie('_eticket_user_') as UserModel;
			const newState: BuyState = {
				steps: {
					step1: { active: true, done: false },
					step2: { active: false, done: false },
					step3: { active: false, done: false },
				},
				user: {
					full_name: getFullName(user),
					email: user.email || '',
					phone: user.phone || '',
				},
				payment: { selectedGateway: '' },
				// expiry: Date.now() + 900000,
			};
			form.setFieldsValue(newState.user);
			setBuyState(newState);
			localStorage.setItem('buyFormState', JSON.stringify(newState));
		}
	}, [form]);

	useEffect(() => {
		const success = searchParams?.get('paymentsuccess') === 'true';
		const gateway = searchParams?.get('payment_gateway');

		if (!buyState.user.full_name || !buyState.user.email || !buyState.user.phone) {
			return
		}

		if (selectedTicket && searchParams && gateway === 'esewa' && success && searchParams.get('data')) {
			setIsVerifying(true);
			dispatch(
				verifyEsewaPayment({
					total_amount: Number(selectedTicket.price) * Number(ticketCount),
					transaction_uuid: searchParams.get('uuid')!,
					venue: searchParams.get('venue')!,
					ticketCount: searchParams.get('ticketCount')!,
					ticket: searchParams.get('ticket')!,
					event_id: params.id,
					full_name: buyState.user.full_name,
					email: buyState.user.email,
					phone: buyState.user.phone,
				})
			).then((res) => {
				if (res.status === 201) {
					if (res.data?.data?.buyerId) {
						setBuyerId(res.data.data.buyerId);
					}
					localStorage.removeItem('buyFormState');
					setToggled(true);
					updateStep(3);
					const cleanUrl = window.location.href.split('&payment_gateway')[0];
					window.history.replaceState(null, '', cleanUrl);
				}
			}).finally(() => {
				setIsVerifying(false);
			});
		}

		if (selectedTicket && searchParams && gateway === 'khalti' && searchParams.get('status') !== 'User canceled') {
			setIsVerifying(true);
			dispatch(
				verifyKhaltiPayment({
					pidx: searchParams.get('pidx')!,
					transaction_id: searchParams.get('transaction_id')!,
					amount: searchParams.get('amount')!,
					purchase_order_id: searchParams.get('purchase_order_id')!,
					purchase_order_name: searchParams.get('purchase_order_name')!,
					total_amount: searchParams.get('total_amount')!,
					venue: searchParams.get('venue')!,
					ticketCount: searchParams.get('ticketCount')!,
					ticket: searchParams.get('ticket')!,
					event_id: params.id,
					full_name: buyState.user.full_name,
					email: buyState.user.email,
					phone: buyState.user.phone,
				})
			).then((res) => {
				if (res.status === 201) {
					if (res.data?.data?.buyerId) {
						setBuyerId(res.data.data.buyerId);
					}
					localStorage.removeItem('buyFormState');
					setToggled(true);
					updateStep(3);
					const cleanUrl = window.location.href.split('&payment_gateway')[0];
					window.history.replaceState(null, '', cleanUrl);
				}
			}).finally(() => {
				setIsVerifying(false);
			});
		}
	}, [searchParams, dispatch, selectedTicket, ticketCount, buyState.user]);

	useEffect(() => {
		if (formData.signature) esewaFormRef.current?.submit();
	}, [formData.signature]);

	const formItemLayout = {
		labelCol: {
			xs: { span: 24 },
			sm: { span: 8 },
		},
		wrapperCol: {
			xs: { span: 24 },
			sm: { span: 16 },
		},
	};

	const tailFormItemLayout = {
		wrapperCol: {
			xs: {
				span: 24,
				offset: 0,
			},
			sm: {
				span: 16,
				offset: 8,
			},
		},
	};

	const renderCurrentStep = () => {
		if (buyState.steps.step1.active) {
			return (
				<Form form={form} onFinish={onFinish} layout="vertical" initialValues={buyState.user}
					onValuesChange={(_, allValues) => {
						const newState = { ...buyState, user: allValues };
						setBuyState(newState);
						localStorage.setItem("buyFormState", JSON.stringify(newState));
					}}
				>
					<Form.Item
						{...formItemLayout}
						name="full_name"
						label="Full name"
						rules={[{ required: true, message: 'Please input your Full name!' }]}
					>
						<Input placeholder="What is your full name?" width={400} value={buyState.user.full_name} />
					</Form.Item>
					<Form.Item
						{...formItemLayout}
						name="phone"
						label="Phone Number"
						rules={[
							{ required: true, message: 'Please input your phone number!' },
							{ pattern: /^[0-9]+$/, message: 'Phone number must contain only digits!' },
							{ len: 10, message: 'Phone number must be exactly 10 digits long!' },
						]}
					>
						<Input addonBefore="+977" placeholder="What is the best # to reach you?" value={buyState.user.phone}/>
					</Form.Item>
					<Form.Item
						{...formItemLayout}
						name="email"
						label="E-mail address"
						rules={[
							{ required: true, message: 'Please input your E-mail address!' },
							{ type: 'email', message: 'The input is not valid E-mail!' },
						]}
						extra="Make sure you have entered a valid and verified email address."
					>
						<Input placeholder="What is your email address?" value={buyState.user.email}/>
					</Form.Item>
					<Form.Item {...tailFormItemLayout}>
						<Button type="primary" htmlType="submit">Confirm</Button>
					</Form.Item>
				</Form>
			)
		} else if (buyState.steps.step2.active) {
			return (
				<div className="payment-methods">
					<div className={`p-methods-con ${buyState.payment.selectedGateway === 'esewa' ? 'selected' : ''}`}
						onClick={() => selectGateway('esewa')}>
						<div className="p-method">
							<Image src="/images/pmethods/esewa-logo.png" width={100} height={50} alt="eSewa" />
						</div>
					</div>
					<div className={`p-methods-con ${buyState.payment.selectedGateway === 'khalti' ? 'selected' : ''}`}
						onClick={() => selectGateway('khalti')}>
						<div className="p-method">
							<Image src="/images/pmethods/khalti-logo.png" width={100} height={50} alt="Khalti" />
						</div>
					</div>
					<br />
					<br />
					<Button onClick={confirmPayment} disabled={!buyState.payment.selectedGateway} size="large" type="primary">
            Confirm payment
						{buyState.payment.selectedGateway ? ` with ${buyState.payment.selectedGateway.toUpperCase()}` : ''}
					</Button>
					<Button
						type='default'
						size='large'
						className='previous-btn'
						onClick={() => updateStep(1)}
					>
						Previous
					</Button>
				</div>
			)
		} else if (buyState.steps.step3.active) {
			return (
				<div className="payment-methods payment_success login-input-box">
					<h1>Woohoo! Payment was successful.</h1>
					<p style={{ fontSize: 14, fontWeight: 300 }}>
						<b style={{ fontWeight: 500 }}>Thank you for using eticketnepal. Your payment request was successful.</b>
						<br />
						<br /> <br />Event ticket has been issued to <b style={{ fontWeight: 500 }}>{buyState.user.full_name}</b>.
            Please visit your email account <b style={{ fontWeight: 500 }}>{buyState.user.email}</b><br /> to download a copy of your ticket.
					</p>
					<div className="more-links">
						<p>
							Didn't receive ticket? Please check your spam folder or you can <a
								href="#"
								onClick={(e) => {
									if (status === 'waiting') return;
									e.preventDefault();
									resendTicketEmail();
								}}
							>{status === 'waiting' ? 'Sending...' : 'Try again.'}</a>
						</p>
					</div>
				</div>
			)
		}
	}

	const columns = [
		{ title: 'Item', dataIndex: 'item', key: 'item' },
		{ title: 'Price', dataIndex: 'price', key: 'price' },
	]

	const data = selectedTicket ? [
		{
			key: '1',
			item: `${selectedTicket.name} (${ticketCount} ${pluralize('ticket', Number(ticketCount))})`,
			price: selectedTicket.ticket_type === 'paid'
				? `RS. ${Number(selectedTicket.price) * Number(ticketCount)}`
				: 'Free',
		},
		// { key: '2', item: 'Internet handling fees (15%)', price: `RS.${Math.round(buyInfo.purchase_info.ticket.price * buyInfo.purchase_info.ticket_count * 0.15)}` },
		{
			key: '3',
			item: 'Sub total',
			// price: `RS.${(buyInfo.purchase_info.ticket.price * buyInfo.purchase_info.ticket_count) + Math.round(buyInfo.purchase_info.ticket.price * buyInfo.purchase_info.ticket_count * 0.15)}`
			price: selectedTicket.ticket_type === 'paid'
				? `RS. ${Number(selectedTicket.price) * Number(ticketCount)}`
				: 'Free',
		},
	] : [];

	return (
		<Spin spinning={status === 'fetching' || purchasesStatus === 'fetching' || isVerifying} size='large'>
			<div className={`ticket-summary ${toggled ? 'toggled' : ''}`}>
				<div className="container-fluid-navigation">
					<div className="ticket-summary-det">
						<div className="row">
							<div className="col-sm-12">
								<div className="events-details-final">
									<div className="ticket-summary-img">
										<img src={selectedEvent?.image ? getFullImageUrl(selectedEvent.image) : 'https://placehold.co/400x400/png?text=Event+Image'} width={400} height={400} alt="Event" />
										{/* <a href="#">
											<i className="fa fa-save" /> &nbsp;Save for later
										</a> */}
									</div>
									<div className="summary-details">
										<p>
											{dayjs(selectedDate).format('DD MMMM YYYY')}{' '}
											<span>on</span>{' '}
											<a href="#">{selectedVenue?.name}</a>
										</p>
										<h1>{selectedEvent?.title}</h1>
										<p>{selectedEvent?.about}</p>
									</div>
								</div>
								<div className="ticket-card">
									{/* <div className="ticket-top-left">
									</div>
									<div className="ticket-top-right">
									</div> */}

									<h4>Amount summary</h4>
									<div className="amount-summary">
										<Table columns={columns} dataSource={data} pagination={false} />
									</div>
								</div>
								{ticketSession && (
									<div className="ticket_expiry">
										<ExpiryCountdown expiryTime={dayjs(ticketSession.end_time).valueOf()} onExpiry={() => setShowExpiredModal(true)} />
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
			<section className="proceed-ticket">
				<div className="container-fluid-navigation">
					<div className="buy-ticket-steps">
						<a
							onClick={(e) => {
								e.preventDefault()
								setToggled(!toggled)
							}}
							href=""
							className="hide-show-bar"
						>
							<i className={`fa fa-angle-${toggled ? 'down' : 'up'}`} /> &nbsp;&nbsp; {toggled ? 'Show' : 'Hide'}
							{toggled && <small> &nbsp; [{selectedEvent?.title?.slice(0, 20)}]</small>}
						</a>
						<Steps
							items={[
								{
									title: 'Confirm Info',
									status: buyState.steps.step1.active ? 'process' : buyState.steps.step1.done ? 'finish' : 'wait',
									icon: buyState.steps.step1.done ? <UserOutlined /> : <UserOutlined />,
								},
								{
									title: 'Pay',
									status: buyState.steps.step2.active ? 'process' : buyState.steps.step2.done ? 'finish' : 'wait',
									icon: buyState.steps.step2.done ? <CreditCardOutlined /> : <CreditCardOutlined />,
								},
								{
									title: 'Done',
									status: buyState.steps.step3.active ? 'process' : buyState.steps.step3.done ? 'finish' : 'wait',
									icon: buyState.steps.step3.done ? <SmileOutlined /> : <SmileOutlined />,
								},
							]}
						/>
						<br />
						<div className="proceed-ticket-actions">
							{renderCurrentStep()}
						</div>
					</div>
				</div>
			</section>
			<AppModal showModal={showExpiredModal} title={'Expired.'}>
				<div className="login-container" style={{ paddingTop: 30, paddingBottom: 20 }}>
					<div className="login-input-box expired-info">
						<h1>Purchase wizard was expired!</h1>
						<p>You should complete your booking wizard within <strong style={{ fontWeight: 'bold' }}>15 minutes</strong>.
							Seems like you are out of the given time.
						</p>
						<h3>No worries you can always try again.</h3>
						<br/>
						<br/>
						<Button
							onClick={() => {
								// const newState = {
								// 	...buyState,
								// 	expiry: Date.now() + 900000,
								// };
								// setBuyState(newState);
								// localStorage.setItem("buyFormState", JSON.stringify(newState));
								// setShowExpiredModal(false);
								localStorage.removeItem("buyFormState");
								if (selectedEvent) router.push(`/events/${selectedEvent?.slug}-${selectedEvent?.id}?showBuyWizard=true`);
								else router.push('/events');
							}}
							type="primary"
							size='large'
							htmlType="submit"
						>
							Try again
						</Button>
					</div>
				</div>
			</AppModal>
			<div style={{ display: 'none' }}>
				<form
					ref={esewaFormRef}
					className="amount-form"
					action={process.env.NEXT_PUBLIC_ESEWA_GATEWAY}
					method="POST"
				>
					<label htmlFor="amount">Amount:</label>
					<input type="text" name="amount" value={formData.amount} readOnly required />
					<input type="hidden" name="tax_amount" value={formData.tax_amount} required />
					<input type="hidden" name="total_amount" value={formData.total_amount} required />
					<input type="hidden" name="transaction_uuid" value={formData.transaction_uuid} required />
					<input type="hidden" name="product_code" value={formData.product_code} required />
					<input type="hidden" name="product_service_charge" value={formData.product_service_charge} required />
					<input
						type="hidden"
						name="product_delivery_charge"
						value={formData.product_delivery_charge}
						required
					/>
					<input
						type="hidden"
						name="success_url"
						value={formData.success_url}
						required
					/>
					<input
						type="hidden"
						name="failure_url"
						value={formData.failure_url}
						required
					/>
					<input
						type="hidden"
						name="signed_field_names"
						value={formData.signed_field_names}
						required
					/>
					<input
						type="hidden"
						name="signature"
						value={formData.signature}
						required
					/>
					<button type="submit">Submit</button>
				</form>
			</div>
		</Spin>
	)
}
