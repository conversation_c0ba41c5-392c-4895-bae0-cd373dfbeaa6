import AppCrumb from "@/components/common/AppCrumb.component";
import ExpandableContent from "@/components/common/ExpandableContent.component";
import { InfoCard } from "@/components/common/InfoCard";
import DetailTabs from "@/components/Layouts/Partials/app/event/DetailTabs";
import fetchRequest from "@/helpers/fetch.request";
import { EventModel } from "@/types/models/event.model";
import { VenueModel } from "@/types/models/venue.model";
import { convertNewLinesToBr, getFullImageUrl, slugger } from "@/utils/general.util";
import { WarningFilled } from "@ant-design/icons";
import dayjs from "dayjs";
import { Metadata } from "next";
import SocialShare from "@/components/common/SocialShare.component";
import { NoResult } from "@/components/common/NoResult";
import LikeButton from "@/components/event/LikeButton.component";
import SubscribeButton from "@/components/event/SubscribeButton.component";
import BookTicketWizard from "@/components/Layouts/Partials/app/event/BookTicket";
import DirectionsMap from "@/components/common/DirectionsMap.component";
import { Tag } from "antd";

async function getEvent(slug: string): Promise<EventModel | null> {
	try {
		const eventId = slug.split('-').pop();
		const eventDetail = await fetchRequest(`/api/events/${eventId}`, {
			cache: 'no-store',
		});

		if (!eventDetail) {
			throw new Error("Event not found");
		}

		return eventDetail as EventModel;
	} catch {
		return null;
	}
}

// eslint-disable-next-line react-refresh/only-export-components
export async function generateMetadata({
	params
}: {
	params: { slug: string }
}): Promise<Metadata> {
	const slug = params.slug;
	const event = await getEvent(params.slug);
	if (!event) {
		return { title: "Event Not Found" };
	}
	const categoryText = (event.categories?.map(c => c.name))?.join(', ') ?? '';

	let title = `${event.title} - ${categoryText} in Nepal`;
	title += ' | Find the best events, concerts, workshops and gatherings in Nepal';

	const description = event.meta_desc || event.about
		? `Explore ${event.title} on eticketnepal. ${event.meta_desc || event.about}`
		: `Join us for ${event.title}, an exciting ${categoryText} event in Nepal. Get your tickets now on eticketnepal.`;

	const eventAddress = event.venues && event.venues.length > 0
		&& event.venues[0]?.address
		? event.venues[0].address
		: null;

	const other: Record<string, string> = {
		"fb:app_id": process.env.NEXT_PUBLIC_FACEBOOK_CLIENT_ID as string,
	};

	if (eventAddress?.zone) other["geo.region"] = String(eventAddress.zone);
	if (eventAddress?.city) other["geo.placename"] = String(eventAddress.city);

	if (eventAddress?.lat != null && eventAddress?.lng != null) {
		other["geo.position"] = `${eventAddress.lat};${eventAddress.lng}`;
		other["ICBM"] = `${eventAddress.lat},${eventAddress.lng}`;
	}

	return {
		title,
		openGraph: {
			title: `${event.title} | eticketnepal`,
			url: `https://www.eticketnepal.com/events/${slug}`,
			type: 'article',
			images: [
				{
					url: (event.image) ? getFullImageUrl(event.image) : 'https://placehold.co/600x250/png?text=Event+Image',
					type: 'image/jpeg',
					width: 1200,
					height: 630,
					alt: event.title
				},
			]
		},
		description,
		keywords: event.meta_keywords,
		other,
		twitter: {
			card: 'summary_large_image',
			title: `${event.title} | eticketnepal`,
			description,
			images: (event.image) ? [
				getFullImageUrl(event.image),
			] : ['https://placehold.co/600x250/png?text=Event+Image'],
		},
	} as Metadata
}

export default async function EventDetail({
	params
}: {
	params: { slug: string }
}) {
	const event = await getEvent(params.slug);
	if (!event) return <NoResult message='Event Not Found' />;

	const detailTabsData = {
		artists: event.artists,
		faqs: event.faqs,
		terms: event.terms && event.terms?.length > 0 ? convertNewLinesToBr(event.terms[0].terms) : '',
		galleries: event.galleries,
		activities: event.activities,
	}

	const destination = event.venues && event.venues.length > 0
		&& event.venues[0]?.address?.lat
		&& event.venues[0]?.address?.lng
		? {
			lat: event.venues[0].address.lat,
			lng: event.venues[0].address.lng,
		}
		: null;

	return (
		<>
			<div className='detail-main-img-options'>
				<div className='row'>
					<div className='col-lg-9 main-image'>
						<div className="d-flex gap-2">
							<AppCrumb looper={slugger(event, "event")} />
							{event.is_delayed && <Tag color="orange">Delayed</Tag>}
						</div>
						<br />

						<img src={event.image ? getFullImageUrl(event.image) : 'https://placehold.co/600x250/png?text=Event+Image'} />

						<div className='event-options'>
							<div className='row'>
								<div className='col-md-9 col-sm-12 options'>
									<div className='opt-four opt'>
										<h2>
											{' '}
											{/* <span>{pluralize('DATE', selectedEvent?.dates && selectedEvent.dates.length)}</span> */}
											<span>DATE</span>
											<br />
											<div>
												{/* {selectedEvent?.dates && selectedEvent.dates.length > 0 ? (
														selectedEvent.dates.map((date, index) => (
															<span
																key={index}
																style={{
																	color: '#c71782',
																	fontWeight: '600',
																	fontSize: '15px',
																}}
															>
																{dayjs(date.date).format('DD MMM YYYY')}
																<br />
															</span>
														))
													) : (
														'N/A'
													)} */}
												{event.start_date_time ? (
													<span
														key="start_date_time"
														style={{
															color: '#c71782',
															fontWeight: '600',
															fontSize: '15px',
														}}
													>
														{dayjs(event.start_date_time).format('DD MMM YYYY h:mm A')}
														<br />
													</span>
												) : (
													<span>N/A</span>
												)}
											</div>
										</h2>
									</div>
									<div className='opt-one opt'>
										<LikeButton event={event} />
									</div>
									{/* <div className='opt-two opt'>
										<a href='#comments-section' className='like-button'>
											<i className='far fa-comment-alt' />
										</a>
										<p>
											<i>5 Comments</i>{' '} */}
									{/* {pluralize("comment", selected.comments.length)} */}
									{/* </p>
									</div> */}
									<div className='opt-four opt'>
										<SubscribeButton event={event} />
										{/* <p>
											<a href=''>
												<i className='fa-regular fa-compass' />
												&nbsp; Get directions
											</a>
										</p> */}
										{destination && <DirectionsMap destination={destination} />}
									</div>
								</div>
								<div className='col-md-3 col-sm-12 book-now'>
									<BookTicketWizard event={event} />
								</div>
							</div>
							<DetailTabs data={detailTabsData} />
						</div>
					</div>
					<div className='col-lg-3 col-md-12 evt-detail-info'>
						<div className='content-padding'>
							<h1
								style={{
									overflowWrap: 'break-word',
									hyphens: 'auto',
									WebkitHyphens: 'auto',
									msHyphens: 'auto'
								}}
							>
								{event.title}
							</h1>
							{event.about && (
								<ExpandableContent content={event.about} initialLimit={100} />
							)}

							{event.main_attraction && (
								<div className="evt-attraction">
									<h2>Main Attractions</h2>
									<ExpandableContent content={event.main_attraction} initialLimit={300} />
								</div>
							)}

							<br />
							<hr />
							<h2>Location and Venues</h2>
							<br />
							{event.venues && (
								event.venues.map((venue: VenueModel, i: number) => (
									<InfoCard
										title={venue.name}
										image={venue.image || ''}
										as={venue.slug}
										description={venue.about || ''}
										key={i}
										address={venue.address || null}
									/>
								))
							)}

							<div className='event-social-share'>
								<SocialShare url={`https://eticketnepal.com/events/${params.slug}`} title={event.title as string} />
							</div>
						</div>
					</div>
				</div>
				{/* <Comment /> */}
			</div>

			{!event?.published_at && (
				<div className="temporary_post_alert">
					<div className="container">
						<p><WarningFilled /> You are viewing a temporary post. This post is not published to live website yet.</p>
					</div>
				</div>
			)}
		</>
	)
}
