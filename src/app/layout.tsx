import { Inter } from 'next/font/google';
import '@/scss/app.scss';
import HeaderComponent from '@/components/Header/Header.component';
import FooterComponent from '@/components/Footer.component';
import { ConfigProvider } from 'antd';
import theme from './../../themeConfig';
import PageProgressBar from '@/components/PageProgressBar.component';
import { GeneralInfoModal } from '@/types/models/general-info.model';
import { ReduxProvider } from '@/components/ReduxProvider';
import ConfirmationModal from '@/components/common/ConfirmationModal';
import LoginModal from '@/components/common/LoginModal';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { Metadata } from 'next';
import { GoogleMapsProvider } from '@/context/GoogleMapsContext';

const inter = Inter({ subsets: ['latin']});

export const metadata: Metadata = {
	metadataBase: new URL('https://www.eticketnepal.com'),
	title: 'eticketnepal | Your Ultimate Destination for Events in Nepal',
	description:
    'Discover and book tickets for the best concerts, festivals, workshops, and events across Nepal. Find, explore, and connect with the vibrant event scene in Nepal.',
	keywords:
    'Nepal events, concert tickets Nepal, festival tickets, workshops Nepal, cultural events Nepal, event booking, Nepal entertainment, live shows Nepal, ticket booking Nepal, Nepal event calendar',
	robots: 'index,follow',
	authors: [{
		name: 'eticketnepal',
		url: 'https://www.eticketnepal.com'
	}],
	openGraph: {
		url: 'https://www.eticketnepal.com',
		type: 'website',
		title: 'eticketnepal | Your Ultimate Destination for Events in Nepal',
		images: [
			{
				url: 'https://eticketnepal.com/images/logo-full.png',
				alt: 'eticketnepal | Your Ultimate Destination for Events in Nepal',
				type: 'image/jpeg',
				width: 1200,
				height: 630
			},
		],
		description: 'Find and book tickets for the best events in Nepal. Discover concerts, festivals, workshops, and more happening across the country.',
	},
	twitter: {
		card: 'summary_large_image',
		title: 'eticketnepal | Your Ultimate Destination for Events in Nepal',
		description: 'Discover and book tickets for the best events in Nepal. Find concerts, festivals, workshops, and more.',
		images: [
			'https://eticketnepal.com/images/logo-full.png',
		],
	},
	other: {
		// 'google-adsense-account': 'ca-pub-YOUR_ADSENSE_ID_HERE',
		"fb:app_id": process.env.NEXT_PUBLIC_FACEBOOK_CLIENT_ID as string,
	}
};

async function getGlobalData() {
	const res = await fetch(`${process.env.NEXT_PUBLIC_APP_BASE_API}/api/general-info`, {
		// next: { revalidate: 60 * 60 * 24 },
		cache: 'no-store',
	})
	return res.json();
}

export default async function RootLayout({
	children,
}: Readonly<{
  children: React.ReactNode;
}>) {
	const globalD: GeneralInfoModal = await getGlobalData();

	return (
		<html lang='en'>
			<head>
				<script
					type="text/javascript"
					src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_API_KEY}&libraries=places`}
				/>
				{/* <meta charSet='UTF-8' />
				<meta name='viewport' content='width=device-width, initial-scale=1' /> */}
				<link
					rel='apple-touch-icon'
					sizes='180x180'
					href='/images/fav/apple-touch-icon.png'
				/>
				<link
					rel='icon'
					type='image/png'
					sizes='32x32'
					href='/images/fav/favicon-32x32.png'
				/>
				<link
					rel='icon'
					type='image/png'
					sizes='16x16'
					href='/images/fav/favicon-16x16.png'
				/>
				<link rel='manifest' href='/static/img/fav/site.webmanifest' />
				<link
					rel='mask-icon'
					href='/static/img/fav/safari-pinned-tab.svg'
					color='#5bbad5'
				/>
				{/* <meta name='msapplication-TileColor' content='#da532c' />
				<meta name='author' content='ETicketNepal' />
				<meta name='rating' content='general' />
				<meta name='application-name' content='ETicketNepal' />
				<meta property='og:title' content='ETicketNepal' />
				<meta property='og:type' content='website' />
				<meta
					name='description'
					content='Eticketnepal offers event tickets, reviews, trailers, concert tickets and events near Kathmandu. Online events ticket booking through various nepali payment gateway.'
				/>
				<meta
					name='keywords'
					content='Event Ticket Bookings, Event Tickets, Events in Nepal, Event venues in nepal, Events Online Booking, Concert, Play, Event, Comedy Show Tickets, Cricket match Tickets, Reviews'
				/>
				<meta
					property='og:image'
					content='https://scontent.fktm10-1.fna.fbcdn.net/v/t1.0-1/p480x480/42088771_1980014658723843_3612490558804590592_n.jpg?_nc_cat=109&_nc_ht=scontent.fktm10-1.fna&oh=7e8ee815ff474588baffa936e5d21577&oe=5CDC6E2E'
				/>
				<meta property='og:url' content='http://eticketnepal.com' />
				<meta property='og:site_name' content='ETicketNepal' />
				<meta property='fb:app_id' content='2279931245600656' />
				<meta name='twitter:site' content='@eticket_nepal' />
				<meta
					name='twitter:title'
					content='Events Tickets, Plays, Sports, Music Concerts, Theatre - ETicketNepal'
				/>
				<meta
					name='twitter:description'
					content='Event Tickets Online Booking. Book and Buy Events, Plays, Music Concert, Cinema, Theatre, Sports, English, Hindi Movie Tickets'
				/>
				<meta
					name='twitter:image'
					content='https://pbs.twimg.com/profile_images/1045209157652156416/VantzUnH_400x400.jpg'
				/>
				<meta name='twitter:url' content='www.eticketnepal.com' />
				<meta name='theme-color' content='#ffffff' /> */}
				{/* <link
					rel='stylesheet'
					href='https://use.fontawesome.com/releases/v6.7.2/css/all.css'
					integrity='sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr'
					crossOrigin='anonymous'
				/> */}
				<link
					rel="stylesheet"
					href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
					integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
					crossOrigin="anonymous"
					referrerPolicy="no-referrer"
				/>
			</head>
			<ReduxProvider>
				<ConfigProvider theme={theme}>
					<body className={inter.className}>
						<AntdRegistry>
							<GoogleMapsProvider>
								<PageProgressBar />
								<HeaderComponent />
								{children}
								<FooterComponent {...globalD} />
								<ConfirmationModal />
								<LoginModal />
							</GoogleMapsProvider>
						</AntdRegistry>
					</body>
				</ConfigProvider>
			</ReduxProvider>

		</html>
	);
}
