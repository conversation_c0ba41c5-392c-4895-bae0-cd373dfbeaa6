.drag_drp_cover {
  height: 350px;
  width: 100%;
  background-color: #f6f6f6;
  margin-bottom: 20px;
  border-radius: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  cursor: pointer;
  border: 2px dashed #ccc;
  transition: all 0.3s;
  overflow: hidden;
  position: relative;
  &.gallery {
    justify-content: flex-start;
    align-items: flex-start;
    ul {
      list-style: none;
      margin: 15px;
      li {
        margin-right: 15px;
        height: 100px;
        width: 100px;
        border-radius: 10px;
        display: inline-block;
        overflow: hidden;
        position: relative;
        img {
          position: relative;
          height: 100%;
          width: 100%;
          object-fit: cover;
        }
        a {
          background-color: $color-error;
          position: absolute;
          z-index: 99;
          display: inline-block;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          top: 5px;
          right: 5px;
          line-height: 1.2;
          i {
            line-height: 0;
            opacity: 1;
          }
        }
      }
    }
  }
  i {
    font-size: 40px;
    transform: rotate(20deg);
    opacity: 0.6;
    transition: 0.3s;
  }
  p {
    color: #c1c1c1;
    margin-top: 8px;
    font-size: 20px;
    transition: 0.3s;
    position: relative;
  }
  img {
    position: absolute;
    width: 100%;
    object-fit: cover;
  }
  &:hover {
    border-color: $color-primary;
    i {
      color: $color-primary;
      opacity: 1;
    }
    p {
      color: $color-primary;
    }
  }
}

.dashboard_add_form {
  .add_form_header {
    width: 100%;
    // height: 150px;
    padding-top: 20px;
    background-color: white;
    padding-bottom: 30px;
    h1 {
      font-weight: bold;
      color: #6c757d;
      + p {
        a {
          border-bottom: 1px solid #999;
        }
        font-size: 18px;
        margin-top: 8px;
        color: #c2c2c2;
        span,
        a {
          color: #6c757d;
        }
      }
    }
  }

  .ant-select-selection-selected-value {
    font-size: 16px;
    font-weight: bold;
  }
  .ant-select-selection__rendered {
    margin-left: 0;
  }

  // .ant-select-outlined:not(.ant-select-disabled):not(
  //     .ant-select-customize-input
  //   ):not(.ant-pagination-size-changer):hover
  //   .ant-select-selector {
  //   border: none !important;
  //   border-color: transparent !important;
  // }

  // .ant-select-outlined:not(.ant-select-disabled):not(
  //     .ant-select-customize-input
  //   ):not(.ant-pagination-size-changer)
  //   .ant-select-selector {
  //   box-shadow: none !important;
  // }

  .sticky-bottom-alert {
    position: fixed;
    bottom: 100px;
    left: 0;
    width: 100%;
    z-index: 99999;
    background-color: #ff4d4f;
    color: white;
    text-align: center;
    padding: 10px 20px;
    font-weight: bold;

    p {
      margin: 0;
      font-size: 16px;
    }
  }

  .add_form_footer {
    width: 100%;
    left: 0;
    padding: 30px 40px 70px 30px;
    height: 40px;
    position: fixed;
    bottom: 0;
    z-index: 99999;
    background-color: white;
    opacity: 1;
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
    p {
      margin-top: 8px;
    }
  }
  .venue-info-container {
    padding: 20px 15px;
    h3 {
      opacity: 0.9;
    }
    p {
      margin-top: 10px;
      font-size: 15px;
      color: $textColorDark;
      opacity: 0.7;
    }
  }
  .ant-select-selection {
    background-color: transparent;
  }
  .ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    border-color: transparent;
    box-shadow: none;
  }

  .add_form_container {
    min-height: 200px;
    width: 100%;
    padding-bottom: 100px;
  }
  .ant-select-selection {
    border: none;
    padding: 0;
  }
  .ant-select-selection--multiple .ant-select-selection__rendered {
    margin-left: 0 !important;
  }
  .ant-select-selection--multiple .ant-select-selection__placeholder {
    margin-left: 0 !important;
  }
  textarea.ant-input {
    height: auto !important;
    background-color: transparent !important;
    padding: 15px 0 !important;
  }
  .ant-select-selection--multiple .ant-select-selection__choice {
    color: $textColorDark !important;
    font-weight: bold;
  }
  .ant-select {
    margin-top: 0;
  }
  .input-container {
    margin-bottom: 30px;
    padding: 10px 15px;
    border: 1px solid #ccc;

    // .ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector {
    //   border: none;
    // }
    .error-message {
      color: red;
      font-size: 14px !important;
      font-weight: normal !important;
    }

    &.active {
      border-color: $color-primary;
    }
    border-radius: 10px;
    label {
      color: #555;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    p {
      margin-top: 0 !important;
      color: #c3c3c3 !important;
      font-size: 14px;
      font-weight: 400;
    }
    .ant-input, .mask-input {
      background-color: transparent;
      color: $textColorDark;
      border: none;
      padding: 0 0;
      height: 30px;
      font-weight: 600;
      font-size: 18px;
      &:focus {
        box-shadow: none;
      }
    }
    .tag-input {
      border: 1px solid #ccc;
      border-radius: 6px;
      width: 400px;
      padding: 0 8px;
    }
  }
  .ant-calendar-picker {
    //display: none;
    //opacity: 0;
  }
  .faq-input-container {
    border-bottom: 1px dashed $color-primary-light;
    margin-bottom: 20px;
  }
}

.image-upload-preview {
  display: flex;
  .image-preview {
    width: 200px;
    border: 1px dashed #ccc;
    border-radius: 5px;
    overflow: hidden;
  }
  .image-info {
    padding-left: 20px;
    h1 {
      font-weight: bold;
      font-size: 18px;
      + p {
      }
    }
  }
  .image-action {
    margin-left: auto;
    display: flex;
    align-items: center;
    a {
      font-size: 24px;
      margin-left: 20px;
      color: #c3c3c3;
      &:hover {
        color: $color-primary;
      }
    }
  }
}

//.ReactCrop {
//  width: 100%;
//}

//.ReactCrop__image {
//  width: 100%;
//  max-height: none !important;
//}
