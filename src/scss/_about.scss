// About Page Styles
.about-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 4rem 2rem;
	color: #1e293b;

	section {
		margin-bottom: 5rem;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
}

// Hero Section
.about-hero {
	text-align: center;
	padding: 6rem 0;
	background: linear-gradient(135deg, #c71782 0%, #ff94d4 100%);
	border-radius: 30px;
	margin-bottom: 6rem;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		width: 300px;
		height: 300px;
		background: linear-gradient(135deg, #c71782 0%, #ff94d4 100%);
		border-radius: 50%;
		top: -150px;
		right: -150px;
		z-index: 0;
	}

	&::after {
		content: '';
		position: absolute;
		width: 200px;
		height: 200px;
		background: linear-gradient(135deg, #c71782 0%, #ff94d4 100%);
		border-radius: 50%;
		bottom: -100px;
		left: -100px;
		z-index: 0;
	}

	.about-title {
		font-size: 3rem;
		font-weight: 700;
		margin-bottom: 1.5rem;
		background: linear-gradient(135deg, #ffff, #ffc1e6);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		position: relative;
		z-index: 1;
	}

	.about-subtitle {
		font-size: 1.25rem;
		color: #ffffff;
		max-width: 600px;
		margin: 0 auto;
		line-height: 1.6;
		position: relative;
		z-index: 1;
	}
}

// Mission Section
.about-mission {
	.mission-content {
		max-width: 800px;
		margin: 0 auto;
		text-align: center;
		padding: 3rem;
		background: white;
		border-radius: 20px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
		
		h2 {
			font-size: 2.25rem;
			margin-bottom: 1.5rem;
			color: #1e293b;
			font-weight: 600;
		}

		p {
			font-size: 1.1rem;
			line-height: 1.8;
			color: #64748b;
			margin: 0;
		}
	}
}

// Adding new section styles after the mission section

// History Section
.about-history {
	position: relative;
	padding: 4rem 0;
	background: linear-gradient(135deg, rgba(37, 99, 235, 0.03) 0%, rgba(79, 70, 229, 0.05) 100%);
	border-radius: 30px;
	overflow: hidden;

	.history-content {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
		position: relative;
		z-index: 1;

		h2 {
			font-size: 2.25rem;
			color: #1e293b;
			margin-bottom: 2rem;
			font-weight: 600;
			text-align: center;
		}

		.timeline {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
			gap: 2rem;

			.milestone {
				background: white;
				padding: 2rem;
				border-radius: 20px;
				box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
				transition: transform 0.3s ease;

				&:hover {
					transform: translateY(-5px);
				}

				.year {
					color: #c71782;
					font-size: 1.5rem;
					font-weight: 600;
					margin-bottom: 1rem;
				}

				h3 {
					font-size: 1.25rem;
					color: #1e293b;
					margin-bottom: 0.75rem;
				}

				p {
					color: #64748b;
					line-height: 1.6;
				}
			}
		}
	}
}

// Achievements Section
.about-achievements {
	padding: 4rem 0;

	.achievements-header {
		text-align: center;
		margin-bottom: 3rem;

		h2 {
			font-size: 2.25rem;
			color: #1e293b;
			margin-bottom: 1rem;
			font-weight: 600;
		}

		p {
			color: #64748b;
			font-size: 1.1rem;
			max-width: 600px;
			margin: 0 auto;
			line-height: 1.6;
		}
	}

	.achievements-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 2rem;
		text-align: center;

		.achievement-card {
			padding: 2rem;
			background: white;
			border-radius: 20px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
			transition: all 0.3s ease;

			&:hover {
				transform: translateY(-5px);
				box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
			}

			.number {
				font-size: 2.5rem;
				font-weight: 700;
				color: #c71782;
				margin-bottom: 1rem;
			}

			h3 {
				font-size: 1.25rem;
				color: #1e293b;
				margin-bottom: 0.75rem;
			}

			p {
				color: #64748b;
				line-height: 1.6;
				font-size: 0.95rem;
			}
		}
	}
}

// Why Choose Us Section
.why-choose-us {
	padding: 4rem 0;
	background: linear-gradient(135deg, rgba(79, 70, 229, 0.03) 0%, rgba(37, 99, 235, 0.05) 100%);
	border-radius: 30px;

	.choose-us-header {
		text-align: center;
		margin-bottom: 3rem;

		h2 {
			font-size: 2.25rem;
			color: #1e293b;
			margin-bottom: 1rem;
			font-weight: 600;
		}

		p {
			color: #64748b;
			font-size: 1.1rem;
			max-width: 600px;
			margin: 0 auto;
			line-height: 1.6;
		}
	}

	.features-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 2rem;
		padding: 0 2rem;

		.feature-card {
			background: white;
			padding: 2rem;
			border-radius: 20px;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
			transition: all 0.3s ease;
			display: flex;
			flex-direction: column;
			gap: 1rem;

			&:hover {
				transform: translateY(-5px);
				box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
			}

			.icon {
				width: 50px;
				height: 50px;
				background: rgba(37, 99, 235, 0.1);
				border-radius: 12px;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #c71782;
				font-size: 1.5rem;
			}

			h3 {
				font-size: 1.25rem;
				color: #1e293b;
				font-weight: 600;
			}

			p {
				color: #64748b;
				line-height: 1.6;
				font-size: 1rem;
			}
		}
	}
}

// Values Section
.values-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 2rem;
	margin-top: 4rem;

	.value-card {
		background: white;
		padding: 2.5rem 2rem;
		border-radius: 20px;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;
		border: 1px solid rgba(0, 0, 0, 0.05);

		&:hover {
			transform: translateY(-5px);
			box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
			border-color: rgba(37, 99, 235, 0.2);
		}

		h3 {
			font-size: 1.5rem;
			color: #1e293b;
			margin-bottom: 1rem;
			font-weight: 600;
			display: flex;
			align-items: center;
			gap: 0.75rem;

			&::before {
				content: '';
				display: block;
				width: 8px;
				height: 8px;
				background: #c71782;
				border-radius: 50%;
			}
		}

		p {
			color: #64748b;
			line-height: 1.6;
			margin: 0;
			font-size: 1rem;
		}
	}
}

// Team Section
.team-section-header {
	text-align: center;
	margin-bottom: 3rem;

	h2 {
		font-size: 2.25rem;
		color: #1e293b;
		margin-bottom: 1rem;
		font-weight: 600;
	}

	p {
		color: #64748b;
		font-size: 1.1rem;
		max-width: 600px;
		margin: 0 auto;
		line-height: 1.6;
	}
}

.team-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
	gap: 3rem;
	margin-top: 3rem;

	.team-member {
		text-align: center;

		.member-image-container {
			width: 180px;
			height: 180px;
			margin: 0 auto 1.5rem;
			border-radius: 20px;
			overflow: hidden;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
			position: relative;
			background: linear-gradient(135deg, #f1f5f9, #e2e8f0);

			&::after {
				content: '';
				position: absolute;
				inset: 0;
				background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
			}

			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: transform 0.3s ease;

				&:hover {
					transform: scale(1.05);
				}
			}
		}

		h3 {
			font-size: 1.25rem;
			color: #1e293b;
			margin-bottom: 0.5rem;
			font-weight: 600;
		}

		p {
			color: #64748b;
			font-size: 0.95rem;
			margin: 0;
		}

		.social-links {
			display: flex;
			justify-content: center;
			gap: 1rem;
			margin-top: 1rem;

			a {
				color: #64748b;
				transition: color 0.3s ease;

				&:hover {
					color: #2563eb;
				}
			}
		}
	}
}

// Contact Section
.about-contact {
	text-align: center;
	background: linear-gradient(135deg, #2563eb, #4f46e5);
	padding: 5rem 2rem;
	border-radius: 30px;
	color: white;
	position: relative;
	overflow: hidden;

	&::before,
	&::after {
		content: '';
		position: absolute;
		width: 300px;
		height: 300px;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
	}

	&::before {
		top: -150px;
		right: -150px;
	}

	&::after {
		bottom: -150px;
		left: -150px;
	}

	.contact-content {
		position: relative;
		z-index: 1;
		max-width: 600px;
		margin: 0 auto;

		h2 {
			font-size: 2.5rem;
			margin-bottom: 1rem;
			font-weight: 600;
		}

		p {
			font-size: 1.1rem;
			margin-bottom: 2rem;
			opacity: 0.9;
			line-height: 1.6;
		}

		.contact-button {
			background: white;
			color: #2563eb;
			padding: 1rem 2.5rem;
			border-radius: 30px;
			font-size: 1.1rem;
			font-weight: 600;
			border: none;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);

			&:hover {
				transform: translateY(-2px);
				box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
				background: #f8fafc;
			}
		}
	}
}

// Responsive Design
@media (max-width: 1024px) {
	.about-container {
		padding: 3rem 1.5rem;
	}

	.about-hero {
		padding: 4rem 1.5rem;

		.about-title {
			font-size: 2.5rem;
		}
	}

	.values-grid {
		grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
	}

	.about-history,
	.about-achievements,
	.why-choose-us {
		padding: 3rem 1.5rem;
	}

	.history-content .timeline,
	.achievements-grid,
	.features-grid {
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	}
}

@media (max-width: 768px) {
	.about-container {
		padding: 2rem 1rem;
	}

	.about-hero {
		padding: 3rem 1rem;

		.about-title {
			font-size: 2rem;
		}

		.about-subtitle {
			font-size: 1.1rem;
		}
	}

	.mission-content {
		padding: 2rem 1.5rem;

		h2 {
			font-size: 1.75rem;
		}
	}

	.team-grid {
		grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
		gap: 2rem;
	}

	.about-contact {
		padding: 3rem 1.5rem;

		h2 {
			font-size: 2rem;
		}
	}

	.about-history,
	.about-achievements,
	.why-choose-us {
		padding: 2rem 1rem;
	}

	.history-content h2,
	.achievements-header h2,
	.choose-us-header h2 {
		font-size: 1.75rem;
	}
}
