@import 'variables';
@import 'mixins';

//=================================================================
//=================================================================
/*(1) Defaults*/
//=================================================================
//=================================================================
h3 {
  font-size: 24px;
  font-weight: bold;
  color: $textColorDark;
}

strong {
  font-weight: bold;
}

/*

(1) App Navigation (div.app-navigation)
	(1.1) Drawer Icon (.drawer-nav-switcher)

*/

//=================================================================
//=================================================================
/*(1) Start of App Navigation*/
//=================================================================
//=================================================================
body {
  font-family: $Lato;
  height: 100%;
}

h1 {
  font-size: 35px;
}

.no-padding-left {
  padding-left: 0 !important;
}

.title-underline {
  height: 3px;
  margin: 20px 0;
  border-radius: 6px;
  width: 200px;
  display: block;
  background-color: white;
}

a {
  color: $color-primary;
}

h1.app-title-primary {
  font-size: 35px;
  font-weight: 300;
  color: #555;
  text-align: center;
  + p {
    text-align: center;
    color: #777;
    font-style: italic;
    padding: 0 40px;
    font-weight: 500;
    width: 50%;
    margin: auto;
    font-size: 15px;
    line-height: 23px;
  }
  @media all and (max-width: 1000px) {
    + p {
      width: 80%;
    }
  }
  @media all and (max-width: 650px) {
    + p {
      width: 100%;
      padding: 0 10px;
    }
  }
}

span.highlighted-heading {
  color: $color-primary;
  font-weight: 400;
  font-style: italic;
}

.section-container-box {
  padding: 20px 0;
}

div.app-navigation {
  div.search-bar {
    color: $color-tertiary;
    min-height: 70px;
    background: $color-primary;
    display: flex;
    align-items: center;
    .small-device-option {
      display: flex;
      width: 100%;
      padding: 10px 0 5px 0;
      flex-direction: row;
      justify-content: space-between;
      a {
        display: inline;
      }
    }
    @media all and (max-width: 991px) {
      .pick-your-city {
        display: none;
      }
      .hide-on-small {
        display: none;
      }
      .small-device-option {
        display: flex !important;
      }
      .search-form-column {
        margin-top: 10px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
      }
    }
  }
  div.navigator {
    padding: 10px 0;
    background: white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    .nav-link {
      display: block;
      padding: 0.5rem 1.5rem 0.5rem 0;
    }
    a {
      color: $color-light;
      &.active {
        color: $color-primary;
      }
      &:hover {
        color: $color-primary-active;
      }
    }
    nav.left-nav-items {
      li.nav-item {
        position: relative;
        a {
          font-size: 18px;
          font-family: $Lato;
        }
        .badge {
          position: absolute;
          top: 0;
          right: 8px;
          padding-right: 0.4em;
          padding-left: 0.4em;
          border-radius: 5rem;
          font-size: 45%;
        }
      }
    }
    nav.right-nav-items {
      button.login-signup {
        padding: 0 8px 2px 8px;
      }
      a {
        font-size: 14px;
        &.link-item-right {
          padding-top: 12px;
        }
      }
    }
  }

  @media all and (max-width: 800px) {
    div.navigator {
      nav.left-nav-items {
        li.nav-item {
          a {
            font-size: 16px;
            font-family: $Lato;
          }
        }
      }
      nav.right-nav-items {
        button.login-signup {
          padding: 0 5px 1px 5px;
          font-size: 12px;
        }
        a {
          font-size: 12px;
          &.link-item-right {
            padding-top: 12px;
          }
        }
      }
    }
  }
  @media all and (max-width: 574px) {
    .navigator {
      .logo-image {
        margin: auto;
      }
    }
    .left-nav-items,
    .right-nav-items {
      ul {
        display: flex;
        justify-content: space-between !important;
      }
    }
    .right-nav-items {
      border-top: 1px solid #eee;
      margin-top: 15px;
    }
  }

  .search-box-input {
    height: 50px;
  }
  .pick-your-city {
    a {
      line-height: 50px;
      font-size: 16px;
      &:hover {
        text-decoration: none;
      }
    }
  }

  //=================================================================
  //=================================================================
  /*(1.1) Start of Drawer Icon (.drawer-nav-switcher)*/
  //=================================================================
  //=================================================================

  .drawer-nav-switcher {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  div.drawer-nav-icon {
    align-self: flex-end;
    width: 30px;
    height: 30px;
    position: relative;
    z-index: 999;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: 0.5s ease-in-out;
    -moz-transition: 0.5s ease-in-out;
    -o-transition: 0.5s ease-in-out;
    transition: 0.5s ease-in-out;
    cursor: pointer;
    span {
      display: block;
      position: absolute;
      height: 4px;
      width: 100%;
      background: white;
      border-radius: 9px;
      left: 0;
      -webkit-transform: rotate(0deg);
      -moz-transform: rotate(0deg);
      -o-transform: rotate(0deg);
      transform: rotate(0deg);
      -webkit-transition: 0.25s ease-in-out;
      -moz-transition: 0.25s ease-in-out;
      -o-transition: 0.25s ease-in-out;
      transition: 0.25s ease-in-out;
    }
  }
}

.drawer-nav-icon span:nth-child(1) {
  top: 0;
}

.drawer-nav-icon span:nth-child(2) {
  top: 10px;
}

.drawer-nav-icon span:nth-child(3) {
  top: 20px;
}

.drawer-nav-icon.open span:nth-child(1) {
  top: 10px;
  -webkit-transform: rotate(135deg);
  -moz-transform: rotate(135deg);
  -o-transform: rotate(135deg);
  transform: rotate(135deg);
}

.drawer-nav-icon.open span:nth-child(2) {
  opacity: 0;
  left: -60px;
}

.drawer-nav-icon.open span:nth-child(3) {
  top: 10px;
  -webkit-transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
  transform: rotate(-135deg);
}

.app-side-drawer {
  .drawer-banner {
    background: url('/images/back-vector.jpeg');

    background-size: cover;
    height: 30%;
    position: relative;
    .container-overlay {
      width: inherit;
      height: 100%;
      background: rgba(168, 13, 93, 0.7);
      .drawer-quick-menu {
        height: 40px;
        background: rgba(255, 255, 255, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        display: flex;
        justify-content: flex-end;
        align-items: flex-end;
        .quick-menu-item {
          padding: 0 25px 7px 25px;
          font-size: 20px;
          color: white;
          opacity: 1;
          &.active {
            color: #ffcc00;
          }
          &:hover {
            opacity: 1;
          }
        }
      }
      .drawer-user-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 70%;
        .user-profile-image {
          // padding: 10px 0 0 0;
          // color: white;
          // font-size: 60px;
          width: 80px;
          border-radius: 50%;
          margin-bottom: 8px;
        }
        .user-icon {
          padding: 10px 0 0 0;
          color: white;
          font-size: 60px;
          margin-bottom: 6px;
        }
        p {
          color: white;
          strong {
            color: #ffcc00;
            font-weight: bold;
          }
          font-size: 16px;
        }
        span {
          padding-top: 5px;
          a {
            font-size: 15px;
            display: inline;
            color: #e3e3e3;
            padding: 10px 20px 0 20px;
          }
          &:hover {
            color: white;
          }
        }
      }
    }
  }
}

/* The side drawer navigation menu */
.sidenav {
  height: 100%;
  width: 300px;
  position: fixed;
  z-index: 1050;
  top: 0;
  right: -325px;
  display: flex;
  flex-direction: column;
  overflow: visible;
  transition: 0.5s;
  background-color: #fff;
  &:after {
    content: '';
    background: url('/images/concert-drawer-back.jpg') no-repeat;
    background-size: 300px;
    opacity: 0.2;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    position: absolute;
    z-index: -1;
    background-position: bottom;
  }
  .side-nav-menu-items {
    padding: 0 0 0 20px;
    a {
      padding: 20px 0;
      font-size: 16px;
      border-bottom: 1px solid #eee;
      text-decoration: none;
      display: block;
      color: #818181;
      &:hover {
        color: $color-primary;
      }
      &:last-child {
        border-bottom: 0;
      }
      &.active {
        color: $color-primary;
      }
    }
  }
}

.app-main-slider {
  height: auto;
  position: relative;
  background: white;
  padding: 0 0 6px 0;
  box-shadow: 0 0.1em 1px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .slider-image {
    padding: 10px;
    height: 350px;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    
    img {
      width: 100%;
      height: 100%;
      border-radius: 6px;
      object-fit: cover;
    }
  }

  // Loading skeleton responsive styles
  .m-4 {
    gap: 10px;
    padding: 10px;
    flex-wrap: wrap;
    justify-content: center !important;

    .ant-skeleton-image {
      width: calc(25% - 10px) !important;
      min-width: 280px;
    }
  }

  // For non-carousel display (3 or fewer items)
  .d-flex.justify-content-center {
    flex-wrap: wrap;
    gap: 15px;
    padding: 10px;
    
    .slider-image {
      flex: 1;
      min-width: 280px;
      max-width: calc(33.333% - 10px);
      height: 300px;
    }
  }

  // Tablet styles
  @media all and (max-width: 992px) {
    .slider-image {
      height: 300px;
      max-width: 600px;
    }

    .m-4 .ant-skeleton-image {
      width: calc(33.333% - 10px) !important;
    }

    .d-flex.justify-content-center .slider-image {
      max-width: calc(50% - 10px);
    }
  }

  // Mobile styles
  @media all and (max-width: 768px) {
    .slider-image {
      height: 250px;
      max-width: 100%;
      padding: 8px;
    }

    .m-4 {
      gap: 8px;
      
      .ant-skeleton-image {
        width: calc(50% - 8px) !important;
      }
    }

    .d-flex.justify-content-center {
      gap: 8px;
      
      .slider-image {
        max-width: 100%;
        height: 250px;
      }
    }
  }

  // Small mobile styles
  @media all and (max-width: 480px) {
    .slider-image {
      height: 200px;
      padding: 6px;
    }

    .m-4 .ant-skeleton-image {
      width: 100% !important;
      height: 200px !important;
    }
  }
}
// .app-main-slider {
//   height: auto;
//   position: relative;
//   background: white;
//   padding: 0 0 6px 0;
//   box-shadow: 0 0.1em 1px rgba(0, 0, 0, 0.1);
//   .slider-image {
//     padding: 10px;
//     min-width: 400px; 
//     min-height: 250px;
//     max-height: 350px;
//     max-width: 500px;
//     img {
//       width: 100%;
//       height: 100%;
//       border-radius: 6px;
//       object-fit: cover; 
//     }
//   }
//   .slick-arrow {
//     position: absolute;
//     z-index: 999;
//     color: #e0e0e0;
//     font-size: 40px;
//     top: 48%;
//     cursor: pointer;
//     &.fa-chevron-left {
//       left: 50px;
//     }
//     &.fa-chevron-right {
//       right: 50px;
//     }
//   }
//   @media all and (max-width: 1230px) {
//     .slick-slide {
//       img {
//         width: 800px;
//       }
//     }
//   }
//   @media all and (max-width: 650px) {
//     .slick-slide {
//       img {
//         width: 500px;
//       }
//     }
//     .slick-arrow {
//       font-size: 30px;
//       top: 45%;
//     }
//   }
//   @media all and (max-width: 500px) {
//     .slick-slide {
//       img {
//         width: 380px;
//       }
//     }
//   }
//   @media all and (max-width: 400px) {
//     .slick-slide {
//       img {
//         width: 300px;
//       }
//     }
//   }
// }

.event-lists-grid {
  margin: 40px 0;

  .events-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    padding: 0 12px;
  }

  .event-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      
      .event-image .event-overlay {
        opacity: 1;
      }
    }

    .event-image {
      position: relative;
      height: 220px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .event-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;

        .view-more-btn {
          padding: 10px 20px;
          background: $color-primary;
          color: white;
          border-radius: 25px;
          font-size: 14px;
          font-weight: 500;
          transition: transform 0.2s ease;

          &:hover {
            transform: scale(1.05);
            text-decoration: none;
          }
        }
      }

      .event-price {
        position: absolute;
        top: 15px;
        right: 15px;
        background: $color-primary;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 500;
      }
    }

    .event-content {
      padding: 20px;
      flex: 1;
      display: flex;
      flex-direction: column;

      .event-title {
        margin: 0 0 15px;
        
        a {
          color: #2c3e50;
          font-size: 18px;
          font-weight: 600;
          line-height: 1.4;
          transition: color 0.2s ease;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          
          &:hover {
            color: $color-primary;
            text-decoration: none;
          }
        }
      }

      .event-details {
        margin-bottom: 20px;
        flex: 1;

        p {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          color: #666;
          font-size: 14px;

          i {
            color: $color-primary;
            width: 20px;
            margin-right: 8px;
          }
        }
      }
    }
  }

  @media (max-width: 992px) {
    .events-grid-container {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
    }

    .event-card {
      .event-image {
        height: 200px;
      }

      .event-content {
        padding: 15px;

        .event-title a {
          font-size: 16px;
        }
      }
    }
  }

  @media (max-width: 576px) {
    .events-grid-container {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .event-card {
      .event-image {
        height: 180px;
      }

      .event-content {
        .event-title a {
          font-size: 15px;
        }

        .event-details p {
          font-size: 13px;
        }
      }
    }
  }
}

.featured-events-list {
  min-height: 300px;
  background: #f2f2f2;
  padding: 40px 20px;

  .venues-header {
    text-align: center;
    margin-bottom: 40px;

    .app-title-primary {
      font-size: 35px;
      font-weight: 300;
      color: #555;
      margin-bottom: 15px;

      .highlighted-heading {
        color: $color-primary;
        font-weight: 400;
        font-style: italic;
      }
    }

    .venues-description {
      color: #777;
      font-style: italic;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
      font-size: 15px;
    }
  }

  .search-filter-event {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .search-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20px;
    }

    .search-form-container {
      flex: 1;
      max-width: 500px;

      .search-input-group {
        .input-group {
          border: 1px solid #e0e0e0;
          border-radius: 6px;
          overflow: hidden;

          .input-group-prepend {
            .input-group-text {
              border: none;
              background: white;
              padding: 12px 15px;
              color: #999;
            }
          }

          input.search-event {
            border: none;
            padding: 12px 15px;
            height: auto;
            font-size: 15px;

            &:focus {
              box-shadow: none;
              outline: none;
            }
          }
        }
      }
    }

    .nearby-venues {
      a {
        display: flex;
        align-items: center;
        gap: 8px;
        color: $color-primary;
        font-size: 15px;
        padding: 8px 16px;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba($color-primary, 0.1);
          text-decoration: none;
        }

        i {
          font-size: 16px;
        }
      }
    }
  }

  // Tablet styles
  @media (max-width: 992px) {
    padding: 30px 15px;

    .venues-header {
      margin-bottom: 30px;

      .app-title-primary {
        font-size: 30px;
      }

      .venues-description {
        font-size: 14px;
        padding: 0 20px;
      }
    }

    .search-filter-event {
      padding: 15px;
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    padding: 20px 10px;

    .venues-header {
      margin-bottom: 25px;

      .app-title-primary {
        font-size: 26px;
      }

      .venues-description {
        font-size: 13px;
        padding: 0 15px;
      }
    }

    .search-filter-event {
      padding: 12px;

      .search-row {
        flex-direction: column;
        gap: 15px;
      }

      .search-form-container {
        max-width: 100%;
      }

      .nearby-venues {
        width: 100%;
        
        a {
          justify-content: center;
          padding: 10px;
          background: rgba($color-primary, 0.1);
        }
      }
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    padding: 15px 8px;

    .venues-header {
      .app-title-primary {
        font-size: 22px;
      }

      .venues-description {
        font-size: 12px;
        padding: 0 10px;
      }
    }

    .search-filter-event {
      padding: 10px;
    }
  }
}

.view-more-events {
  display: flex;
  align-items: center;
  justify-content: center;
  // a {
  //   background-color: rgba(0, 0, 0, 0.1);
  //   color: white;
  //   &:hover {
  //     box-shadow: 0 2px 0 rgba(0, 0, 0, 0.2);
  //     color: #666;
  //   }
  //   &:active {
  //     background-color: rgba(0, 0, 0, 0.1) !important;
  //     color: #666;
  //   }
  // }
}

.filters-selected {
  padding-top: 15px;
  padding-bottom: -10px;
  display: flex;
  //align-items: flex-start;
  justify-content: flex-end;
  .ant-tag {
    background-color: white;
    color: #888;
  }
}

.small-circle {
  display: inline-block;
  height: 22px;
  width: 22px;
  position: relative;
  border-radius: 15px;
  text-align: center;
  border: 1px solid $color-primary;
  i {
    color: $color-primary;
    text-align: center;
    line-height: 14px;
    font-size: 13px;
  }
  span.like-counter {
    background-color: white;
    position: absolute;
    right: -14px;
    color: $color-primary !important;
    font-weight: 400 !important;
    bottom: -4px;
  }
}

.x {
  position: relative;
}

.middle {
  transition: 0.5s ease;
  opacity: 0;
  position: absolute;
  z-index: 1;
  height: 100%;
  width: 100%;
  top: 50%;
  left: 50%;
  background-color: rgba(0, 0, 0, 0.7);
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  text-align: center;
}

.image {
  opacity: 1;
  display: block;
  width: 100%;
  height: auto;
  transition: 0.5s ease;
  backface-visibility: hidden;
}

.x:hover .image {
  opacity: 0.3;
}

.x:hover .middle {
  opacity: 1;
}

.text {
  color: white;
  position: absolute;
  top: 35%;
  left: 25%;
  a {
    border-radius: 0;
    border: 1px dashed white;
    &:hover {
      border-color: white;
    }
  }
  font-size: 16px;
  padding: 16px 32px;
}

.exclusive-event-counter {
  min-height: 350px;
  margin-bottom: 100px;
  .cover-image-counter {
    height: 100%;
    width: 40%;
    float: left;
    .overlay-cover-counter {
      position: relative;
      color: white;
      display: flex;
      flex-direction: column;
      @include primary-color-rgba(0.5);
      height: 100%;
      width: 100%;
      .text-content {
        padding: 100px 60px;
        color: white;
        h1 {
          color: white;
        }
        h3 {
          font-size: 20px;
          color: white;
        }
      }
      .buy-ticket-button {
        position: absolute;
        bottom: 20px;
        right: 20px;
        text-align: center;
        display: block;
        height: 60px;
        width: 60px;
        border-radius: 60px;
        background-color: white;
        font-size: 30px;
        transition: 0.3s;
        i {
          padding: 12px 0 0 15px;
          transform: rotate(45deg);
        }
        &:hover {
          color: $color-primary-active;
        }
      }
    }
  }
  .updating-counter {
    width: 60%;
    float: right;
    min-height: 100%;
    margin-bottom: 100px;
    .ticker-time {
      height: 350px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      h1 {
        color: $color-primary;
        font-size: 70px;
        font-weight: 200;
      }
      h3 {
        font-size: 22px;
        color: #777;
      }
    }
    .ticker-time-days {
      background: #fcfcfc;
    }
    .ticker-time-hour {
      background-color: #f7f7f7;
    }
    .ticker-time-minutes {
      background-color: #fcfcfc;
    }
    .ticker-time-seconds {
      background-color: #f7f7f7;
    }
  }

  @media all and (max-width: 1150px) and (min-width: 800px) {
    min-height: 250px;
    .cover-image-counter {
      width: 45%;
      .overlay-cover-counter {
        .text-content {
          padding: 50px 40px;
          h1 {
            font-size: 30px;
          }
          h3 {
            font-size: 16px;
          }
        }
      }
    }
    .updating-counter {
      width: 55%;
      .ticker-time {
        height: 250px;
        h1 {
          font-size: 40px;
        }
        h3 {
          font-size: 16px;
        }
      }
    }
  }
  @media all and (max-width: 800px) {
    min-height: 300px;
    .cover-image-counter {
      width: 100%;
      .overlay-cover-counter {
        .text-content {
          padding: 80px 40px;
        }
      }
    }
    .updating-counter {
      width: 100%;
      .ticker-time {
        height: 200px;
        h1 {
          font-size: 40px;
        }
        h3 {
          font-size: 16px;
        }
      }
    }
  }

  @media all and (max-width: 576px) {
    .cover-image-counter {
      .overlay-cover-counter {
        .buy-ticket-button {
          height: 40px;
          width: 40px;
          font-size: 20px;
          i {
            padding: 8px 0 0 10px;
          }
        }
        .text-content {
          padding: 50px 40px;
          h1 {
            font-size: 25px;
          }
          h3 {
            font-size: 14px;
          }
        }
      }
    }
    .updating-counter {
      width: 100%;
      .ticker-time {
        height: 160px;
        h1 {
          font-size: 45px;
        }
        h3 {
          font-size: 18px;
        }
      }
    }
  }
}

.filter-event-section {
  min-height: 200px;
  div.filter-content {
    border-left: 2px solid $color-primary;
    background-color: #f7f7f7;
    margin-bottom: 20px;
    width: 90%;
    float: right;
    position: relative;
    ul.tab-switcher {
      position: relative;
      top: 20px;
      li {
        display: inline-block;
        a {
          border: 1px solid #dbdbdb;
          background-color: white;
          position: absolute;
          transform: rotate(90deg);
          top: 0;
          left: -127px;
          display: flex;
          color: #999;
          min-height: 70px;
          width: 170px;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          font-size: 22px;
          transition: 0.4s;
          &.today {
            top: 50px;
            border-bottom-left-radius: 8px;
          }
          &.tomorrow {
            top: 225px;
          }
          &.this_week {
            top: 400px;
          }
          &.this_weekend {
            top: 575px;
            border-bottom-right-radius: 8px;
          }
          &:hover {
            text-decoration: none;
            border-color: $color-primary;
            background-color: $color-primary;
            color: white;
          }
          .this-date {
            font-size: 12px;
            display: block;
          }
          &.active {
            border-color: $color-primary;
            background-color: $color-primary;
            color: white;
          }
        }
      }
    }
  }
  div.filter-by-category {
    min-height: 700px;
    margin: 20px auto 0 auto;
    ul {
      width: 80%;
      margin: auto;
      flex-direction: row;
      justify-content: center;
      display: flex;
      flex-wrap: wrap;
      li {
        a {
          display: inline-block;
          margin: 2px 2px;
          min-height: 47px;
          width: auto;
          padding: 0 15px;
          text-align: center;
          line-height: 47px;
          border-radius: 3px;
          background-color: white;
          border: 1px solid transparent;
          transition: 0.5s;
          &:hover {
            text-decoration: none;
            color: $color-primary;
            border-color: $color-primary;
          }
          &.active {
            border-color: $color-primary;
          }
        }
      }
    }

    .filtered-reason {
      padding-bottom: 30px;
      h3 {
        color: #666;
        font-size: 20px;
        font-style: italic;
        text-align: center;
      }
      h1 {
        font-size: 35px;
        text-align: center;
        color: #444;
      }
    }
    .filtered-item {
      margin-bottom: 50px;
    }
    .filtered-event-lists {
      margin: 60px auto 0 auto;
      padding-left: 10%;
      padding-bottom: 85px;
      .event-image {
        border-radius: 3px;
        overflow: hidden;
      }
      .event-details {
        margin-left: -15px;
        background-color: white;
        min-height: 100px;
        padding: 20px 25px;
        border-radius: 3px;
        p.time-location {
          color: $color-primary;
          font-size: 15px;
        }
        h2 {
          color: $color-primary;
          font-size: 21px;
        }
        p {
          font-size: 14px;
          color: #777;
          line-height: 20px;
        }
        a.buy-ticket-button {
          float: right;
          display: inline-block;
          height: 40px;
          width: 40px;
          border: 1px solid $color-primary;
          text-align: center;
          border-radius: 50px;
          line-height: 40px;
          transition: 0.3s;
          i {
            transform: rotate(45deg);
          }
          &:hover {
            background-color: $color-primary;
            color: white;
          }
        }
        div.event-about {
          color: gray;
        }
        h3.event-cost {
          line-height: 60px;
          font-weight: bold;
          color: $color-primary;
        }
        .social-share {
          a {
            font-size: 12px;
            height: 25px;
            width: 25px;
            border-radius: 30px;
            display: inline-block;
            color: white;
            text-align: center;
            line-height: 25px;
            margin-right: 7px;
            &.facebook {
              background-color: #3b5999;
            }
            &.twitter {
              // background-color: #55acee;
              background-color: black;
            }
            &.google {
              background-color: #dd4b39;
            }
          }
        }
      }
    }
  }

  @media all and (max-width: 1050px) {
    div.filter-by-category {
      margin: 20px auto 0 auto;
      ul {
        li {
          a {
            margin: 0 2px;
            min-height: 35px;
            font-size: 14px;
            width: 100px;
            line-height: 35px;
          }
        }
      }
    }
  }

  @media all and (max-width: 1150px) {
    .filter-evt-info-img {
      flex: 0 0 35%;
      max-width: 35%;
    }
  }

  @media all and (max-width: 800px) {
    .filter-evt-info-img {
      flex: 0 0 80%;
      max-width: 80%;
      img {
        width: 100%;
      }
      .event-image {
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
      }
    }
    .filter-evt-info {
      margin-top: 10px;
      flex: 0 0 100%;
      max-width: 100%;
    }
    .event-details {
      margin-left: 0 !important;
      border-top-left-radius: 0 !important;
      border-top-right-radius: 0 !important;
    }
  }
  @media all and (max-width: 720px) {
    div.filter-content {
      ul.tab-switcher {
        li {
          a {
            top: 0;
            left: -90px;
            min-height: 45px;
            width: 125px;
            font-size: 17px;
            &.today {
              top: 40px;
            }
            &.tomorrow {
              top: 170px;
            }
            &.this_week {
              top: 300px;
            }
            &.coming_soon {
              top: 430px;
            }
            &:hover {
              text-decoration: none;
              border-color: $color-primary;
              background-color: $color-primary;
              color: white;
            }
            .this-date {
              font-size: 10px;
              display: block;
            }
          }
        }
      }
    }

    div.filter-by-category {
      margin: 10px auto 0 auto;
      .filtered-reason {
        h1 {
          font-size: 30px;
        }
      }
      ul {
        width: 90%;
        flex-wrap: wrap;
        li {
          a {
            display: inline-block;
            margin: 3px;
          }
        }
      }
      .filtered-item {
        margin-bottom: 50px;
      }
      .filtered-event-lists {
        margin: 60px auto 0 auto;
        padding-left: 3%;
      }
    }
  }

  @media all and (max-width: 460px) {
    div.filter-content {
      width: 85%;
    }
    .filter-evt-info-img {
      flex: 0 0 100%;
      max-width: 100%;
    }
    div.filter-by-category {
      .filtered-reason {
        h1 {
          font-size: 25px;
        }
        h3 {
          font-size: 18px;
        }
      }
      .filtered-item {
        margin-bottom: 40px;
      }
      .filtered-event-lists {
        margin: 30px auto 0 auto;
        padding-left: 0;
        .event-details {
          padding: 15px 15px;
          h2 {
            font-size: 18px;
          }
          h3.event-cost {
            line-height: 40px;
          }
        }
      }
    }
  }
}

.we-accept-section {
  border-top: 1px solid #e3e3e3;
  min-height: 100px;
  padding: 40px 0;
  background-color: #f7f7f7;
  .we-accept-rotation {
    position: relative;
    li img {
      opacity: 0.5;
      cursor: pointer;
      transition: 0.3s;

      margin: auto;
      &:hover {
        opacity: 1;
      }
    }
  }
}

div.blog-grid-section {
  min-height: 200px;
  padding: 0 0 80px 0;
  h2 {
    font-weight: 500;
    padding-bottom: 40px;
    font-size: 30px;
    text-align: center;
  }
  .card {
    border: none;
    h3 {
      font-size: 20px;
      a {
        color: black;
        &:hover {
          text-decoration: none;
        }
      }
      padding: 15px 0 7px 0;
      font-weight: bold;
    }
    p {
      color: #777;
      font-size: 14px;
      line-height: 22px;
      &.blog-info {
        span {
          font-weight: bold;
          font-size: 13px !important;
          color: $color-primary;
          border-right: 1px solid $color-primary;
          padding: 0 10px 0 0;
          &:last-child {
            padding-left: 5px;
            border-right: none;
          }
        }
      }
    }
  }
}

footer {
  min-height: 200px;
  background-color: #09101c;
  border-top: 10px solid $color-primary;
  .subscribe-to-news {
    min-height: 50px;
    padding: 15px;
    border-bottom: 1px solid $color-light;
    .input-container {
      padding-right: 0;
    }
    .social-us ul {
      float: right;
      li {
        display: inline-block;
        // padding-top: 15px;
        a {
          color: white !important;
          padding: 15px;
        }
      }
    }
    h2 {
      color: white;
      font-weight: 600;
      font-size: 23px;
      line-height: 50px;
    }
    input.form-control {
      height: 50px;
      width: 350px;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    .subscribe-button {
      padding: 7px 8px 0 0;
      background-color: white;
      height: 50px;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
      button {
        margin-bottom: 0 !important;
      }
    }
    i.fa-envelope-open {
      color: $color-primary;
      font-size: 22px;
    }
  }
  .footer-body {
    border-bottom: 1px solid $color-light;
    min-height: 220px;
    margin-top: 50px;
    padding-bottom: 30px;
    padding-left: 10px;

    h2 {
      color: white;
      font-weight: 600;
      font-size: 20px;
      padding-bottom: 15px;
      margin-bottom: 15px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    p {
      color: $color-light;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 12px;
    }

    .contact-footer {
      margin-bottom: 30px;
      
      p {
        padding-right: 10%;
      }
    }

    .logo-image {
      margin-top: 15px;
      height: 30px;
      opacity: 0.9;
      transition: opacity 0.3s ease;

      &:hover {
        opacity: 1;
      }
    }

    a {
      color: $color-light;
      transition: color 0.3s ease;

      &:hover {
        color: white;
        text-decoration: none;
      }
    }

    // Tablet styles
    @media all and (max-width: 992px) {
      margin-top: 40px;
      
      h2 {
        font-size: 18px;
        padding-bottom: 12px;
        margin-bottom: 12px;
      }

      .contact-footer {
        margin-bottom: 25px;
        
        p {
          padding-right: 5%;
        }
      }

      .col-lg-5 {
        margin-bottom: 25px;
      }

      .col-lg-3 {
        width: 50%;
        margin-bottom: 20px;
      }
    }

    // Mobile styles
    @media all and (max-width: 768px) {
      margin-top: 30px;
      padding-bottom: 20px;

      h2 {
        font-size: 16px;
        padding-bottom: 10px;
        margin-bottom: 10px;
      }

      p {
        font-size: 13px;
        line-height: 1.5;
        margin-bottom: 10px;
      }

      .contact-footer {
        margin-bottom: 20px;
        
        p {
          padding-right: 0;
        }
      }

      .logo-image {
        height: 25px;
        margin-top: 10px;
      }

      .col-lg-3 {
        width: 100%;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    // Small mobile styles
    @media all and (max-width: 480px) {
      margin-top: 25px;
      
      h2 {
        font-size: 15px;
      }

      p {
        font-size: 12px;
      }

      .logo-image {
        height: 22px;
      }

      .contact-footer {
        margin-bottom: 15px;
      }
    }
  }
  .footer-copy-right {
    p {
      text-align: center;
      padding: 30px 0;
      color: white;
      font-size: 14px;
    }
  }

  @media all and (max-width: 1200px) {
    .subscribe-to-news {
      .social-us ul {
        float: right;
        li {
          a {
            font-size: 14px;
          }
        }
      }
      h2 {
        font-size: 20px;
      }
      input.form-control {
        width: 250px;
      }
    }
  }

  @media all and (max-width: 991px) {
    .subscribe-to-news {
      .subscribe-us-text {
        display: none;
      }
      .subscribe-text-hidden {
        display: block !important;
        color: white;
        font-weight: bold;
        padding-bottom: 5px;
        text-align: center;
        margin-bottom: 10px;
      }
      .subs-form {
        flex: 0 0 70%;
        max-width: 70%;
      }
      .social-us ul li {
        a {
          line-height: 70px;
          font-size: 16px;
        }
      }
      .social-button-container {
        flex: 0 0 30%;
        max-width: 30%;
      }
    }
  }
  @media all and (max-width: 769px) {
    .subscribe-to-news {
      .subscribe-box-container{
        display: flex;
        flex-direction: column;
        // align-items: center;
        // justify-content: center;
        .subscribe-text-hidden{
          h2{
            text-align: center;
          }
        }
      }
      .subs-form {
        flex: 0 0 100%;
        max-width: 100%;
      }
      .social-button-container {
        flex: 0 0 100%;
        max-width: 100%;
        margin: 0 auto;
      }
      .social-us ul li {
        a {
          line-height: 40px;
          font-size: 16px;
        }
        &:first-child {
          a {
            padding-left: 0;
          }
        }
      }
    }
  }
  @media all and (max-width: 465px) {
    .subscribe-to-news {
      input.form-control {
        width: 140px;
      }
      .social-button-container{
        .social-us{
          display: flex;
          align-items: center;
          justify-content: center;
          
        }
      }
    }
  }
}

.all-events-container {
  margin-top: 20px;
}

.search-filter-event {
  background-color: #f3f3f2;
  .search-box-options {
    input.search-event {
      border-right: 5px solid #f8fafb;
      height: 60px;
      border-left: none;
      font-size: 18px;
      border-radius: 0;
    }
    .input-group {
      display: flex;
      align-items: center;
    }
    .input-group-text {
      border-radius: 0;
      border-left: none;
      i {
        color: #999;
      }
    }
    .more-filter-options {
      a {
        color: #666;
        cursor: pointer;
        line-height: 60px;
        font-size: 15px;
        &:hover {
          text-decoration: none;
          color: $color-primary-active;
        }
        &[aria-expanded='true'] {
          color: $color-primary-active;
        }
      }
      .pick-date-menu {
        ul {
          padding: 0 5px;
          li {
            a {
              padding: 0 10px;
              font-size: 14px;
              line-height: 35px;
              background-color: #f8fafb;
              display: block;
              margin-bottom: 5px;
              &:hover {
                color: black;
              }
            }
          }
        }
      }
      .price-range-text {
        font-size: 14px;
        padding: 5px 0 0 0;
        color: #888;
        text-align: right;
        font-weight: 600;
      }
    }
    padding: 0 15px;
    background: white;
    margin-bottom: 15px;
    box-shadow: 0 0.05em 1px rgba(0, 0, 0, 0.1);
  }
}

.category-select-filter {
  display: flex !important;
  flex-wrap: nowrap !important;
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch;
  gap: 8px !important;
  padding-bottom: 8px !important;
  scrollbar-width: none; // Firefox
  &::-webkit-scrollbar { display: none; } // Chrome/Safari

  .category-item {
    flex: 0 0 auto;
    white-space: nowrap;
    min-width: 110px;
    max-width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
    height: 32px !important;
    padding: 6px 10px !important;
    font-size: 13px !important;
  }
}

// Style for the dropdown menu
.ant-dropdown {
  .ant-dropdown-menu {
    padding: 8px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    .ant-dropdown-menu-item {
      padding: 10px 16px; // Increased padding
      border-radius: 6px;
      font-size: 14px; // Increased font size
      
      i {
        margin-right: 8px;
        color: $color-primary;
        font-size: 15px; // Increased icon size
      }

      &:hover {
        background: rgba($color-primary, 0.05);
        color: $color-primary;
      }
    }
  }
}

.container-title {
  background-color: #f8fafb;
  padding: 8px;
  h3 {
    color: #666;
    i {
      color: #888;
    }
  }
}

.detail-main-img-options {
  min-height: 300px;
  max-width: 90%;
  margin: 15px auto;
  .detail-cover-img {
    position: relative;
    height: auto;
    // > img {
    //   position: absolute;
    // }
    .logo-image {
      position: absolute;
      width: 15%;
      box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.4);
      bottom: 20px;
      left: 20px;
      background-color: white;
      padding: 5px;
    }
  }
  .event-options {
    margin-top: 20px;
    .book-now {
      text-align: center;
      padding-right: 25px;
      a {
        padding-left: 30px;
        padding-right: 30px;
        display: inline-block;
        margin-top: 15px;
        font-size: 22px;
        font-weight: 600;
      }
    }
    .options {
      min-height: 100px;
      display: flex;
      @media (max-width: 768px) {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-around;
        text-align: center;
        padding: 0 15px;
      }
      .opt {
        text-align: center;
        width: 33.99%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        h2 {
          padding-left: 20px;
          color: $color-primary;
          font-size: 25px;
          text-align: left;
          line-height: 20px;
          font-weight: 600;
          span {
            color: #888;
            font-size: 12px;
            font-weight: normal;
          }
        }
        a.like-button {
          font-size: 30px;
          padding: 10px 0;
          display: inline-block;
        }
        p {
          line-height: 0;
          margin: 0 !important;
          padding: 0 !important;
          color: #777;
          font-size: 14px;
        }
      }
      .opt-one {
        background: #fefefe;
        width: 20%;
      }
      .opt-two {
        width: 28%;
        background: #f9f9f9;
      }
      .opt-three {
        width: 31%;
        background: #f5f5f5;
        border-right: 2px solid $color-primary;
      }
      .opt-four {
        background: #f6f6f6;
        padding: 0 15px;
        button {
          font-weight: 400;
        }
        p {
          margin-top: 15px !important;
        }
        a {
          color: #666;
          i {
            color: $color-primary;
            font-size: 18px;
          }
          &:hover {
            color: $color-primary;
          }
        }
      }
    }
    .rate-movie {
      padding-top: 25px;
      span {
        font-size: 12px;
        color: #888;
      }
      .rate-value {
        font-size: 22px;
        font-weight: bold;
        color: #555;
        padding-left: 15px;
      }
    }
  }
  .main-image {
    margin-top: 10px;
    padding-right: 0;
    img {
      width: 100%;
    }
    .main-image-container {
      position: relative;
      img.play-icon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        height: 100px;
        width: 100px;
        cursor: pointer;
      }
    }
    .bread-crumb {
      li {
        display: inline-block;
        margin: 5px 10px 15px 0;
        &.current {
          color: #888;
        }
        font-size: 14px;
        a {
          color: #666;
          font-size: 14px;
          &:hover {
            text-decoration: none;
            color: $color-primary-active;
          }
        }
        i {
          color: #777;
          padding: 0 0 0 5px;
          font-size: 12px;
        }
      }
    }
  }
  .evt-detail-info {
    background: #f3f3f2;
    border-left: 5px solid $color-primary;
    //max-height: 950px;
    .inquiry-success {
      h2 {
        color: $color-primary;
      }
      h3 {
        font-weight: bold;
      }
      p {
        color: $color-light;
      }
      b {
        font-weight: bold;
        color: #333;
      }
    }
    h1 {
      color: $color-primary !important;
      & + div {
        font-size: 17px !important;
        line-height: 1.6;
        color: #777;
        margin-top: 15px;
      }
    }
    .content-padding {
      padding: 30px 10px;
    }
    .location-list {
      margin-top: 40px;
      li {
        min-height: 80px;
        background-color: white;
        box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
        margin-bottom: 15px;
        padding: 10px;
        cursor: pointer;
        h2 {
          color: #444;
          font-size: 17px;
          span {
            float: right;
            color: #777;
            font-weight: normal;
            font-size: 14px;
          }
        }
        p {
          font-size: 12px;
          color: $color-primary;
          line-height: 12px;
          margin-bottom: 10px;
        }
        span {
          color: #888;
          font-size: 14px;
        }
      }
    }
    .category-list {
      li {
        display: inline;
        a {
          font-size: 12px;
          background-color: #9c9c9c;
          color: white;
          padding: 1px 5px 2px 5px;
          border-radius: 3px;
          margin-right: 5px;
        }
      }
    }
    h1 {
      color: #777;
      span {
        color: $color-primary;
        font-size: 32px;
      }
    }
    .stay {
      color: #666;
    }
    .booking-contact {
      li {
        i {
          display: inline-block;
          width: 25px;
        }
        font-size: 14px;
        margin-top: 6px;
        color: #666;
      }
    }
    .evt-attraction {
      margin-top: 30px;
      ul {
        li {
          font-size: 14px;
          margin: 10px 0;
          color: #666;
          i {
            padding-right: 10px;
          }
        }
      }
    }
    position: relative;
    h1 {
      line-height: 34px;
      font-size: 26px;
      padding-right: 20px;
      color: #444;
      font-weight: 600;
    }
    p {
      font-size: 14px;
      line-height: 26px;
      margin: 5px 0 25px 0;
      width: 92%;
      color: #555;
    }
    h2 {
      font-size: 18px;
      font-weight: 600;
    }
    .event-social-share {
      margin-top: 30px;
      margin-bottom: 30px;

      h2 {
        font-size: 18px;
        margin-bottom: 15px;
      }

      ul li {
        display: inline;
        margin: 0 20px 0 0;
        float: left;
        a {
          display: block;
          height: 30px;
          width: 30px;
          overflow: hidden;
          img {
            height: 30px;
            display: inline;
          }
        }
      }
    }
    @media (max-width: 768px) {
      width: 100%;
    }
  }

  .event-brief-tab {
    margin-top: 50px;
    ul.nav-tabs {
      border-bottom: 1px solid #999;
      margin-bottom: 20px;
      background: #f9f9f9;
      li {
        a.nav-link {
          color: #333;
          font-size: 15px;
          width: 120px;
          text-align: center;
          height: 50px;
          line-height: 50px;
          padding: 0;
          border-radius: 0;
          cursor: pointer;
          &.active {
            color: $color-primary-active;
            font-weight: 600;
            border-bottom: 3px solid $color-primary;
          }
        }
      }
    }
    .tab-content {
      padding: 30px 10px;
      min-height: 400px;
      h1 {
        font-size: 25px;
        font-weight: 600;
        a {
          color: #777;
        }
        margin-bottom: 10px;
        & + p {
          color: #777;
          font-size: 14px;
          width: 60%;
          line-height: 25px;
        }
      }
      .activity-list {
        margin-bottom: 20px;
        h3 {
          color: $color-primary;
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        p {
          font-size: 14px;
          line-height: 22px;
          color: #666;
          width: 90%;
        }
      }
      .event-gallery {
        img {
          width: 150px;
          height: 150px;
          display: inline;
          margin-right: 5px;
          cursor: pointer;
          border: 1px solid $color-primary;
        }
      }
      .date-selector {
        h2 {
          font-weight: 600;
          font-size: 20px;
          line-height: 25px;
          float: left;
        }
        div.dropdown {
          float: right;
          a.dropdown-toggle {
            font-size: 30px;
            cursor: pointer;
            &:after {
              display: none;
            }
          }
        }
      }
      .tab-pane {
        padding-right: 40px;
      }
    }
  }

  .related-events-scroll {
    min-height: 100px;
    padding: 0;
    h3 {
      font-size: 25px;
      font-weight: 600;
      margin-bottom: 20px;
    }
  }
}

.selection-info-section {
  background-color: white;
  padding: 10px;
  font-weight: bold;
  color: #888;
  font-size: 14px;
  span {
    margin-right: 30px;
  }
}

.list_v_events {
  width: 100%;
  max-height: 150px;
  display: flex;
  margin: 20px 0;
  .image-container {
    flex: 1;
  }
  .content-container {
    flex: 3;
    padding-left: 10px;
    h5 {
      margin-top: 0;
      line-height: 8px;
      font-size: 14px;
      font-weight: bold;
      color: #777;
    }
    p {
      font-size: 12px;
      color: #999;
      line-height: 13px;
      margin-bottom: 0;
    }
  }
}

.related-events-scroll {
  div.event-grid {
    padding: 0 15px;
    .card-body {
      padding: 0 10px 10px 10px;
      min-height: 150px;
    }
    .card-footer {
      font-size: 14px;
    }
    h2 {
      font-size: 20px;
      min-height: 70px;
      width: 70%;
      font-weight: 600;
      padding: 10px 0;
      a {
        color: #444;
      }
    }
    p.card-text {
      color: #555;
      font-size: 14px;
      margin-top: 10px;
      line-height: 20px;
      width: 80%;
    }
  }
}

.tab-details {
  .detail-panel {
    width: 80%;
    min-height: 100px;
    display: flex;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
    .dimage {
      width: 15%;
      // img {
      //   width: 50%;
      // }
    }
    .dinfo {
      flex: 1;
      //padding: 15px 0;
      margin-left: 20px;
      .dinfo-content {
        padding: 0 15px;
        border-left: 1px solid $color-primary;
        height: 100%;
        h2 {
          line-height: 23px;
          font-size: 22px;
          font-weight: 800;
          // color: #444;
          color: $color-primary;
          + p {
            padding: 10px 0;
            color: #777;
            font-size: 14px;
            span {
              font-style: italic;
              font-weight: 600;
            }
          }
        }
        .artist-details {
          padding: 10px 0;
          line-height: 20px;
          font-size: 13px;
          width: 90%;
          color: #555;
        }
      }
    }
  }
}

@media all and (max-width: 768px) {
  .tab-details {
    .detail-panel {
      width: 100%;
    }
  }
  .detail-main-img-options .event-brief-tab .tab-content .tab-pane {
    padding-right: 0;
  }
}

.ticket-counter {
  margin: 30px 0;
  li {
    min-height: 80px;
    width: 87%;
    &:nth-child(2n + 1) {
      background: #f9f9f9;
    }
  }
}

.bookWizard {
  display: flex;
  justify-content: space-between;
  li {
    width: 45%;
    a {
      color: #495057;
      font-weight: 600;
      padding: 0 0 5px 0;
      &[aria-selected='true'] {
        color: $color-primary !important;
      }
      i {
        font-size: 20px;
      }
      i.fa-ticket-alt {
        transform: rotate(-45deg);
      }
    }
    p {
      font-size: 12px;
      color: #999;
      width: 90%;
    }
  }
}

.book-ticket-steps-det {
  min-height: 100px;
  width: 100%;
  padding-bottom: 20px;
  .ticket-cat {
    overflow: hidden;
    h2 {
      font-size: 16px;
      font-weight: 600;
      line-height: 25px;
    }
    .features {
      width: 100%;
      min-height: 150px;
      background: white;
      padding: 15px;
      h2 {
        color: $color-primary;
        font-size: 20px;
      }
      ul {
        margin-top: 20px;
        li {
          font-size: 13px;
          margin-bottom: 10px;
          color: #555;
          i {
            font-size: 10px;
            padding-right: 3px;
          }
        }
      }
    }
  }
  .gold-ticket {
    .bar {
      & + h2 {
        color: #666;
      }
    }
    .features {
      border-top: 2px solid #ffcc00;
    }
  }
  .silver-ticket {
    .bar {
      & + h2 {
        color: #666;
      }
    }
    .features {
      border-top: 2px solid #979797;
    }
  }
  .bronze-ticket {
    .bar {
      & + h2 {
        color: #666;
      }
    }
    .features {
      border-top: 2px solid #ed6e0b;
    }
  }
  .bar {
    width: 100%;
    margin-top: -10px;
    margin-left: -5px;
    min-height: 20px;
    transform: rotate(-5deg);
  }
}

.comments-list-container {
  //border-bottom: 1px solid #ccc;
  padding: 20px 10px;
  &:nth-child(2n + 1) {
    background: #eee;
  }
  &:nth-child(2n + 2) {
    background: #f7f6f6;
  }
}

.comment-section {
  padding-bottom: 30px;
  a.load-more-events {
    display: block;
    background: #f9f9f9;
    color: #999;
    border-radius: 3px;
    text-align: center;
    padding: 15px;
    font-weight: 600;
    margin-top: 30px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    transition: 0.4s;
    &:hover {
      color: #777;
    }
  }
  a.dropdown-toggle {
    font-size: 15px;
    border-bottom: 2px solid $color-primary;

    &:hover {
      border-bottom: 2px solid $color-primary;
    }
  }
  h2 {
    font-size: 18px;
    font-weight: 600;
  }
  .comment-grid-view,
  .reply-box {
    width: 100%;
    display: flex;
    margin-bottom: 20px;
    .commenter-img {
      border-radius: 5px;
      overflow: hidden;
      height: 80px;
      width: 80px;
    }
    .comment-action-box {
      margin-left: 15px;
      min-height: 80px;
      background: white;
      border-radius: 5px;
      padding: 3px;
      flex: 1;
      h4 {
        color: $color-primary;
        font-weight: 600;
        font-size: 14px;
        span {
          padding-left: 10px;
          color: #999;
          font-size: 12px;
        }
      }
      p {
        min-height: 24px;
        width: 90%;
        padding-top: 5px;
        font-size: 14px;
        line-height: 20px;
        color: #666;
      }
      a {
        font-size: 12px;
        margin-right: 15px;
        font-weight: bold;
        color: #999;
        span {
          font-size: 12px;
        }
      }
      textarea {
        border: 3px solid rgba(0, 0, 0, 0.2);
        font-size: 18px;
        resize: none;
        transition: 0.3s;
        &:focus {
          border-color: rgba(0, 0, 0, 0.3);
        }
      }
    }
    .comment-text {
      padding: 10px;
    }
  }

  .reply-box {
    margin-top: 15px;
    .commenter-img {
      height: 50px;
      width: 50px;
    }
    padding-left: 95px;
    .comment-action-box {
      p {
        font-size: 13px;
      }
    }
    .comment-text {
      height: auto;
      min-height: auto;
    }
    textarea {
      font-size: 13px !important;
      border-width: 2px !important;
    }
  }

  ul.nav-tabs {
    border-bottom: 1px solid #eee !important;
    a.nav-link.active {
      padding: 10px 40px 5px 15px;
      color: $color-primary;
      font-weight: 600;
      border-bottom: 2px solid $color-primary;
      transition: 0.3s;
    }
  }
}

.upcoming-venue-events {
  h1 {
    font-size: 30px;
    font-weight: 600;
    color: #555;
    & + .title-underline {
      background: $color-primary;
      width: 150px;
      margin-bottom: 30px;
    }
  }
  padding: 30px 15px 30px 0;
}

.login-container {
  display: flex;
  flex-flow: column;
  flex: 1;
  .ant-input {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    border: none;
    border-bottom: 1px solid #9c9c9c;
    padding-left: 20px !important;
    &:focus {
      border-color: #a80d5e;
      box-shadow: none;
    }
  }
  .ant-input-affix-wrapper .ant-input-prefix {
    left: 0;
  }
  .ant-input-prefix {
    i {
      color: $color-primary;
    }
  }
  // padding: 80px 15px;
  .login-input-box {
    max-width: 600px;
    margin: auto;
    min-height: 150px;
    color: #333;
    h1 {
      font-weight: 400;
      color: $color-primary;
      font-size: 40px;
      margin-bottom: 10px;
      @media (max-width: 576px) {
        font-size: 30px;
        font-weight: 400;
      }
      @media (max-width: 320px) {
        font-size: 25px;
        font-weight: 400;
      }
    }
    p {
      font-size: 13px;
      color: #555;
      line-height: 22px;
      margin-bottom: 30px;
      letter-spacing: 0.03em;
    }

    .login-extra-option {
      h3 {
        text-align: center;
        font-weight: 800;
        color: #777;
        line-height: 30px;
        margin-top: 20px;
        margin-bottom: 10px;
        font-size: 20px;
      }
      p.alternate-login {
        text-align: center;
        font-size: 14px;
        margin-bottom: 20px;
        a {
          font-size: 16px;
          text-decoration: underline;
          &.facebook {
            color: #4266b2;
          }
          &.google {
            color: #ea4336;
          }
          
        }
      }

      .more-links {
        border-top: 1px solid #eee;
        padding-top: 20px;
        display: flex;
        justify-content: space-between;
        a {
          font-size: 15px;
          text-decoration: underline;
        }
      }
    }
    @media (max-width: 425px) {
      width: 100% !important;
      padding: 0 20px !important;
    }
  }
}

.login-decorator {
  padding-top: 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.dashboard-cover {
  width: 100%;
  min-height: 135px;
  background: linear-gradient(rgba(168, 13, 93, 0.65), rgba(168, 13, 93, 0.65)),
    url('/images/back-vector.jpeg');
  background-size: cover;
  position: relative;
}

.notify-popover {
  position: absolute;
  bottom: 20px;
  right: 5%;
  z-index: 99;
  max-width: 276px;
  line-height: 1.5;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 0.3rem;
  h2 {
    font-size: 18px;
    font-weight: 600;
  }
  hr {
    margin: 5px 0;
  }
}

.dashboard-user {
  padding-bottom: 50px;
  min-height: 600px;
  .user-profile-info {
    // max-height: 300px;
    padding: 0;
    background: white;
    display: flex;
    align-items: center;
    border-left: 5px solid $color-primary;
    flex-direction: column;
    .profile-picture {
      margin-top: -90px;
      border: 5px solid white;
      height: 150px;
      width: 150px;
      background: white;
      border-radius: 200px;
      position: relative;
      overflow: hidden;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 100%;
      }
      .ant-progress {
        position: absolute;
        //z-index: 999;
      }
      .ant-progress-inner {
        height: 140px !important;
        width: 140px !important;
      }
      .ant-progress-circle-path {
        stroke: $color-primary !important;
      }
      .upload-btn-wrapper {
        position: absolute;
        width: 135px;
        height: 40px;
        border-bottom-left-radius: 100px;
        border-bottom-right-radius: 100px;
        bottom: 0;
        background: $color-primary;
        opacity: 0.7;
        cursor: pointer;
        overflow: hidden;
        .user-profile-upload {
          height: 40px;
          width: 130px;
          position: absolute;
          z-index: 999;
          top: 0;
          opacity: 0;
          cursor: pointer;
        }
        .upload-icon {
          display: block;
          position: absolute;
          color: white;
          top: 10px;
          left: 60px;
        }
      }
    }
    h3 {
      font-weight: 600;
      font-size: 18px;
      padding: 10px 0 0 0;
      color: #666;
    }
    p {
      font-size: 13px;
      color: #999;
      i {
        padding-left: 5px;
      }
    }
    .padding-left-10 {
      padding-left: 10px;
      width: 100%;
    }
    .profile-completion {
      padding: 5px 0;
      border-top: 1px solid #eee;
      margin: 40px 0;
      width: 100%;
      .ant-progress-success-bg {
        background: #b13776;
      }
      .ant-progress-bg {
        background-color: $color-primary-active;
      }
      h4 {
        font-size: 13px;
        font-weight: 600;
        color: #666;
        & + p {
          font-size: 10px;
        }
      }
      p {
        font-size: 10px;
      }
      .profile-completion-content {
        padding: 15px 15px 0 0;
      }
    }
    .profile-footer {
      min-height: 60px;
      background: #fcfcfc;
      border-top: 1px solid #eee;
      // margin-top: auto;
      // width: 100%;
      display: flex;
      div {
        flex: 1;
        background: #fefefe;
        border-right: 1px solid #eee;
        min-height: 60px;
        display: table;
        &:last-child {
          border-right: none;
        }
        a {
          padding-left: 20px;
          display: table-cell;
          vertical-align: middle;
          font-weight: 600;
          cursor: pointer;
          font-size: 12px;
          i {
            font-size: 14px;
          }
          span {
            color: #999;
            font-weight: normal;
            font-size: 11px;
          }
          &:hover {
            background: #f7f7f7;
          }
        }
      }
    }
  }
  .user-profile-content {
    padding-left: 0;
  }

  .dashboard-navigation {
    min-height: 60px;
    background: $color-primary;
    display: flex;
    flex-direction: row;
    .dashboard-home {
      width: 65px;
      min-height: 60px;
      background: $color-primary-active;
      text-align: center;
      display: table;
      a {
        color: white;
        display: table-cell;
        vertical-align: middle;
      }
    }
    .dash-nav-items {
      padding: 0 20px;
      flex: 10;
      ul li {
        list-style: none;
        display: inline;
        a {
          color: #eee;
          line-height: 60px;
          font-size: 12px;
          margin-right: 30px;
          i {
            padding: 0 4px;
          }
          &.active {
            font-weight: 800;
            color: white;
            font-size: 14px;
          }
        }
      }
    }
    .add-info {
      background: $color-primary-active;
      flex: 3;
      padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      span {
        color: white;
        font-weight: 800;
        font-size: 12px;
        i {
          font-size: 10px;
          padding: 0 2px;
          color: $color-secondary;
        }
      }
      a {
        color: white;
        font-size: 18px;
      }
    }
  }
  .dashboard-content {
    display: flex;
    margin-top: 30px;
    flex-direction: row;
    .content-spacing {
      width: 65px;
    }
    .content-list {
      flex: 8;
      &.full-width-page {
        flex: none;
        width: 85%;
      }
      .tabs-switcher {
        display: flex;
        flex-direction: row;
        margin-bottom: 10px;
        color: #666;
        .mtab {
          background: white;
          margin-right: 15px;
          padding: 10px 25px 10px 15px;
          //border-radius: 3px;
          cursor: pointer;
          &.active {
            background-color: $color-primary;
            color: white;
            box-shadow: 0 0 4px 2px rgba(0, 0, 0, 0.2);
          }
        }
      }
      .tab-content-info {
        padding: 40px 15px;
        width: 100%;
      }

      .list-container li {
        background: white;
        margin-bottom: 20px;
        min-height: 80px;
        border-radius: 3px;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: row;
        img {
          // border-radius: 80px;
          margin: 10px;
          height: 80px;
          width: 140px;
        }
        .title-content {
          padding-left: 10px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          h2 {
            font-size: 17px;
            font-weight: 600;
            color: #666;
            margin-bottom: 5px;
          }
          p {
            color: #888;
            font-size: 13px;
            width: 90%;
          }
          .add-info {
            margin-top: 4px;
            p {
              width: 100%;
            }
          }
        }
        .booked-event-content {
          padding: 10px 0;
        }
        div.action-bar {
          display: flex;
          width: 25%;
          background: $color-primary;
          margin-left: auto;
          .ab_foot {
            display: flex;
            flex-direction: row;
            font-size: 13px;
            justify-content: center;
            width: 100%;
            height: 30px;
            text-align: center;
            align-self: flex-end;
            padding: 10px;
            line-height: 10px !important;
            color: #ccc;
            background: $color-primary-active;
          }
        }
      }
      .booked-events-list {
        li {
          min-height: 100px;
        }
      }
    }
    .content-history {
      flex: 3;
    }
  }
}

.nav-items-footer {
  padding: 40px 0;
  .container-fluid-navigation {
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
  }
  ul li {
    list-style: none;
    display: inline;
    margin-right: 30px;
    font-size: 13px;
    font-weight: 400;
    a {
      color: #999;
      &:hover {
        text-decoration: underline;
        color: #333;
      }
    }
  }
}

.ticket-summary {
  width: 100%;
  background: rgb(237, 237, 237);
  border-bottom: 1px solid rgb(221, 221, 221);
  transition: 0.3s linear;
  
  .ticket-summary-det {
    position: relative;
    width: 100%;
    min-height: 350px;
    padding: 20px 0;
    
    .events-details-final {
      display: flex;
      align-items: flex-start;
      gap: 32px;
      margin-bottom: 24px;
      
      .ticket-summary-img {
        flex: 0 0 400px;
        position: relative;
        
        img {
          width: 100%;
          height: 300px;
          object-fit: cover;
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
          transition: all 0.3s ease;
          display: block;
          
          &:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
          }
        }
        
        a {
          position: absolute;
          top: 12px;
          right: 12px;
          display: inline-flex;
          align-items: center;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
          color: #495057;
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(8px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          text-decoration: none;
          border: 1px solid rgba(255, 255, 255, 0.4);
          transition: all 0.3s ease;
          z-index: 10;
          
          i {
            margin-right: 4px;
            font-size: 12px;
            color: #6c757d;
          }
          
          &:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            color: #212529;
          }
        }
      }
      
      .summary-details {
        flex: 1;
        padding: 0;
        
        p {
          margin: 0 0 12px 0;
          color: #6c757d;
          font-size: 16px;
          line-height: 1.6;
          font-weight: 400;
          
          span {
            color: #adb5bd;
            font-weight: 300;
          }
          a {
            color: $color-primary;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
            
            &:hover {
              color: darken($color-primary, 10%);
              text-decoration: underline;
            }
          }
        }
        
        h1 {
          margin: 16px 0 20px 0;
          color: #212529;
          font-size: 32px;
          font-weight: 700;
          line-height: 1.3;
          letter-spacing: -0.5px;
        }
        
        p:last-child {
          margin-top: 0;
          color: #6c757d;
          font-size: 16px;
          line-height: 1.6;
          font-weight: 400;
        }
      }
    }
    
    .ticket-card {
      background: white;
      border-radius: 20px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      padding: 32px 24px;
      position: relative;
      border: none;
      margin-bottom: 20px;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, $color-primary 0%, lighten($color-primary, 20%) 100%);
        border-radius: 20px 20px 0 0;
      }
      
      h4 {
        margin: 0 0 24px 0;
        color: #212529;
        font-size: 22px;
        font-weight: 700;
        text-align: center;
        padding: 0;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 50px;
          height: 3px;
          background: $color-primary;
          border-radius: 2px;
        }
      }
      
      .amount-summary {
        margin-top: 24px;
        
        .ant-table {
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
          border: 1px solid #e9ecef;
          
          .ant-table-thead > tr > th {
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
            font-weight: 600;
            color: #495057;
            padding: 16px 12px;
            font-size: 14px;
            text-align: center;
            
            &:first-child {
              text-align: left;
            }
          }
          
          .ant-table-tbody > tr > td {
            padding: 14px 12px;
            border-bottom: 1px solid #f1f3f4;
            font-size: 14px;
            color: #495057;
            text-align: center;
            
            &:first-child {
              text-align: left;
              font-weight: 500;
            }
            
            &:last-child {
              font-weight: 600;
              color: #212529;
            }
          }
          
          .ant-table-tbody > tr:last-child > td {
            border-bottom: none;
            background: #f8f9fa;
            font-weight: 700;
            color: #212529;
          }
        }
      }
    }
    
    .ticket_expiry {
      text-align: center;
      padding: 0;
      
      span {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        box-shadow: 0 4px 16px rgba(255, 107, 107, 0.3);
        display: inline-flex;
        align-items: center;
        
        &::before {
          content: '⏰';
          margin-right: 8px;
          font-size: 16px;
        }
      }
    }
  }

  // Desktop styles (1200px and above) - Two column layout
  @media (min-width: 1200px) {
    .ticket-summary-det {
      padding: 32px 0;
      
      .events-details-final {
        margin-bottom: 0;
        gap: 40px;
        
        .ticket-summary-img {
          flex: 0 0 450px;
          
          img {
            height: 350px;
          }
          
          a {
            top: 16px;
            right: 16px;
            padding: 8px 14px;
            font-size: 13px;
            
            i {
              font-size: 13px;
            }
          }
        }
        
        .summary-details {
          h1 {
            font-size: 36px;
          }
          
          p {
            font-size: 18px;
          }
          
          p:last-child {
            font-size: 16px;
          }
        }
      }
      
      .ticket-card {
        position: absolute;
        top: 32px;
        right: 0;
        width: 350px;
        max-width: none;
        margin: 0;
        padding: 28px 20px;
        
        h4 {
          font-size: 20px;
        }
        
        .amount-summary {
          margin-top: 20px;
          
          .ant-table {
            .ant-table-thead > tr > th {
              padding: 14px 10px;
              font-size: 13px;
            }
            
            .ant-table-tbody > tr > td {
              padding: 12px 10px;
              font-size: 13px;
            }
          }
        }
      }
      
      .ticket_expiry {
        position: absolute;
        bottom: 32px;
        right: 0;
        width: 350px;
        text-align: center;
        
        span {
          padding: 10px 20px;
          font-size: 13px;
          
          &::before {
            font-size: 14px;
          }
        }
      }
    }
  }

  // Large Tablet styles (992px - 1199px)
  @media (max-width: 1199px) and (min-width: 992px) {
    .ticket-summary-det {
      padding: 24px 0;
      
      .events-details-final {
        gap: 24px;
        
        .ticket-summary-img {
          flex: 0 0 350px;
          
          img {
            height: 260px;
          }
          
          a {
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            font-size: 11px;
            
            i {
              font-size: 11px;
            }
          }
        }
        
        .summary-details {
          h1 {
            font-size: 28px;
          }
        }
      }
      
      .ticket-card {
        padding: 24px 20px;
        max-width: 500px;
        
        h4 {
          font-size: 20px;
        }
      }
    }
  }

  // Tablet styles (768px - 991px)
  @media (max-width: 991px) {
    .ticket-summary-det {
      padding: 20px 0;
      
      .events-details-final {
        flex-direction: column;
        gap: 20px;
        align-items: center;
        text-align: center;
        
        .ticket-summary-img {
          flex: none;
          width: 100%;
          max-width: 400px;
          
          img {
            height: 240px;
          }
          
          a {
            top: 12px;
            right: 12px;
            padding: 6px 12px;
            font-size: 12px;
            
            i {
              font-size: 12px;
            }
          }
        }
        
        .summary-details {
          padding: 0 20px;
          text-align: center;
          
          h1 {
            font-size: 26px;
            margin: 12px 0 16px 0;
          }
          
          p {
            font-size: 15px;
          }
          
          p:last-child {
            font-size: 14px;
          }
        }
      }
      
      .ticket-card {
        padding: 20px 16px;
        max-width: 400px;
        
        h4 {
          font-size: 18px;
        }
      }
    }
  }

  // Mobile styles (576px - 767px)
  @media (max-width: 767px) {
    .ticket-summary-det {
      padding: 16px 0;
      
      .events-details-final {
        gap: 16px;
        
        .ticket-summary-img {
          max-width: 100%;
          
          img {
            height: 200px;
            border-radius: 12px;
          }
          
          a {
            top: 8px;
            right: 8px;
            padding: 4px 8px;
            font-size: 10px;
            border-radius: 16px;
            
            i {
              font-size: 10px;
            }
          }
        }
        
        .summary-details {
          padding: 0 16px;
          
          h1 {
            font-size: 24px;
            margin: 10px 0 14px 0;
          }
          
          p {
            font-size: 14px;
          }
          
          p:last-child {
            font-size: 13px;
          }
        }
      }
      
      .ticket-card {
        padding: 16px 12px;
        border-radius: 16px;
        max-width: 100%;
        
        h4 {
          font-size: 16px;
        }
        
        .amount-summary {
          margin-top: 16px;
          
          .ant-table {
            .ant-table-thead > tr > th {
              padding: 12px 8px;
              font-size: 13px;
            }
            
            .ant-table-tbody > tr > td {
              padding: 10px 8px;
              font-size: 13px;
            }
          }
        }
      }
      
      .ticket_expiry {
        span {
          padding: 10px 20px;
          font-size: 13px;
          
          &::before {
            font-size: 14px;
          }
        }
      }
    }
  }

  // Small mobile styles (up to 575px)
  @media (max-width: 575px) {
    .ticket-summary-det {
      padding: 12px 0;
      
      .events-details-final {
        gap: 12px;
        
        .ticket-summary-img {
          img {
            height: 180px;
            border-radius: 10px;
          }
          
          a {
            top: 6px;
            right: 6px;
            padding: 3px 6px;
            font-size: 9px;
            border-radius: 14px;
            
            i {
              font-size: 9px;
            }
          }
        }
        
        .summary-details {
          padding: 0 12px;
          
          h1 {
            font-size: 22px;
            margin: 8px 0 12px 0;
          }
          
          p {
            font-size: 13px;
          }
          
          p:last-child {
            font-size: 12px;
          }
        }
      }
      
      .ticket-card {
        padding: 14px 10px;
        border-radius: 12px;
        
        h4 {
          font-size: 15px;
        }
        
        .amount-summary {
          margin-top: 12px;
          
          .ant-table {
            .ant-table-thead > tr > th {
              padding: 10px 6px;
              font-size: 12px;
            }
            
            .ant-table-tbody > tr > td {
              padding: 8px 6px;
              font-size: 12px;
            }
          }
        }
      }
      
      .ticket_expiry {
        span {
          padding: 8px 16px;
          font-size: 12px;
          
          &::before {
            font-size: 12px;
          }
        }
      }
    }
  }
}

.proceed-ticket {
  min-height: 100px;
  //box-shadow: inset 0 -1px 1px rgba(0, 0, 0, 0.1);
  .buy-ticket-steps {
    margin: 0 auto 80px auto;
    padding-top: 80px;
    position: relative;
    .hide-show-bar {
      background: rgb(237, 237, 237);
      display: inline-block;
      z-index: 999;
      padding: 0 15px;
      min-height: 30px;
      position: absolute;
      right: 0;
      top: 0;
      border-bottom-right-radius: 5px;
      border-bottom-left-radius: 5px;
      border: 1px solid rgb(221, 221, 221);
      border-top: none;
      line-height: 30px;
      font-size: 13px;
      small {
        font-size: 11px;
        font-weight: bold;
      }
      i {
        font-size: 16px;
      }
      &:focus {
        text-decoration: none;
      }
    }
    ul.nav-tabs {
      border-bottom: 4px solid #999;
      justify-content: space-between;
      display: flex;
      .nav-link {
        margin-bottom: -35px;
        border-radius: 70px;
        height: 70px;
        font-size: 25px;
        width: 70px;
        line-height: 60px;
        text-align: center;
        padding: 0;
        border: 4px solid #999;
        background: white;
        color: #999;
        &.active {
          color: $color-primary;
          border-color: $color-primary;
        }
      }
    }
    .tab-content {
      margin-top: 50px;
      padding: 10px 0 50px 0;
      h1 {
        color: #666;
        font-size: 30px;
        font-weight: 600;
        text-align: center;
      }
      p.step-info {
        max-width: 500px;
        margin: 0 auto;
        font-size: 13px;
        line-height: 20px;
        color: #999;
        padding: 10px;
        text-align: center;
      }
    }
  }
  .proceed-ticket-actions {
    padding: 30px 0;
  }

  // Tablet styles
  @media (max-width: 992px) and (min-width: 768px) {
    .buy-ticket-steps {
      margin: 0 auto 60px auto;
      padding-top: 60px;
      
      ul.nav-tabs {
        .nav-link {
          height: 60px;
          width: 60px;
          font-size: 20px;
          line-height: 50px;
          border-width: 3px;
        }
      }
      
      .tab-content {
        margin-top: 40px;
        padding: 8px 0 40px 0;
        
        h1 {
          font-size: 26px;
        }
        
        p.step-info {
          max-width: 400px;
          font-size: 12px;
        }
      }
    }
    
    .proceed-ticket-actions {
      padding: 25px 0;
    }
  }

  // Mobile styles
  @media (max-width: 768px) and (min-width: 480px) {
    .buy-ticket-steps {
      margin: 0 auto 40px auto;
      padding-top: 40px;
      
      .hide-show-bar {
        padding: 0 10px;
        min-height: 25px;
        line-height: 25px;
        font-size: 11px;
        
        small {
          font-size: 10px;
        }
        
        i {
          font-size: 14px;
        }
      }
      
      ul.nav-tabs {
        border-bottom-width: 3px;
        
        .nav-link {
          height: 50px;
          width: 50px;
          font-size: 16px;
          line-height: 42px;
          border-width: 2px;
          margin-bottom: -25px;
        }
      }
      
      .tab-content {
        margin-top: 30px;
        padding: 5px 0 30px 0;
        
        h1 {
          font-size: 22px;
        }
        
        p.step-info {
          max-width: 100%;
          font-size: 11px;
          padding: 8px;
        }
      }
    }
    
    .proceed-ticket-actions {
      padding: 20px 40px;
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    .buy-ticket-steps {
      margin: 0 auto 30px auto;
      padding-top: 30px;
      
      .hide-show-bar {
        padding: 0 8px;
        min-height: 22px;
        line-height: 22px;
        font-size: 10px;
        
        small {
          font-size: 9px;
        }
        
        i {
          font-size: 12px;
        }
      }
      
      ul.nav-tabs {
        border-bottom-width: 2px;
        
        .nav-link {
          height: 40px;
          width: 40px;
          font-size: 14px;
          line-height: 34px;
          border-width: 2px;
          margin-bottom: -20px;
        }
      }
      
      .tab-content {
        margin-top: 25px;
        padding: 5px 0 25px 0;
        
        h1 {
          font-size: 18px;
        }
        
        p.step-info {
          font-size: 10px;
          padding: 5px;
        }
      }
    }
    
    .proceed-ticket-actions {
      padding: 15px 0;
    }
  }
}

section.help-support {
  min-height: 400px;
  padding: 20px 0;
  //background: #0c5460;
  div.search-help {
    min-height: 80px;
    padding: 10px;
    .input-group {
      background: #f5f8fa;
      border-radius: 5px;
      padding: 5px;
      border: 1px solid #ccc;

    }
    input.form-control {
      border: 1px solid #ccc;
    }
    button.btn {
      margin-left: 7px;
      padding: 5px 30px;
    }
  }

  div.search-content {
    padding: 40px 10px;
    color: #666;
    .search_browse {
      font-size: 22px;
      //margin-bottom: 5px;
    }
    .Article-Head {
      h2 {
        margin-bottom: 5px;
      }
      a {
        float: right;
        margin: -19px 0 0 0;
        font-size: 12px;
        background: #eee;
        padding: 1px 5px;
        border-radius: 3px;
        transition: 0s;
        &:hover {
          background: $color-primary;
          color: white;
        }
      }
      .meta {
        font-weight: normal;
        font-size: 12px;
        color: #909799;
      }
    }
    h3 {
      font-size: 16px;
      padding-bottom: 20px;
    }
    a {
      color: #1a8bdb;
    }
    h5 {
      font-size: 13px;
      padding-bottom: 10px;
      a {
        font-size: 12px;
        background: #eee;
        padding: 1px 5px;
        border-radius: 3px;
        margin-left: 5px;
        &:hover {
          background: $color-primary;
          color: white;
        }
      }
      i {
        font-size: 10px;
      }
    }
    .article-line {
      ul {
        padding-left: 18px;
        li {
          margin-bottom: 7px;
          //font-size: 12px;
          list-style: disc;

          p {
            font-size: 20px;
            margin: 5px 0 5px 0;
          }
          .meta {
            font-weight: normal;
            font-size: 10px;
            color: #909799;
          }
          a {
            font-size: 14px;
            margin-bottom: 5px;
          }
          h4 {
            margin-bottom: 5px;
          }
        }
        .support {
          margin-bottom: 10px;
        }
      }
    }

    .contact-section-support {
      p {
        font-size: 14px;
      }
      ul li {
        font-size: 12px;
        color: #999;
        margin-bottom: 5px;
      }
    }

    .article-content {
      margin: 30px 0 30px 0;
      p {
        line-height: 26px !important;
        font-size: 15px;
      }
    }
  }
}

.contact-section {
  min-height: 600px;
  background: #f2f2f2;
  .contact-body {
    .contact-form-action {
      border-top: 5px solid $color-primary;
      border-bottom: 5px solid $color-primary;
      margin-bottom: auto;
      margin-top: auto;
      flex: 1;
      height: 100%;
      min-height: 700px;
      background: white;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .contact-form-action-content {
        width: 75%;
        .social-connect {
          display: flex;
          flex-direction: row;
          border-bottom: 1px solid #eee;
          justify-content: space-between;
          padding: 20px 0;
          a {
            font-size: 18px;
          }
        }
        .contact-form {
          padding: 40px 0 0 0;
          .ant-input,
          button {
            border-radius: 0;
          }
        }
      }
    }
    padding: 30px 0;
    .general-contact-info {
      height: 700px;
      background: url('/images/office.jpeg') no-repeat;
      background-size: cover;
      width: 100%;
      .background-overlay {
        width: inherit;
        height: 100%;
        background: rgba(168, 13, 93, 0.7);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .general-info {
          width: 100%;
          padding: 0 0 0 40px;
          h3 {
            font-size: 16px;
            color: white;
          }
          p {
            margin-top: 15px;
            margin-bottom: 40px;
            font-size: 12px;
            color: white;
          }
        }
      }
    }
    .col-md-5 {
      padding-right: 0;
    }

    h3 {
      color: #999;
      font-weight: bold;
    }
  }
}

.faq-shades {
  background-image: linear-gradient(to bottom, #f7f4f4, #fff);
  padding: 60px 0;
}

.faq-page {
  color: #888;
  h1 {
    font-size: 40px;
    margin-bottom: 30px;
    & + p {
    }
    & + .ant-input-affix-wrapper {
      max-width: 70%;
      display: block;
      margin: auto;
      .ant-input {
        height: 50px;
        font-weight: bold;
        color: #666;
        font-size: 17px;
      }
      & + p {
        max-width: 60%;
        margin: 40px auto;
        text-align: center;
        font-size: 15px;
        line-height: 25px;
      }
    }
  }
  h1,
  h2 {
    font-weight: bold;
    color: #777;
    text-align: center;
    & + p {
      max-width: 60%;
      margin: auto;
      text-align: center;
      font-size: 15px;
      line-height: 25px;
    }
  }
  h2 {
    font-size: 28px;
    margin-bottom: 16px;
    & + p {
    }
  }
  .content-box {
    margin: 50px 0;
  }
}

.toggled {
  overflow: hidden;
  padding-top: 0;
  padding-bottom: 0;
  height: 0;
  border-width: 0 1px;
}

#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: white;
  position: fixed;
  z-index: 1031;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
}

/* Fancy blur effect */
#nprogress .peg {
  display: block;
  position: absolute;
  right: 0;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px white, 0 0 5px white;
  opacity: 1;

  -webkit-transform: rotate(3deg) translate(0px, -4px);
  -ms-transform: rotate(3deg) translate(0px, -4px);
  transform: rotate(3deg) translate(0px, -4px);
}

/* Remove these to get rid of the spinner */
#nprogress .spinner {
  display: block;
  position: fixed;
  z-index: 1031;
  top: 15px;
  right: 15px;
}

#nprogress .spinner-icon {
  width: 18px;
  height: 18px;
  box-sizing: border-box;

  border: solid 2px transparent;
  border-top-color: white;
  border-left-color: white;
  border-radius: 50%;

  -webkit-animation: nprogress-spinner 400ms linear infinite;
  animation: nprogress-spinner 400ms linear infinite;
}

.nprogress-custom-parent {
  overflow: hidden;
  position: relative;
}

.nprogress-custom-parent #nprogress .spinner,
.nprogress-custom-parent #nprogress .bar {
  position: absolute;
}

@-webkit-keyframes nprogress-spinner {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes nprogress-spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

#components-form-demo-normal-login .login-form {
  max-width: 300px;
}

#components-form-demo-normal-login .login-form-forgot {
  float: right;
}

#components-form-demo-normal-login .login-form-button {
  width: 100%;
}

.tab-2-content {
  margin-left: 40px;
}

.tab-3-content {
  padding-left: 80px;
}

.ticket-plans {
  text-align: center;
  h1 {
    font-size: 26px;
    font-weight: bold;
    text-align: center;
    + p {
      font-style: italic;
      color: #999;
      font-size: 13px;
      max-width: 500px;
      margin: 0 auto 20px auto;
      text-align: center;
    }
  }
  .date-on-panel {
    background-color: #e3e3e3;
    max-width: 100px;
    padding: 10px;
    cursor: pointer;
    font-size: 12px;
    line-height: 18px;
    min-height: 80px;
    display: flex;
    align-items: center;
    float: left;
    margin: 10px;
    &.active {
      border-bottom: 4px solid $color-primary;
    }
  }

  .planbox-container {
    .plan-row {
      display: flex;
      flex-wrap: wrap;
    }
    .plan-col {
      display: flex;
      flex-direction: column;
    }
    .plan-box {
      padding: 10px;
      background: white;
      border: 1px solid #ccc;
      // border-top-left-radius: 5px;
      // border-top-right-radius: 5px;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      flex-direction: column;
      flex: 1;
      height: 100%;
      h3 {
        font-weight: bold;
        // margin: 30px 0 10px 0;
        margin-bottom: 10px;
        line-height: 1.3;
        letter-spacing: 3px;
        color: #333;
        word-wrap: break-word;
        overflow-wrap: break-word;
        max-width: 100%;
        text-overflow: ellipsis;
        min-height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        + p {
          font-size: 12px;
          font-style: italic;
          color: #9c9c9c;
        }
      }
      h1.price {
        font-size: 25px;
        // margin-top: 20px;
        margin-top: auto;
        margin-bottom: 20px;
        font-family: $lightFont;
        font-weight: bold;
        color: #333;
        small {
          font-size: 16px;
          font-family: $Lato;
          padding-right: 5px;
        }
      }
      a.btn {
        margin: 20px;
      }
    }
    .most-popular-mark {
      color: transparent;
      padding: 5px 10px;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      text-transform: uppercase;
      font-weight: bold;
      font-size: 15px;
      &.active {
        background-color: $color-primary;
        color: white;
        + .plan-box {
          border-top-left-radius: 0;
          border-top-right-radius: 0;
        }
      }
    }
  }
  .btn-proceed {
    margin-bottom: 10px;
  }
  .seat-selector {
    margin: 30px 0;
  }
  // .number-button {
  //   padding: 8px 16px;
  //   border: 1px solid #ccc;
  //   border-radius: 4px;
  //   transition: all 0.3s ease;
  //   background-color: white;
  //   color: #4a4a4a;
  // }
  
  // .number-button.selected {
  //   color: white;
  //   font-weight: bold;
  //   border-color: green;
  //   background-color: green;
  // }
  

  .number-button {
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
  }
  
  .number-button.selected {
    background-color: #c71782; /* Green color */
    color: white;
    border-color: #c71782;
  }
  
  /* Add spacing between buttons */
  .flex .number-button {
    margin-right: 8px; /* Adjust spacing */
  }
  
  .flex .number-button:last-child {
    margin-right: 0; /* Remove margin for the last button */
  }
  

}

.about-package {
  min-height: 200px;
  width: 100%;
  background-color: #f9f9f9;
  padding: 30px 40px 40px 40px;
  margin-top: 20px;
  h1 {
    color: $color-primary;
    margin-bottom: 30px;
    font-weight: bold;
    font-size: 30px;
  }
  p {
    margin-top: 20px;
    line-height: 28px;
    width: 90%;
    color: #666;
  }
}

.itinerary-list {
  .ant-collapse-header {
    padding: 20px 0 20px 25px !important;
    font-size: 18px;
    font-weight: bold;
    color: #777 !important;
    i.arrow {
      font-size: 16px !important;
      line-height: 60px !important;
    }
  }
}

.rate-modal {
  padding: 30px 0 !important;
  h2 {
    font-size: 25px;
    font-weight: bold;
    text-align: center;
    color: #555;
    &.rate-value {
      font-size: 20px;
      font-style: italic;
    }
  }
  p {
    text-align: center;
    font-size: 14px;
    color: #666;
    font-style: italic;
  }
  a {
    font-size: 14px;
    text-align: center;
  }
}

.search-city-listing {
  ul li {
    background: white;
    &:nth-child(2n) {
      background: #eee;
    }
    a {
      display: block;
      padding: 15px;
      font-weight: bold;
    }
  }
}

.more-cities-list {
  padding: 15px;
  border-top: 5px solid $color-primary;
  background: white;
  max-height: 300px;
  //overflow-x: scroll;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 12px;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
  }
  ul {
    li {
      width: 25%;
      display: inline-block;
      padding-bottom: 3px;
      a {
        color: #666;
        font-size: 14px;
        text-decoration: underline;
      }
    }
  }
}

.casts-list {
  text-align: center;
  .cast-image {
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px auto;
    width: 100px;
  }
  h4 {
    font-weight: bold;
    font-size: 15px;
  }
  p {
    color: #999;
    font-size: 14px;
    &:last-child {
      color: #555;
    }
  }
}

.example-enter {
  max-height: 0;
  transition: all 0.3s ease-in;
}

.example-enter.example-enter-active {
  max-height: 1000px;
}

.example-leave {
  max-height: 1000px;
  transition: all 0.3s ease-out;
}

.example-leave.example-leave-active {
  max-height: 0;
}

.v-error {
  color: red;
  font-size: 13px;
}

.tab-content-info {
  label {
    color: #888;
  }
  label.input-l {
    color: #808080;
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 15px;
    display: block;
  }
  input {
    background: #f7f7f7;
    border-width: 0.1em;
    border-radius: 3px !important;
    box-shadow: inset 0 0.12em 2px #dbdbdb;
  }
  .ant-input {
    height: 45px;

    &:hover {
      border-color: #d9d9d9 !important;
    }
    &:focus {
      border-color: #d9d9d9 !important;
      background: #f7f7f7;
      box-shadow: inset 0 0.12em 2px #dbdbdb;
    }
  }
  .ant-input-affix-wrapper {
    &:hover {
      border-color: #d9d9d9 !important;
    }
  }
  .ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
    border-color: #d9d9d9 !important;
  }
  small {
    font-size: 12px;
    color: #999;
  }
  .ant-radio-inner {
    height: 25px;
    width: 25px;
    &:after {
      height: 15px;
      width: 15px;
      top: 4px;
      left: 4px;
    }
  }
}

.user-preferences {
  padding: 20px;
  width: 100%;
  h1 {
    color: #666;
    font-size: 25px;
    font-weight: 300;
  }
  p {
    color: #888;
    line-height: 24px;
    font-size: 14px;
    font-weight: 300;
  }
  h4 {
    color: #666;
  }
  .event-options .ant-checkbox-wrapper {
    font-size: 13px;
    color: #888;
  }
  .checkbox-opt {
    margin-bottom: 10px;
  }
}

.city-list-container {
  .ant-fullcalendar-header {
    display: none;
  }
  .col-md-3 {
    padding-right: 0;
    &:nth-child(4n) {
      padding-right: 15px;
    }
  }
  .top-cities {
    background-color: $color-primary;
    cursor: pointer;
    margin-bottom: 15px;
    text-align: center;
    padding: 40px 0 15px 0;
    font-size: 18px;
    color: white;
    font-weight: bold;
    display: block;
    position: relative;
    span {
      position: relative;
      z-index: 999;
    }
    &:hover {
      background-color: $color-primary-active;
    }
    &:after {
      content: '';
      opacity: 0.7;
      background-size: cover;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      position: absolute;
    }
    &.kathmandu {
      &:after {
        background-image: url('/images/cities/kathmandu.jpg');
      }
    }
    &.pokhara {
      &:after {
        background-image: url('/images/cities/pokhara.jpg');
      }
    }

    &.biratnagar {
      &:after {
        background-image: url('/images/cities/biratnagar.jpg');
      }
    }
    &.birgunj {
      &:after {
        background-image: url('/images/cities/birgunj.jpg');
      }
    }
    &.dharan {
      &:after {
        background-image: url('/images/cities/dharan.jpg');
      }
    }
    &.bharatpur {
      &:after {
        background-image: url('/images/cities/bharatpur.jpg');
      }
    }
    &.janakpur {
      &:after {
        background-image: url('/images/cities/janakpur.jpg');
      }
    }
    &.dhangadhi {
      &:after {
        background-image: url('/images/cities/dhangadi.jpg');
      }
    }
  }
}

.loader-package {
  margin: 30px auto;
}

.text-bold {
  font-weight: 700 !important;
  font-family: $Lato !important;
}

.faq-events {
  .ant-collapse-header {
    font-weight: bold;
  }
  .ant-collapse-content-box {
    padding-left: 40px;
  }
}

.event-terms {
  font-size: 14px;
  color: $color-light;
}

.evt-comment {
  position: relative;
  i.anticon {
    position: absolute;
    bottom: 10px;
    right: 10px;
  }
}

.side-paragraph-details {
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}

.no-data-found {
  width: 100%;
  text-align: center;
  margin-bottom: 30px;
  img {
    display: block;
    margin: 20px auto;
    padding-left: 60px;
    opacity: 0.7;
  }
  h4 {
    font-size: 24px;
    color: $color-light;
    font-weight: bold;
    text-align: center;
  }
  p {
    text-align: center;
    color: $color-light;
    font-size: 14px;
  }
}

.venue-chooser {
  width: 70%;
  display: flex;
  margin: auto;
  text-align: left;
  cursor: pointer;
  padding: 5px;
  background-color: white;
  h3 {
    font-weight: bold;
    line-height: 20px;
  }
  p {
    font-size: 13px;
    line-height: 15px;
    color: #999;
  }
  .venue-icon {
    flex: 1;
    margin-right: 10px;
    img {
      width: 100%;
    }
  }
  .venue-details {
    flex: 8;
    padding-top: 8px;
  }

  &:nth-child(2n + 2) {
    background-color: #eee;
  }
}

.q-list {
  margin-bottom: 50px;
}

.terms-conditions {
  p {
    font-size: 14px;
    color: #999;
    margin-bottom: 20px;
  }
  h1,
  h2,
  h3,
  h4,
  h5 {
    font-weight: bold;
  }
  ul {
    padding-left: 30px;
  }
  li {
    list-style: disc;
    font-size: 13px;
    padding-left: 5px;
    color: #999;
    margin-bottom: 10px;
  }
}

.cat-container {
  padding: 30px 0;
  background-color: #f2f2f2;
  .cat-box {
    h2 {
      font-size: 17px;
      font-weight: bold;
      color: #555;
      & + p {
        font-size: 12px;
        line-height: 17px;
        max-width: 80%;
        margin-top: 5px;
        color: #999;
      }
    }
    display: flex;
    padding: 15px 10px 0 10px;
    min-height: 200px;
    background-color: white;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    border-top: 3px solid $color-primary;
    flex-direction: column;
    justify-content: space-between;
    .cat-name {
      margin-top: 30px;
    }
    .cat-foot {
      width: 100%;
      display: flex;
      color: #555;
      padding: 10px;
      align-self: flex-end;
      font-size: 13px;
      font-weight: bold;
      justify-content: space-between;
    }
  }
}

.category-browse {
  min-height: 100px;
  width: 100%;
  margin-top: 80px;
  margin-bottom: 100px;
  padding: 10px;
  display: flex;
  div.scroll-btn {
    min-height: 100px;
    // flex: 1.4;
    h2 {
      font-size: 20px;
      font-weight: bold;
      color: #666;
      span {
        font-size: 25px;
        color: #444;
        padding-right: 10px;
      }
      margin-bottom: 25px;
    }
    a {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: white;
      height: 40px;
      width: 40px;
      font-size: 20px;
      color: #999;
      border-radius: 40px;
      box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.08);
      text-decoration: none;
    }
  }
  div.cat-content {
    min-height: 150px;
    flex: 6;
    width: 100%;
    overflow: hidden;
    .slider-control-centerleft,
    .slider-control-centerright {
      display: none;
    }
    div.cat-items {
      padding: 0 15px;
      background-color: white;
      height: 150px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      justify-content: center;
      color: #888;
      transition: color 0.3s;
      &:hover {
        color: #555;
      }
      i {
        font-size: 40px;
      }
      h4 {
        font-size: 13px;
        margin-top: 10px;
        font-weight: bold;
        color: #777;
      }
    }
  }
}

.full_screen_custom_modal {
  &.ant-modal {
    top: 0;
    padding-bottom: 0;
  }
  .ant-modal-content {
    min-height: 100vh;
    border-radius: 0;
    opacity: 0.95;
    &:after {
      content: '';
      background: url('/images/concert-drawer-back.jpg') no-repeat 0 200px
        fixed;
      background-size: cover;
      opacity: 0.03;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      position: absolute;
      z-index: -1;
    }
  }
  .ant-modal-header {
    display: none;
  }
  .ant-modal-close-x {
    .anticon {
      font-size: 38px;
    }
  }
  // .ant-modal-close-x {
  //   width: 92px;
  // }
  .ant-modal-body {
    padding: 0;
  }
}

div.search-modal {
  .search-close {
    position: fixed;
    right: 50px;
    top: 20px;
    font-size: 40px;
    color: #999;
    cursor: pointer;
    z-index: 999;
    &:hover {
      color: #666;
    }
  }
  &:after {
    content: '';
    background: url('/images/concert-drawer-back.jpg') no-repeat 0 200px
      fixed;
    background-size: cover;
    opacity: 0.03;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    position: absolute;
    z-index: -1;
  }
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  float: left;
  z-index: 999;
  padding-bottom: 20px;
  background: transparentize(white, 0.03);
  &.open {
    opacity: 1;
    display: block;
  }
  &.close {
    opacity: 0;
    display: none;
  }

  .search-contents {
    padding: 60px 0 0 0;
    display: flex;
    div.app-filter-options {
      flex: 1.3;
      ul {
        width: 80%;
        margin-top: 50px;
        li {
          a {
            color: #555;
            transition: none;
            border-radius: 4px;
            i {
              display: inline-block;
              width: 20px;
            }
            padding: 10px;
            display: block;
            margin-bottom: 2px;
            &.active {
              background-color: $color-primary;
              color: white;
              font-weight: bold;
              &:after {
                display: block;
              }
            }
            &:hover {
              background-color: $color-primary;
              color: white;
              &:after {
                display: block;
              }
            }
            position: relative;
            &:after {
              position: absolute;
              top: 5px;
              right: -12px;
              width: 28px;
              height: 28px;
              background: $color-primary;
              content: '';
              display: none;
              -webkit-transform: rotate(45deg);
              -khtml-transform: rotate(45deg);
              -moz-transform: rotate(45deg);
              -ms-transform: rotate(45deg);
              -o-transform: rotate(45deg);
              transform: rotate(45deg);
              zoom: 1;
              -webkit-border-radius: 3px;
              -khtml-border-radius: 3px;
              -moz-border-radius: 3px;
              -ms-border-radius: 3px;
              -o-border-radius: 3px;
              border-radius: 3px;
            }
          }
        }
      }
    }
    div.search-input {
      flex: 3;
      .search-box-input {
        border: 1px solid #ccc;
        border-radius: 3px;
        min-height: 50px;
      }
      .search-info {
        color: #999;
        font-size: 12px;
        strong {
          font-weight: bold;
          color: $color-primary;
        }
        span {
          color: #555;
        }
      }
      div.result-display {
        min-height: 500px;
        position: relative;
        .filter-content {
          width: 100%;
          height: 100%;
        }
        .filtered-event-lists {
          flex: 1;
          margin-top: 30px;
          padding-left: 30px;
          p {
            font-size: 13px;
            line-height: 14px;
          }
          h2 {
            font-size: 20px;
          }
          .event-details {
            padding: 15px;
          }
          h3.event-cost {
            line-height: 40px;
            font-size: 16px;
          }
        }
      }
      div.category-filter {
        padding: 20px 0 0 0;
        a {
          background-color: #ccc;
          border: 1px solid #c3c3c3;
          padding: 5px 10px;
          color: white;
          line-height: 10px;
          font-size: 12px;
          margin-right: 10px;
          border-radius: 10px;
          margin-bottom: 5px;
          display: inline-block;
          &.active {
            background-color: $color-primary;
            border-color: $color-primary-active;
          }
        }
      }
    }
    div.filter-options {
      flex: 1;
    }
  }
  .for_results {
    overflow: hidden;
    overflow-y: auto;
    margin-top: 200px;
  }
}

.search-result-display {
  margin-top: 250px;
  overflow: hidden;
  overflow-y: auto;
  .search-contents {
    padding: 0 !important;
  }
}

.overflow-wrapper {
  background-color: green;
  position: relative;
  overflow: hidden;
  overflow-y: auto;
}

.fixed_filter_option {
  min-height: 250px;
  background-color: white;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 999999;
}

.payment-methods {
  cursor: pointer;
  width: 80%;
  margin: auto;
  .p-methods-con {
    display: inline-block;
    background: white;
    &.selected {
      border-bottom: 2px solid $color-primary;
    }
    padding: 6px 8px;
    margin-right: 15px;
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.3);
    .p-method {
      background: white;
      padding: 2px;
      border: 1px solid #999;
      display: inline-block;
      height: 55px;
      img {
        height: 50px;
      }
    }
  }
  .previous-btn {
    margin-left: 10px;
  }

  // Tablet styles
  @media (max-width: 992px) {
    width: 90%;
    
    .p-methods-con {
      padding: 5px 6px;
      margin-right: 12px;
      
      .p-method {
        height: 50px;
        
        img {
          height: 45px;
        }
      }
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    width: 95%;
    text-align: center;
    
    .p-methods-con {
      display: block;
      margin: 0 auto 15px auto;
      padding: 8px 10px;
      width: 200px;
      
      .p-method {
        height: 45px;
        width: 100%;
        text-align: center;
        
        img {
          height: 40px;
          max-width: 100%;
        }
      }
    }
    
    .previous-btn {
      margin-left: 0;
      margin-top: 10px;
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    width: 100%;
    padding: 0 10px;
    
    .p-methods-con {
      width: 180px;
      padding: 6px 8px;
      
      .p-method {
        height: 40px;
        
        img {
          height: 35px;
        }
      }
    }
  }
}

.payment_success {
  align-items: center;
  text-align: center;
  h1 {
    color: $color-primary;
    font-weight: 300;
    margin-bottom: 20px;
  }

  // Tablet styles
  @media (max-width: 992px) {
    h1 {
      font-size: 26px;
      margin-bottom: 15px;
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    h1 {
      font-size: 22px;
      margin-bottom: 12px;
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    h1 {
      font-size: 18px;
      margin-bottom: 10px;
    }
  }
}

.artist_banner {
  margin-top: -10px;
  position: relative;
  width: 100%;
  background-color: #444;
  .detail-main-img-options {
    min-height: 150px;
  }
  .artist_intro {
    height: 100px !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    .artist_title {
      display: flex;
      .artist_name {
        align-self: flex-end;
        padding-left: 20px;
        .extra_info {
          margin-top: 30px;
          margin-bottom: -60px;
        }
        h2 {
          font-size: 30px;
          font-weight: bold;
          color: white;
        }
        p {
          color: #eee;
          font-weight: bold;
          font-size: 14px;
        }
      }
      .artist_image {
        height: 200px;
        width: 200px;
        // background-color: $color-primary;
        overflow: hidden;
        border-radius: 50%;
        margin-top: 30px;
        border: 5px solid white;
        box-shadow: 2px 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: -100px;
        img {
          // width: 120%;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}

.artist_tab {
  .tab_item {
    min-height: 100px;
    display: flex;
    h3 {
      cursor: pointer;
      align-self: flex-end;
      padding: 10px 20px 5px 5px;
      font-weight: bold;
      color: #666;
      font-size: 22px;
      border-bottom: 5px solid $color-primary;
    }
  }

  width: 100%;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  background-color: white;
}

.artist_details {
  padding: 50px 0;
  font-size: 15px;
  color: #666;
  .details {
    background-color: white;
    padding: 20px 10px;
    height: auto;
  }
  line-height: 26px;
  position: relative;
  // z-index: -1;
  background-color: #f9f9f9;
  .details {
    min-height: 400px;
  }
  .artist_relation {
    h3 {
      cursor: pointer;
      font-size: 24px;
      font-weight: bold;
      color: #666;
      padding-bottom: 10px;
      border-bottom: 1px solid #ccc;
    }
  }
}

.evt_list_view {
  position: relative;
  z-index: 1;
  display: flex;
  margin: 10px 0;
  border-radius: 5px;
  overflow: hidden;
  .image-con {
    width: 30%;
    margin-right: 10px;
  }
  .event_det {
    align-self: center;
    margin-top: 0;
    h3 {
      border-bottom: none;
      padding: 0;
      font-size: 17px;
      margin-top: 0;
      font-weight: bold;
    }
    p {
      font-size: 12px;
      margin-top: 4px;
      line-height: 10px;
    }
  }
}

.temporary_post_alert {
  position: fixed;
  bottom: 0;
  padding: 10px;
  width: 100%;
  background-color: $color-error;
  color: white;
}

.card_horizontal {
  margin: 0 0 15px 0;
  padding: 18px 15px;
  box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.05);
  background-color: white;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  h2 {
    color: $color-primary;
    margin-bottom: 5px;
  }
  .content {
    margin: 0 !important;
    color: #ccc !important;
  }
  p {
    margin-top: 15px !important;
    font-size: 16px !important;
    line-height: 1.4 !important;
    margin-bottom: 0 !important;
  }
  .image_container {
    margin-right: 4px;
  }
}

.step_progress_complete {
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  h1 {
    font-weight: bold !important;
  }
  p {
    font-size: 19px;
    color: #999;
    max-width: 900px;
    margin-top: 20px;
  }
  .success_icon {
    margin-top: 80px;
    background-color: $color-primary;
    color: white !important;
    height: 180px;
    width: 180px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 70px;
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
    border-radius: 50%;
  }
  .preview_act_buton {
    padding: 10px;
    margin-top: 100px;
    min-width: 400px;
    max-width: 800px;
    // background-color: red;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.success_svg {
  opacity: 0.8;
  min-width: 350px;
  margin-top: 60px;
}

.confirmation-app-modal {
  margin-top: 20px;
  margin-bottom: 10px;
  .alert-modal-exclam {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    h2 {
      font-weight: 600;
      font-size: 24px;
      text-align: center;
    }
    p {
      margin-top: 15px;
      text-align: center;
      width: 80%;
      &:nth-child(2n) {
        font-size: 14px;
        color: #888;
      }
    }
    img {
      height: 100px;
    }
  }
  .confirmation-action {
    display: flex;
    div {
      padding: 10px;
      flex: 1;
    }
  }
}

.image-crop-modal {
  .image-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}


.amount-form {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;
  
  label {
    font-size: 16px;
  }
  
  input {
    padding: 8px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 5px;
  }
  
  button {
    padding: 10px;
    background-color: $color-primary;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    
    &:hover {
      background-color: $color-primary;
    }
  }
}

.map-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 800px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 0 auto;

  .map-info {
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid #eaeaea;

    h4 {
      margin: 0 0 12px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .address-details {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }

  .address-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;

    span {
      margin-left: 4px;
    }
  }
}

.qa-container {
  .back-button {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    cursor: pointer;
    gap: 8px;
    color: #c71782;
    transition: color 0.2s ease-in-out;
  }

  .back-button:hover {
    color: #a0146a;
  }

  h2 {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }

  .qa-content {
    .question {
      font-size: 20px;
      font-weight: 600;
      color: #c71782;
    }

    .answer {
      font-size: 16px;
      line-height: 1.6;
      text-align: left;
      color: #333;
    }
  }

  .no-data {
    font-size: 16px;
    color: #777;
    padding: 10px;
  }
}

.subscribe-to-news {
  background: #14181d;
  padding: 40px 30px;
  color: white;

  .subscribe-us-text {
    margin-bottom: 25px;
    h2 {
      font-size: 28px;
      font-weight: 600;
      color: white;
      margin: 0;
    }
  }

  .subscribe-box-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
    
    .newsletter-form {
      flex: 1;
      max-width: 500px;
      display: flex;
      gap: 15px;
    }

    .social-button-container {
      .social-us ul {
        display: flex;
        gap: 15px;
        margin: 0;
        padding: 0;
        
        li {
          list-style: none;
          
          a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            color: white;
            transition: all 0.3s ease;
            
            &:hover {
              background: $color-primary;
              transform: translateY(-2px);
            }
            
            i {
              font-size: 18px;
            }
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 30px 20px;
    
    .subscribe-box-container {
      flex-direction: column;
      align-items: stretch;
      gap: 25px;
      
      .newsletter-form {
        max-width: 100%;
      }
      
      .social-button-container {
        .social-us ul {
          justify-content: center;
        }
      }
    }
  }
}

.footer-body {
  background: #09101c;
  padding: 60px 0 40px;
  
  h2 {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
  }
  
  p {
    color: rgba(255,255,255,0.7);
    font-size: 14px;
    line-height: 1.8;
    margin-bottom: 15px;
  }
  
  a {
    color: rgba(255,255,255,0.7);
    transition: all 0.3s ease;
    
    &:hover {
      color: white;
      text-decoration: none;
    }
  }

  .logo-image {
    height: 35px;
    opacity: 0.9;
    transition: opacity 0.3s ease;
    
    &:hover {
      opacity: 1;
    }
  }

  @media (max-width: 992px) {
    padding: 40px 0 30px;
    
    .col-lg-4, .col-lg-5, .col-lg-3 {
      margin-bottom: 30px;
    }
  }

  @media (max-width: 576px) {
    padding: 30px 0 20px;
    
    h2 {
      font-size: 16px;
      margin-bottom: 15px;
    }
    
    p {
      font-size: 13px;
    }
    
    .logo-image {
      height: 30px;
    }
  }
}

.footer-copy-right {
  background: #060a12;
  padding: 20px 0;
  
  p {
    text-align: center;
    color: rgba(255,255,255,0.5);
    font-size: 13px;
    margin: 0;
  }

  @media (max-width: 576px) {
    padding: 15px 0;
    
    p {
      font-size: 12px;
    }
  }
}

.category-container {
  background: linear-gradient(to right, rgba(255,255,255,0.95), rgba(255,255,255,0.95));
  backdrop-filter: blur(10px);
  padding: 20px 0;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  position: sticky;
  top: 0;
  z-index: 100;
  border-radius: 12px;
  
  .category-select-filter {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;

    .category-group {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      align-items: center;
      justify-content: center;

      .category-item {
        display: inline-flex;
        align-items: center;
        padding: 12px 24px;
        background: white;
        border-radius: 12px;
        color: #555;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 1px solid #eee;
        box-shadow: 0 2px 6px rgba(0,0,0,0.02);
        
        i {
          margin-right: 8px;
          font-size: 16px;
          transition: transform 0.3s ease;
        }

        &:hover {
          background: #f8f9fa;
          color: $color-primary;
          border-color: rgba($color-primary, 0.3);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.05);

          i {
            transform: scale(1.1);
          }
        }

        &.active {
          background: $color-primary;
          color: white;
          border-color: $color-primary;
          box-shadow: 0 4px 12px rgba($color-primary, 0.2);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 15px rgba($color-primary, 0.25);
          }
        }

        &.primary {
          background: #f8f9fa;
          border-color: #e9ecef;
          font-weight: 600;
          color: $color-primary;
          
          &:not(.active):hover {
            background: #f1f3f5;
            border-color: #dee2e6;
          }
        }

        &.more-btn {
          background: #f8f9fa;
          border-color: #e9ecef;
          
          &:hover {
            background: #f1f3f5;
            border-color: #dee2e6;
          }
        }
      }
    }
  }

  // Tablet view
  @media (max-width: 992px) {
    padding: 15px 0;
    
    .category-select-filter {
      .category-group {
        gap: 10px;
        
        .category-item {
          padding: 10px 20px;
          font-size: 13px;
          
          i {
            font-size: 15px;
          }
        }
      }
    }
  }

  // Mobile view
  @media (max-width: 768px) {
    padding: 12px 0;
    position: sticky;
    top: 0;
    z-index: 100;
    
    .category-select-filter {
      padding: 0 15px;
      
      .category-group {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
        padding-bottom: 5px;
        justify-content: flex-start;

        &::-webkit-scrollbar {
          display: none;
        }
        
        .category-item {
          padding: 12px 20px;
          font-size: 14px;
          flex-shrink: 0;
          white-space: nowrap;
          
          i {
            font-size: 16px;
          }
        }
      }
    }
  }
}

// Dropdown styles
.ant-dropdown {
  .ant-dropdown-menu {
    padding: 8px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);

    .ant-dropdown-menu-item {
      padding: 10px 16px;
      border-radius: 8px;
      margin: 2px 0;
      
      .ant-checkbox-wrapper {
        display: flex;
        align-items: center;
        
        i {
          margin-right: 8px;
          color: $color-primary;
          font-size: 15px;
        }
      }

      &:hover {
        background: rgba($color-primary, 0.05);
      }
    }
  }
}

.search-filter-container {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  margin: 24px 0;
  margin-bottom: 10px !important;

  .search-filter-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;

    // Search Box Styles
    .search-box {
      width: 100%;

      .search-input-wrapper {
        position: relative;
        width: 100%;

        .search-icon {
          position: absolute;
          left: 16px;
          top: 50%;
          transform: translateY(-50%);
          color: #666;
          font-size: 18px;
        }

        .search-input {
          width: 100%;
          height: 52px;
          padding: 0 16px 0 48px;
          border: 2px solid #eee;
          border-radius: 12px;
          font-size: 16px;
          transition: all 0.3s ease;

          &:focus {
            outline: none;
            border-color: $color-primary;
            box-shadow: 0 0 0 4px rgba($color-primary, 0.1);
          }

          &::placeholder {
            color: #999;
          }
        }
      }
    }

    // Filter Options
    .filter-options {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      align-items: flex-start;

      .filter-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      .filter-btn {
        height: 44px;
        padding: 0 20px;
        border: 2px solid #eee;
        border-radius: 10px;
        background: white;
        color: #555;
        font-size: 15px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        cursor: pointer;

        i {
          font-size: 16px;
          color: #666;
        }

        &:hover {
          border-color: $color-primary;
          color: $color-primary;

          i {
            color: $color-primary;
          }
        }

        &.active {
          background: $color-primary;
          border-color: $color-primary;
          color: white;

          i {
            color: white;
          }
        }
      }

      // Price Range Filter
      .price-range-filter {
        flex: 1;
        min-width: 250px;
        padding: 0 12px;

        // :global {
        //   .ant-slider {
        //     margin: 8px 0;

        //     .ant-slider-rail {
        //       background-color: #eee;
        //     }

        //     .ant-slider-track {
        //       background-color: $color-primary;
        //     }

        //     .ant-slider-handle {
        //       border-color: $color-primary;
              
        //       &:focus {
        //         box-shadow: 0 0 0 5px rgba($color-primary, 0.12);
        //       }
        //     }

        //     .ant-slider-mark-text {
        //       color: #666;
        //       font-size: 13px;
        //     }
        //   }
        // }
      }
    }
  }

  // Responsive Styles
  @media (max-width: 992px) {
    padding: 20px;

    .search-filter-wrapper {
      gap: 20px;

      .filter-options {
        .filter-btn {
          height: 40px;
          padding: 0 16px;
          font-size: 14px;
        }

        .price-range-filter {
          width: 100%;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .search-filter-wrapper {
      gap: 16px;

      .search-box {
        .search-input-wrapper {
          .search-input {
            height: 48px;
            font-size: 15px;
          }
        }
      }

      .filter-options {
        flex-direction: column;
        align-items: stretch;
        
        .filter-btn {
          width: 100%;
          justify-content: center;
        }

        .price-range-filter {
          padding: 0;
        }
      }
    }
  }

  @media (max-width: 576px) {
    padding: 12px;
    margin: 0 auto;

    .search-filter-wrapper {
      gap: 12px;

      .search-box {
        .search-input-wrapper {
          .search-input {
            height: 44px;
            font-size: 14px;
          }
        }
      }
    }
  }
}

.subscribe-section {
  background: linear-gradient(135deg, #1a1f25 0%, #2c3037 100%);
  padding: 80px 0;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/pattern-bg.png') repeat;
    opacity: 0.03;
    pointer-events: none;
  }

  .container {
    position: relative;
    z-index: 1;
  }

  .social-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    gap: 40px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 20px;
      margin-top: 20px;
    }
  }

  .subscribe-us-text {
    h2 {
      font-size: 32px;
      font-weight: 700;
      background: linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0;
      line-height: 1.2;
      margin-bottom: 20px;
    }
  }

  .newsletter-form {
    .input-group {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 18px;  
      transition: all 0.3s ease;
      height: 50px;

      &:focus-within {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      .input-group-prepend {
        padding: 0 18px;  // Increased from 16px
        
        i {
          color: $color-primary;
          font-size: 20px;
        }
      }

      input {
        flex: 1;
        background: transparent;
        border: none;
        color: white;
        font-size: 16px;
        padding: 20px 10px;  
        width: 100%;
        height: 32px; 

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          outline: none;
        }
      }

      .subscribe-btn {
        background: $color-primary;
        color: white;
        border: none;
        padding: 20px 32px; 
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-left: 10px;

        &:hover {
          background: lighten($color-primary, 5%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba($color-primary, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  .social-button-container {
    .social-us {
      ul {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin: 0;
        padding: 0;
        list-style: none;

        li {
          a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 44px;
            height: 44px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: white;
            font-size: 18px;
            transition: all 0.3s ease;

            &:hover {
              background: $color-primary;
              border-color: $color-primary;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba($color-primary, 0.3);
            }

            &:active {
              transform: translateY(0);
            }
          }
        }
      }
    }
  }

  @media (max-width: 1200px) {
    padding: 60px 0;

    .row {
      grid-template-columns: 1fr 2fr;
      
      .social-button-container {
        grid-column: 1 / -1;
        
        .social-us ul {
          justify-content: center;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 50px 0;

    .row {
      grid-template-columns: 1fr;
      gap: 30px;
      text-align: center;
    }

    .subscribe-us-text h2 {
      font-size: 28px;  
      text-align: center;
    }

    .newsletter-form .input-group {
      flex-direction: column;
      gap: 12px;
      padding: 12px;

      .input-group-prepend {
        padding: 0;
      }

      input {
        text-align: center;
        padding: 12px;
      }

      .subscribe-btn {
        width: 100%;
        margin-left: 0;
      }
    }
  }

  @media (max-width: 480px) {
    padding: 40px 0;

    .subscribe-us-text h2 {
      font-size: 24px; 
    }

    .social-button-container .social-us ul {
      gap: 10px;

      li a {
        width: 40px;
        height: 40px;
        font-size: 16px;
      }
    }
  }
}

@media all and (max-width: 768px) {
  .tab-details {
    .detail-panel {
      width: 100%;
    }
  }
  .detail-main-img-options .event-brief-tab .tab-content .tab-pane {
    padding-right: 0;
  }
}

// Event Detail Page Responsive Styles
@media all and (max-width: 1200px) {
  .detail-main-img-options {
    .main-image {
      .d-flex {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
      }
      
      img {
        width: 100%;
        height: auto;
        max-height: 400px;
        object-fit: cover;
      }
    }
    
    .event-options {
      .options {
        .opt {
          h2 {
            font-size: 20px;
            padding-left: 15px;
            
            span {
              font-size: 10px;
            }
          }
        }
      }
    }
    
    .evt-detail-info {
      h1 {
        font-size: 22px;
        line-height: 28px;
        padding-right: 15px;
      }
      
      .content-padding {
        padding: 20px 15px;
      }
      
      .event-social-share {
        margin-top: 30px;
        margin-bottom: 30px;
      }
    }
  }
}

@media all and (max-width: 992px) {
  .detail-main-img-options {
    .row {
      flex-direction: column;
    }
    
    .main-image {
      order: 1;
      margin-bottom: 20px;
      
      img {
        border-radius: 8px;
      }
    }
    
    .evt-detail-info {
      order: 2;
      border-left: none;
      border-top: 5px solid $color-primary;
      margin-top: 0;
      
      .content-padding {
        padding: 25px 20px;
      }
      
      h1 {
        font-size: 24px;
        line-height: 30px;
        padding-right: 0;
        margin-bottom: 20px;
      }
      
      h2 {
        font-size: 16px;
      }
      
      .evt-attraction {
        margin-top: 25px;
        
        h2 {
          font-size: 18px;
          margin-bottom: 15px;
        }
      }
      
      .event-social-share {
        margin-top: 30px;
        text-align: center;
        
        ul {
          display: flex;
          justify-content: center;
          gap: 15px;
          
          li {
            margin: 0;
            float: none;
          }
        }
      }
    }
    
    .event-options {
      .row {
        flex-direction: column;
        gap: 20px;
      }
      
      // .options {
      //   flex-direction: column;
      //   min-height: auto;
        
      //   .opt {
      //     width: 100%;
      //     text-align: center;
      //     padding: 20px 15px;
      //     border-radius: 8px;
      //     margin-bottom: 10px;
          
      //     h2 {
      //       text-align: center;
      //       padding-left: 0;
      //       font-size: 18px;
            
      //       span {
      //         font-size: 12px;
      //       }
      //     }
          
      //     a.like-button {
      //       font-size: 24px;
      //       padding: 15px 0;
      //     }
          
      //     p {
      //       margin: 10px 0 0 0;
      //       font-size: 13px;
      //     }
      //   }
        
      //   .opt-one {
      //     background: #fefefe;
      //   }
        
      //   .opt-two {
      //     background: #f9f9f9;
      //   }
        
      //   .opt-three {
      //     background: #f5f5f5;
      //     border-right: none;
      //     border-bottom: 2px solid $color-primary;
      //   }
        
      //   .opt-four {
      //     background: #f6f6f6;
      //     padding: 20px 15px;
          
      //     a {
      //       display: inline-flex;
      //       align-items: center;
      //       gap: 8px;
      //       padding: 10px 20px;
      //       border-radius: 25px;
      //       background: rgba($color-primary, 0.1);
      //       color: $color-primary;
      //       transition: all 0.3s ease;
            
      //       &:hover {
      //         background: $color-primary;
      //         color: white;
      //         text-decoration: none;
      //       }
      //     }
      //   }
      // }
      
      .book-now {
        order: -1;
        
        a {
          width: 100%;
          text-align: center;
          padding: 15px 30px;
          font-size: 18px;
          border-radius: 8px;
        }
      }
    }
  }
}

@media all and (max-width: 768px) {
  .detail-main-img-options {
    margin: 10px auto;
    max-width: 95%;
    
    .main-image {
      img {
        max-height: 300px;
      }
      
      .bread-crumb {
        li {
          font-size: 12px;
          margin: 3px 5px 10px 0;
          
          a {
            font-size: 12px;
          }
        }
      }
    }
    
    .evt-detail-info {
      .content-padding {
        padding: 20px 15px;
      }
      
      h1 {
        font-size: 20px;
        line-height: 26px;
        margin-bottom: 15px;
      }
      
      h2 {
        font-size: 16px;
        margin-bottom: 15px;
      }
      
      p {
        font-size: 13px;
        line-height: 22px;
        width: 100%;
        margin: 5px 0 20px 0;
      }
      
      .evt-attraction {
        margin-top: 20px;
        
        h2 {
          font-size: 16px;
          margin-bottom: 12px;
        }
        
        ul li {
          font-size: 13px;
          margin: 8px 0;
        }
      }
      
      .event-social-share {
        margin-top: 30px;
        margin-bottom: 30px;
        
        ul li a {
          height: 30px;
          width: 30px;
          
          img {
            height: 30px;
          }
        }
      }
    }
    
    .event-options {
      .options {
        .opt {
          padding: 15px 10px;
          
          h2 {
            font-size: 16px;
            
            span {
              font-size: 11px;
            }
          }
          
          a.like-button {
            font-size: 20px;
            padding: 10px 0;
          }
          
          p {
            font-size: 12px;
          }
        }
        
        .opt-four {
          padding: 15px 10px;
          
          a {
            padding: 8px 16px;
            font-size: 14px;
          }
        }
      }
      
      .book-now {
        a {
          padding: 12px 25px;
          font-size: 16px;
        }
      }
    }
  }
  
  .event-brief-tab {
    margin-top: 30px;
    
    ul.nav-tabs {
      li {
        a.nav-link {
          font-size: 13px;
          width: auto;
          min-width: 80px;
          height: 40px;
          line-height: 40px;
          padding: 0 10px;
        }
      }
    }
    
    .tab-content {
      padding: 20px 0;
      min-height: 300px;
      
      h1 {
        font-size: 20px;
        
        & + p {
          width: 100%;
          font-size: 13px;
        }
      }
      
      .activity-list {
        h3 {
          font-size: 16px;
        }
        
        p {
          width: 100%;
          font-size: 13px;
        }
      }
      
      .event-gallery {
        img {
          width: 120px;
          height: 120px;
          margin-right: 8px;
          margin-bottom: 8px;
        }
      }
      
      .tab-pane {
        padding-right: 0;
      }
    }
  }
}

@media all and (max-width: 576px) {
  .detail-main-img-options {
    margin: 5px auto;
    max-width: 98%;
    
    .main-image {
      img {
        max-height: 250px;
      }
      
      .d-flex {
        gap: 8px;
      }
    }
    
    .evt-detail-info {
      .content-padding {
        padding: 15px 12px;
      }
      
      h1 {
        font-size: 18px;
        line-height: 24px;
        margin-bottom: 12px;
      }
      
      h2 {
        font-size: 15px;
        margin-bottom: 12px;
      }
      
      p {
        font-size: 12px;
        line-height: 20px;
        margin: 5px 0 15px 0;
      }
      
      .evt-attraction {
        margin-top: 15px;
        
        h2 {
          font-size: 15px;
          margin-bottom: 10px;
        }
        
        ul li {
          font-size: 12px;
          margin: 6px 0;
        }
      }
      
      .event-social-share {
        margin-top: 25px;
        margin-bottom: 25px;
        
        ul {
          gap: 12px;
          
          li a {
            height: 28px;
            width: 28px;
            
            img {
              height: 28px;
            }
          }
        }
      }
    }
    
    .event-options {
      .options {
        .opt {
          padding: 12px 8px;
          margin-bottom: 8px;
          
          h2 {
            font-size: 14px;
            
            span {
              font-size: 10px;
            }
          }
          
          a.like-button {
            font-size: 18px;
            padding: 8px 0;
          }
          
          p {
            font-size: 11px;
          }
        }
        
        .opt-four {
          padding: 12px 8px;
          
          a {
            padding: 6px 12px;
            font-size: 13px;
          }
        }
      }
      
      .book-now {
        a {
          padding: 10px 20px;
          font-size: 15px;
        }
      }
    }
  }
  
  .event-brief-tab {
    margin-top: 25px;
    
    ul.nav-tabs {
      li {
        a.nav-link {
          font-size: 12px;
          min-width: 70px;
          height: 35px;
          line-height: 35px;
          padding: 0 8px;
        }
      }
    }
    
    .tab-content {
      padding: 15px 0;
      min-height: 250px;
      
      h1 {
        font-size: 18px;
        
        & + p {
          font-size: 12px;
        }
      }
      
      .activity-list {
        h3 {
          font-size: 15px;
        }
        
        p {
          font-size: 12px;
        }
      }
      
      .event-gallery {
        img {
          width: 100px;
          height: 100px;
          margin-right: 6px;
          margin-bottom: 6px;
        }
      }
    }
  }
}

@media all and (max-width: 480px) {
  .detail-main-img-options {
    .main-image {
      img {
        max-height: 200px;
      }
    }
    
    .evt-detail-info {
      .content-padding {
        padding: 12px 10px;
      }
      
      h1 {
        font-size: 16px;
        line-height: 22px;
      }
      
      h2 {
        font-size: 14px;
      }
      
      p {
        font-size: 11px;
        line-height: 18px;
      }
    }
    
    .event-options {
      .options {
        .opt {
          padding: 10px 6px;
          
          h2 {
            font-size: 13px;
            
            span {
              font-size: 9px;
            }
          }
        }
        
        .opt-four {
          a {
            padding: 5px 10px;
            font-size: 12px;
          }
        }
      }
      
      .book-now {
        a {
          padding: 8px 16px;
          font-size: 14px;
        }
      }
    }
  }
  
  .event-brief-tab {
    ul.nav-tabs {
      li {
        a.nav-link {
          font-size: 11px;
          min-width: 60px;
          height: 32px;
          line-height: 32px;
          padding: 0 6px;
        }
      }
    }
    
    .tab-content {
      h1 {
        font-size: 16px;
      }
      
      .activity-list {
        h3 {
          font-size: 14px;
        }
        
        p {
          font-size: 11px;
        }
      }
      
      .event-gallery {
        img {
          width: 80px;
          height: 80px;
        }
      }
    }
  }
}

// Additional Event Detail Page Component Responsive Styles
@media all and (max-width: 992px) {
  .info-card {
    flex-direction: column;
    gap: 15px;
    
    .info-card-image {
      width: 100%;
      height: 200px;
      border-radius: 8px;
    }
    
    .info-card-content {
      width: 100%;
      padding: 0;
      
      h3 {
        font-size: 18px;
        margin-bottom: 10px;
      }
      
      p {
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}

@media all and (max-width: 768px) {
  .info-card {
    .info-card-image {
      height: 180px;
    }
    
    .info-card-content {
      h3 {
        font-size: 16px;
      }
      
      p {
        font-size: 13px;
      }
    }
  }
  
  .expandable-content {
    .content {
      font-size: 13px;
      line-height: 1.5;
    }
    
    .expand-button {
      font-size: 13px;
      padding: 8px 16px;
    }
  }
  
  .social-share {
    ul {
      gap: 12px;
      
      li a {
        width: 32px;
        height: 32px;
        font-size: 14px;
      }
    }
  }
  
  .directions-map {
    .map-container {
      height: 250px;
    }
    
    .map-info {
      padding: 12px;
      
      h4 {
        font-size: 16px;
        margin-bottom: 10px;
      }
    }
    
    .address-details {
      grid-template-columns: 1fr;
      gap: 8px;
    }
    
    .address-item {
      font-size: 13px;
    }
  }
  
  .book-ticket-wizard {
    .wizard-header {
      h2 {
        font-size: 18px;
      }
      
      p {
        font-size: 13px;
      }
    }
    
    .wizard-content {
      padding: 15px;
    }
    
    .ticket-options {
      .ticket-option {
        padding: 12px;
        
        h3 {
          font-size: 16px;
        }
        
        .price {
          font-size: 18px;
        }
      }
    }
  }
  
  .like-button,
  .subscribe-button {
    .btn {
      padding: 8px 16px;
      font-size: 14px;
    }
    
    .count {
      font-size: 12px;
    }
  }
  
  .temporary_post_alert {
    padding: 8px;
    
    .container p {
      font-size: 13px;
      line-height: 1.4;
    }
  }
}

@media all and (max-width: 576px) {
  .info-card {
    .info-card-image {
      height: 150px;
    }
    
    .info-card-content {
      h3 {
        font-size: 15px;
      }
      
      p {
        font-size: 12px;
      }
    }
  }
  
  .expandable-content {
    .content {
      font-size: 12px;
    }
    
    .expand-button {
      font-size: 12px;
      padding: 6px 12px;
    }
  }
  
  .social-share {
    ul {
      gap: 10px;
      
      li a {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }
    }
  }
  
  .directions-map {
    .map-container {
      height: 200px;
    }
    
    .map-info {
      padding: 10px;
      
      h4 {
        font-size: 14px;
        margin-bottom: 8px;
      }
    }
    
    .address-item {
      font-size: 12px;
    }
  }
  
  .book-ticket-wizard {
    .wizard-header {
      h2 {
        font-size: 16px;
      }
      
      p {
        font-size: 12px;
      }
    }
    
    .wizard-content {
      padding: 12px;
    }
    
    .ticket-options {
      .ticket-option {
        padding: 10px;
        
        h3 {
          font-size: 14px;
        }
        
        .price {
          font-size: 16px;
        }
      }
    }
  }
  
  .like-button,
  .subscribe-button {
    .btn {
      padding: 6px 12px;
      font-size: 13px;
    }
    
    .count {
      font-size: 11px;
    }
  }
  
  .temporary_post_alert {
    padding: 6px;
    
    .container p {
      font-size: 12px;
    }
  }
}

@media all and (max-width: 480px) {
  .info-card {
    .info-card-image {
      height: 120px;
    }
    
    .info-card-content {
      h3 {
        font-size: 14px;
      }
      
      p {
        font-size: 11px;
      }
    }
  }
  
  .expandable-content {
    .content {
      font-size: 11px;
    }
    
    .expand-button {
      font-size: 11px;
      padding: 5px 10px;
    }
  }
  
  .social-share {
    ul {
      gap: 8px;
      
      li a {
        width: 24px;
        height: 24px;
        font-size: 10px;
      }
    }
  }
  
  .directions-map {
    .map-container {
      height: 180px;
    }
    
    .map-info {
      padding: 8px;
      
      h4 {
        font-size: 13px;
        margin-bottom: 6px;
      }
    }
    
    .address-item {
      font-size: 11px;
    }
  }
  
  .book-ticket-wizard {
    .wizard-header {
      h2 {
        font-size: 14px;
      }
      
      p {
        font-size: 11px;
      }
    }
    
    .wizard-content {
      padding: 10px;
    }
    
    .ticket-options {
      .ticket-option {
        padding: 8px;
        
        h3 {
          font-size: 13px;
        }
        
        .price {
          font-size: 14px;
        }
      }
    }
  }
  
  .like-button,
  .subscribe-button {
    .btn {
      padding: 5px 10px;
      font-size: 12px;
    }
    
    .count {
      font-size: 10px;
    }
  }
  
  .temporary_post_alert {
    padding: 5px;
    
    .container p {
      font-size: 11px;
    }
  }
}

// Compact Choose Events and Filter Section
.featured-events-list.section-container-box.container {
  padding: 20px 0 10px 0 !important;
  margin-bottom: 0 !important;
  .container-fluid-navigation {
    h1.app-title-primary {
      font-size: 28px !important;
      margin-bottom: 8px !important;
    }
    p {
      font-size: 13px !important;
      margin-bottom: 10px !important;
    }
  }
}

.search-filter-container {
  padding: 12px !important;
  margin: 10px 0 !important;
  border-radius: 10px !important;
  .search-filter-wrapper {
    gap: 10px !important;
    .search-box .search-input-wrapper .search-input {
      height: 36px !important;
      font-size: 14px !important;
      padding: 0 10px 0 36px !important;
    }
    .filter-options {
      gap: 6px !important;
      .filter-btn {
        height: 32px !important;
        padding: 0 10px !important;
        font-size: 13px !important;
      }
      .price-range-filter {
        min-width: 120px !important;
        padding: 0 4px !important;
        .ant-slider {
          margin: 0 !important;
        }
      }
    }
  }
}

.category-select-filter {
  gap: 6px !important;
  padding: 10px 0 !important;
  margin-bottom: 10px !important;
  .category-item {
    padding: 6px 10px !important;
    font-size: 13px !important;
    height: 32px !important;
  }
}

// Force category filter to always be a horizontal scroll/slider, even on desktop
.category-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto !important;
  padding: 0 !important;
}

.category-select-filter {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto !important;
  padding: 0 !important;

   .category-group {
     display: flex !important;
     flex-wrap: nowrap !important;
     overflow-x: auto !important;
     -webkit-overflow-scrolling: touch;
     gap: 8px !important;
     padding-bottom: 8px !important;
     scrollbar-width: thin;
     &::-webkit-scrollbar { height: 6px; background: transparent; }
     &::-webkit-scrollbar-thumb { background: #eee; border-radius: 3px; }
     max-width: 100%;
     width: 100%;
     margin-left: 0 !important;
     margin-right: 0 !important;
     padding-left: 12px !important;
     padding-right: 12px !important;
   
     .category-item {
       flex: 0 0 auto;
       white-space: nowrap;
       min-width: 110px;
       max-width: 200px;
       text-overflow: ellipsis;
       overflow: hidden;
       height: 36px !important;
       padding: 8px 14px !important;
       font-size: 15px !important;
       border-radius: 20px;
       background: #fff;
       border: 1px solid #eee;
       box-shadow: 0 1px 2px rgba(0,0,0,0.02);
       transition: background 0.2s, color 0.2s, border 0.2s;
       &:hover, &.active {
         background: #f8f9fa;
         color: #c71782;
         border-color: #c71782;
       }
     }
   }
 }

.search-filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  background: #f8f8fa;
  border-radius: 14px;
  padding: 12px 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);

  .search-box {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 10px;
    padding: 0 12px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.03);
    flex: 2 1 420px;
    min-width: 220px;
    max-width: 600px;
    width: 100%;

    .search-icon {
      color: #bbb;
      font-size: 18px;
      margin-right: 8px;
    }
    .search-input {
      border: none;
      outline: none;
      background: transparent;
      font-size: 15px;
      width: 100%;
      padding: 10px 0;
    }
  }

  .filter-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #fff;
    border: 1.5px solid #eee;
    border-radius: 10px;
    padding: 8px 18px;
    font-size: 15px;
    color: #444;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 110px;
    max-width: 140px;
    font-weight: 500;
    justify-content: center;
  }

  .price-range-filter {
    min-width: 260px;
    max-width: 400px;
    width: 340px;
    margin-left: 8px;
    .ant-slider {
      margin: 0 8px;
    }
  }
}

@media (max-width: 900px) {
  .search-filter-row {
    flex-wrap: wrap;
    gap: 10px;
    .search-box {
      min-width: 120px;
      flex: 1 1 180px;
      max-width: 100%;
      width: 100%;
    }
    .price-range-filter {
      min-width: 120px;
      width: 100%;
      max-width: 100%;
    }
    .filter-btn {
      min-width: 90px;
      max-width: 100%;
      width: 100%;
    }
  }
}
@media (max-width: 600px) {
  .search-filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    padding: 10px 6px;
    .search-box, .filter-btn, .price-range-filter {
      width: 100%;
      min-width: 0;
      max-width: 100%;
    }
  }
}

// Event Buy Page Responsive Styles
.container-fluid-navigation {
  // Tablet styles
  @media (max-width: 992px) {
    padding: 0 15px;
  }

  // Mobile styles
  @media (max-width: 768px) {
    padding: 0 10px;
  }

  // Small mobile styles
  @media (max-width: 480px) {
    padding: 0 8px;
  }
}

// Form responsive styles for event buy page
.ant-form {
  // Tablet styles
  @media (max-width: 992px) {
    .ant-form-item {
      margin-bottom: 16px;
      
      .ant-form-item-label {
        padding-bottom: 4px;
        
        label {
          font-size: 14px;
        }
      }
      
      .ant-form-item-control {
        .ant-input {
          height: 40px;
          font-size: 14px;
        }
        
        .ant-input-affix-wrapper {
          height: 40px;
          
          .ant-input {
            height: 38px;
          }
        }
      }
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    .ant-form-item {
      margin-bottom: 12px;
      
      .ant-form-item-label {
        padding-bottom: 3px;
        
        label {
          font-size: 13px;
        }
      }
      
      .ant-form-item-control {
        .ant-input {
          height: 36px;
          font-size: 13px;
        }
        
        .ant-input-affix-wrapper {
          height: 36px;
          
          .ant-input {
            height: 34px;
          }
        }
      }
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    .ant-form-item {
      margin-bottom: 10px;
      
      .ant-form-item-label {
        padding-bottom: 2px;
        
        label {
          font-size: 12px;
        }
      }
      
      .ant-form-item-control {
        .ant-input {
          height: 32px;
          font-size: 12px;
        }
        
        .ant-input-affix-wrapper {
          height: 32px;
          
          .ant-input {
            height: 30px;
          }
        }
      }
    }
  }
}

// Button responsive styles
.ant-btn {
  // Tablet styles
  @media (max-width: 992px) {
    &.ant-btn-lg {
      height: 40px;
      padding: 0 16px;
      font-size: 14px;
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    &.ant-btn-lg {
      height: 36px;
      padding: 0 14px;
      font-size: 13px;
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    &.ant-btn-lg {
      height: 32px;
      padding: 0 12px;
      font-size: 12px;
    }
  }
}

// Table responsive styles
.ant-table {
  // Tablet styles
  @media (max-width: 992px) {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 8px 6px;
      font-size: 13px;
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 6px 4px;
      font-size: 12px;
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
      padding: 4px 3px;
      font-size: 11px;
    }
  }
}

// Steps component responsive styles
.ant-steps {
  // Tablet styles
  @media (max-width: 992px) {
    .ant-steps-item {
      .ant-steps-item-icon {
        width: 24px;
        height: 24px;
        font-size: 12px;
        line-height: 22px;
      }
      
      .ant-steps-item-title {
        font-size: 13px;
        padding-right: 8px;
      }
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    .ant-steps-item {
      .ant-steps-item-icon {
        width: 20px;
        height: 20px;
        font-size: 10px;
        line-height: 18px;
      }
      
      .ant-steps-item-title {
        font-size: 12px;
        padding-right: 6px;
      }
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    .ant-steps-item {
      .ant-steps-item-icon {
        width: 18px;
        height: 18px;
        font-size: 9px;
        line-height: 16px;
      }
      
      .ant-steps-item-title {
        font-size: 11px;
        padding-right: 4px;
      }
    }
  }
}

// Row and column responsive adjustments
.row {
  // Tablet styles
  @media (max-width: 992px) {
    margin-left: -10px;
    margin-right: -10px;
    
    .col-sm-9,
    .col-sm-3 {
      padding-left: 10px;
      padding-right: 10px;
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    margin-left: -8px;
    margin-right: -8px;
    
    .col-sm-9,
    .col-sm-3 {
      padding-left: 8px;
      padding-right: 8px;
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    margin-left: -6px;
    margin-right: -6px;
    
    .col-sm-9,
    .col-sm-3 {
      padding-left: 6px;
      padding-right: 6px;
    }
  }
}

// Modal responsive styles
.ant-modal {
  // Tablet styles
  @media (max-width: 992px) {
    .ant-modal-content {
      margin: 20px;
      
      .ant-modal-header {
        padding: 16px 20px;
        
        .ant-modal-title {
          font-size: 18px;
        }
      }
      
      .ant-modal-body {
        padding: 20px;
      }
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    .ant-modal-content {
      margin: 10px;
      
      .ant-modal-header {
        padding: 12px 16px;
        
        .ant-modal-title {
          font-size: 16px;
        }
      }
      
      .ant-modal-body {
        padding: 16px;
      }
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    .ant-modal-content {
      margin: 5px;
      
      .ant-modal-header {
        padding: 10px 12px;
        
        .ant-modal-title {
          font-size: 14px;
        }
      }
      
      .ant-modal-body {
        padding: 12px;
      }
    }
  }
}

// Login container responsive styles
.login-container {
  // Tablet styles
  @media (max-width: 992px) {
    padding: 20px;
    
    .login-input-box {
      padding: 20px;
      
      h1 {
        font-size: 32px;
      }
      
      p {
        font-size: 12px;
      }
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    padding: 15px;
    
    .login-input-box {
      padding: 15px;
      
      h1 {
        font-size: 28px;
      }
      
      p {
        font-size: 11px;
      }
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    padding: 10px;
    
    .login-input-box {
      padding: 10px;
      
      h1 {
        font-size: 24px;
      }
      
      p {
        font-size: 10px;
      }
    }
  }
}

// Expired info responsive styles
.expired-info {
  // Tablet styles
  @media (max-width: 992px) {
    h1 {
      font-size: 24px;
    }
    
    p {
      font-size: 13px;
    }
    
    h3 {
      font-size: 18px;
    }
  }

  // Mobile styles
  @media (max-width: 768px) {
    h1 {
      font-size: 20px;
    }
    
    p {
      font-size: 12px;
    }
    
    h3 {
      font-size: 16px;
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    h1 {
      font-size: 18px;
    }
    
    p {
      font-size: 11px;
    }
    
    h3 {
      font-size: 14px;
    }
  }
}

// Mobile styles
@media (max-width: 768px) and (min-width: 480px) {
  .ticket-summary-det {
    min-height: auto;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    
    .events-details-final {
      flex-direction: column;
      align-items: center;
      text-align: center;
      background: white;
      border-radius: 24px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
      padding: 0;
      margin-bottom: 24px;
      overflow: hidden;
      position: relative;
      
      .ticket-summary-img {
        padding: 0;
        text-align: center;
        position: relative;
        width: 100%;
        
        img {
          width: 100%;
          height: 240px;
          object-fit: cover;
          border-radius: 0;
          box-shadow: none;
          margin-bottom: 0;
        }
        
        a {
          position: absolute;
          top: 16px;
          right: 16px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 10px 16px;
          border-radius: 25px;
          font-size: 13px;
          font-weight: 600;
          color: #2c3e50;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
          text-decoration: none;
          border: 1px solid rgba(255, 255, 255, 0.3);
          transition: all 0.3s ease;
          
          i {
            margin-right: 8px;
            font-size: 16px;
            color: #3498db;
          }
          
          &:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
          }
        }
      }
      
      .summary-details {
        padding: 28px 24px;
        text-align: left;
        max-width: 100%;
        background: white;
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        
        p {
          margin: 0 0 12px 0;
          color: #7f8c8d;
          font-size: 15px;
          line-height: 1.6;
          font-weight: 500;
          
          span {
            color: #95a5a6;
            font-weight: 400;
          }
          a {
            color: #3498db;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
            
            &:hover {
              color: #2980b9;
              text-decoration: underline;
            }
          }
        }
        
        h1 {
          margin: 12px 0 16px 0;
          color: #2c3e50;
          font-size: 26px;
          font-weight: 700;
          line-height: 1.3;
          width: 100%;
          letter-spacing: -0.5px;
        }
        
        p:last-child {
          margin-top: 0;
          color: #7f8c8d;
          font-size: 14px;
          line-height: 1.6;
          width: 100%;
          font-weight: 400;
        }
      }
    }
    
    .ticket-card {
      background: white;
      border-radius: 24px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
      padding: 32px 24px;
      margin: 0 auto;
      max-width: 100%;
      position: relative;
      border: none;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      }
      
      h4 {
        margin: 0 0 24px 0;
        color: #2c3e50;
        font-size: 20px;
        font-weight: 700;
        text-align: center;
        padding: 0;
        position: relative;
        letter-spacing: -0.5px;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
          width: 50px;
          height: 3px;
          background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
          border-radius: 2px;
        }
      }
      
      .amount-summary {
        margin-top: 24px;
        
        .ant-table {
          border-radius: 16px;
          overflow: hidden;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          border: 1px solid #ecf0f1;
          
          .ant-table-thead > tr > th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 2px solid #e9ecef;
            font-weight: 600;
            color: #2c3e50;
            padding: 18px 16px;
            font-size: 15px;
            text-align: center;
            
            &:first-child {
              text-align: left;
            }
          }
          
          .ant-table-tbody > tr > td {
            padding: 16px;
            border-bottom: 1px solid #f1f3f4;
            font-size: 15px;
            color: #34495e;
            text-align: center;
            
            &:first-child {
              text-align: left;
              font-weight: 500;
            }
            
            &:last-child {
              font-weight: 600;
              color: #2c3e50;
            }
          }
          
          .ant-table-tbody > tr:last-child > td {
            border-bottom: none;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 700;
            color: #2c3e50;
          }
        }
      }
    }
    
    .ticket_expiry {
      text-align: center;
      padding: 24px 0 0 0;
      
      span {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
        padding: 12px 24px;
        border-radius: 30px;
        font-size: 14px;
        font-weight: 600;
        box-shadow: 0 8px 24px rgba(255, 107, 107, 0.4);
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
        
        &::before {
          content: '⏰';
          margin-right: 8px;
          font-size: 16px;
        }
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 12px 32px rgba(255, 107, 107, 0.5);
        }
      }
    }
  }

  // Small mobile styles
  @media (max-width: 480px) {
    .ticket-summary-det {
      min-height: auto;
      padding: 16px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      
      .events-details-final {
        flex-direction: column;
        align-items: center;
        text-align: center;
        background: white;
        border-radius: 20px;
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        padding: 0;
        margin-bottom: 20px;
        overflow: hidden;
        position: relative;
        
        .ticket-summary-img {
          padding: 0;
          text-align: center;
          position: relative;
          width: 100%;
          
          img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 0;
            box-shadow: none;
            margin-bottom: 0;
          }
          
          a {
            position: absolute;
            top: 12px;
            right: 12px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: #495057;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.2s ease;
            
            i {
              margin-right: 6px;
              font-size: 14px;
              color: #6c757d;
            }
            
            &:hover {
              background: rgba(255, 255, 255, 1);
              transform: translateY(-1px);
            }
          }
        }
        
        .summary-details {
          padding: 24px 20px;
          text-align: left;
          max-width: 100%;
          background: white;
          
          p {
            margin: 0 0 8px 0;
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
            font-weight: 400;
            
            span {
              color: #adb5bd;
              font-weight: 300;
            }
            a {
              color: $color-primary;
              text-decoration: none;
              font-weight: 500;
              
              &:hover {
                text-decoration: underline;
              }
            }
          }
          
          h1 {
            margin: 8px 0 12px 0;
            color: #212529;
            font-size: 22px;
            font-weight: 700;
            line-height: 1.3;
            width: 100%;
          }
          
          p:last-child {
            margin-top: 0;
            color: #6c757d;
            font-size: 13px;
            line-height: 1.5;
            width: 100%;
            font-weight: 400;
          }
        }
      }
      
      .ticket-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        padding: 28px 20px;
        margin: 0 auto;
        max-width: 100%;
        position: relative;
        border: none;
        overflow: hidden;
        
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        
        h4 {
          margin: 0 0 20px 0;
          color: #2c3e50;
          font-size: 18px;
          font-weight: 700;
          text-align: center;
          padding: 0;
          position: relative;
          
          &::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 3px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
          }
        }
        
        .amount-summary {
          margin-top: 20px;
          
          .ant-table {
            border-radius: 14px;
            overflow: hidden;
            box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
            border: 1px solid #ecf0f1;
            
            .ant-table-thead > tr > th {
              background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
              border-bottom: 2px solid #e9ecef;
              font-weight: 600;
              color: #2c3e50;
              padding: 14px 12px;
              font-size: 14px;
              text-align: center;
              
              &:first-child {
                text-align: left;
              }
            }
            
            .ant-table-tbody > tr > td {
              padding: 14px 12px;
              border-bottom: 1px solid #f1f3f4;
              font-size: 14px;
              color: #34495e;
              text-align: center;
              
              &:first-child {
                text-align: left;
                font-weight: 500;
              }
              
              &:last-child {
                font-weight: 600;
                color: #2c3e50;
              }
            }
            
            .ant-table-tbody > tr:last-child > td {
              border-bottom: none;
              background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
              font-weight: 700;
              color: #2c3e50;
            }
          }
        }
      }
      
      .ticket_expiry {
        text-align: center;
        padding: 20px 0 0 0;
        
        span {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
          color: white;
          padding: 10px 20px;
          border-radius: 25px;
          font-size: 13px;
          font-weight: 600;
          box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
          display: inline-flex;
          align-items: center;
          transition: all 0.3s ease;
          
          &::before {
            content: '⏰';
            margin-right: 6px;
            font-size: 14px;
          }
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 28px rgba(255, 107, 107, 0.5);
          }
        }
      }
    }
  }
}
