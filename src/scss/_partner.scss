.partner_container {
  padding-top: 20px;
  min-height: calc(100vh - 180px);
}
.partner_registration_box {
  width: 800px !important;
  max-width: 800px !important;
  h1 {
    + p {
      max-width: 560px;
    }
  }
  .partner_category_selector {
    display: flex;
    flex-wrap: wrap;
    
    li {
      max-width: 130px;
      display: flex;
      margin-right: 30px;
      margin-bottom: 30px;
      
      &.hotel {
        .img-holder {
          background: $color-primary
            url('https://images.unsplash.com/photo-1496417263034-38ec4f0b665a?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1651&q=20');
          background-size: cover;
          opacity: 0.7;
        }
      }
      &.vehicle {
        .img-holder {
          background: $color-primary
            url('https://images.unsplash.com/photo-1468689210283-44a1b8ba20ce?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1950&q=20');
          background-size: cover;
          opacity: 0.7;
        }
      }
      &.travel {
        .img-holder {
          background: $color-primary
            url('https://images.unsplash.com/photo-1571546010145-1d0ea1b8c6b6?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1650&q=20');
          background-size: cover;
          opacity: 0.7;
        }
      }
      &.venue {
        .img-holder {
          background: $color-primary
            url('https://images.unsplash.com/photo-1527261834078-9b37d35a4a32?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1650&q=20');
          background-size: cover;
          opacity: 0.7;
        }
      }
      
      a {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
        padding: 17px 15px;
        border-radius: 20px;
        border: 1px solid #e4e4e4;
        transition: 0.2s;
        
        .img-holder {
          background-image: url('https://images.unsplash.com/photo-1543351611-58f69d7c1781?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=200&q=20');
          background-size: cover;
          height: 60px;
          width: 100%;
          border-radius: 10px;
          overflow: hidden;
          margin-bottom: 15px;
          flex-shrink: 0;
        }
        
        .content-wrapper {
          display: flex;
          flex-direction: column;
          flex-grow: 1;
          
          h3 {
            color: $color-dark;
            font-size: 20px;
            margin-bottom: 6px;
          }
          
          p {
            line-height: 1.4;
            font-size: 12px;
            color: #999;
            flex-grow: 1;
            margin-bottom: 15px;
          }
          
          .footer_dec {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0;
            margin-top: auto;
            
            i {
              color: #c0c0c0;
            }
            
            .decorator {
              margin-top: 10px;
              height: 10px;
              width: 40px;
              background: #c0c0c0;
              border-radius: 20px;
            }
          }
        }
        
        &.active,
        &:hover {
          @include primary-color-rgba(0.04);
          box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.05);
          border: 1px solid $color-primary-light;
          
          .content-wrapper{
            .footer_dec {
              .decorator {
                background: $color-primary;
              }
              
              i {
                color: $color-primary;
              }
            }
          }
          .img-holder {
            opacity: 1;
          }
        }
      }
    }
  }
  @media (max-width: 768px) {
    width: 100% !important;
    max-width: 600px !important;
  }
}

.partner_info_form {
  .form-group {
    margin-bottom: 35px;
    input {
      font-weight: 700;
      font-size: 16px;
    }
  }
}

.partner_registration {
  position: fixed;
  right: 2%;
  bottom: 15%;
  z-index: 99;
  ul {
    li {
      a {
        border: 2px solid $color-primary;
        width: 30px;
        height: 30px;
        margin-top: 32px;
        border-radius: 50%;
        text-align: center;
        line-height: 25px;
        display: block;
        font-weight: bold;
        &.active {
          background: $color-primary;
          color: white;
        }
        &.inactive {
          border-color: $color-primary-light;
          color: $color-primary-light;
        }
        &.done {
          @include primary-color-rgba(0.06);
        }
      }
      position: relative;
      div.info_step {
        //transform: rotate(90deg);
        position: absolute;
        width: 400px;
        left: -420px;
        top: -5px;
        text-align: right;
        h4 {
          margin-top: 0;
          font-weight: 500;
          color: $color-primary;
          margin-bottom: 0;
          + p {
            margin-top: 0;
            color: #7d7d7d;
            // opacity: 0.7;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }
}

.company-info-form {
  background: #fff;
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  max-width: 1200px;
  margin: 0 auto;

  .form-header {
    margin-bottom: 2rem;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 1rem;

    h1 {
      font-size: 2rem;
      color: $color-primary;
      font-weight: 700;
      margin: 0;
    }
  }

  form {
    .row {
      margin-bottom: 1rem;
    }

    .logo-upload {
      margin-bottom: 2rem;

      label {
        display: block;
        margin-bottom: 1rem;
        color: #333;
        font-weight: 600;
        font-size: 1rem;
      }

      .company-logo {
        background-color: white;
        height: 110px;
        width: 110px;
        margin-bottom: 1.5rem;
        border-radius: 50%;
        overflow: hidden;
        border: 2px dashed $color-primary;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &.upload-trigger {
          cursor: pointer;

          span {
            color: $color-primary;
            font-size: 0.9rem;
            font-weight: 500;
          }

          &:hover {
            border-color: darken($color-primary, 10%);
            transform: translateY(-2px);
          }
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .image-preview-container {
        position: relative;
        display: inline-block;
      }

      .clear-icon {
        position: absolute;
        top: -10px;
        right: -10px;
        cursor: pointer;
        font-size: 22px;
        color: $color-primary;
        background-color: white;
        border-radius: 50%;
        transition: all 0.2s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);

        &:hover {
          transform: scale(1.15);
          color: darken($color-primary, 15%);
        }
      }
    }

    .input-container {
      display: flex;
      flex-direction: column;
      margin-bottom: 1.5rem;

      label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
        font-size: 1rem;
      }

      .error-message {
        font-size: 0.875rem;
        color: #e53935;
        margin-bottom: 0.5rem;
        font-weight: 500;
      }

      .tag-input {
        width: 400px !important;
      }

      // Input field styling
      // .ant-input,
      // .ant-input-affix-wrapper,
      // .ant-select-selector {
      //   border-radius: 6px;
      //   padding: 0.625rem 0.875rem;
      //   border: 1px solid #e0e0e0;
      //   transition: all 0.3s ease;
        
      //   &:hover {
      //     border-color: $color-primary;
      //   }
        
      //   &:focus, &.ant-input-focused {
      //     border-color: $color-primary;
      //     box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
      //     outline: none;
      //   }
      // }

      // TextArea input
      // textarea.ant-input {
      //   min-height: 120px;
      //   padding: 0.75rem;
      // }

      // Field info text
      .field-info {
        font-size: 0.8rem;
        color: #666;
        margin-top: 0.25rem;
        font-style: italic;
      }
    }

    .submit-btn {
      display: flex;
      justify-content: flex-start;
      margin-top: 2.5rem;

      button {
        background: $color-primary;
        border: none;
        padding: 0.75rem 2rem;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 6px;
        color: #fff;
        cursor: pointer;
        transition: all 0.3s ease;
        height: auto;
        box-shadow: 0 4px 6px rgba($color-primary, 0.25);

        &:hover {
          background: darken($color-primary, 10%);
          transform: translateY(-2px);
          box-shadow: 0 6px 8px rgba($color-primary, 0.3);
        }

        &:active {
          transform: translateY(1px);
          box-shadow: 0 2px 4px rgba($color-primary, 0.25);
        }

        &:disabled {
          background: #d0d0d0;
          cursor: not-allowed;
          box-shadow: none;
          transform: none;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 1.5rem;
    
    .form-header h1 {
      font-size: 1.5rem;
    }
    
    .logo-upload {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 1.5rem;
    }
    
    .submit-btn button {
      width: 100%;
    }
  }
}
