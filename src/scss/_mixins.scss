@mixin border-radius($radius) {
  -webkit-border-radius: $radius;
  -moz-border-radius: $radius;
  -ms-border-radius: $radius;
  border-radius: $radius;
}

// Text selection
@mixin text-selection($color-selection, $color-selection-bg) {
  ::-moz-selection { // sass-lint:disable-line no-vendor-prefixes
	background: $color-selection-bg;
	color: $color-selection;
	text-shadow: none;
  }

  ::selection {
	background: $color-selection-bg;
	color: $color-selection;
	text-shadow: none;
  }
}

@mixin primary-color-rgba($opacity:1) {
  background-color: rgba(168, 13, 93, $opacity);
}

@mixin primary-color-text-rgba($opacity:1) {
  color: rgba(168, 13, 93, $opacity);
}

@mixin secondary-color-rgba($opacity:1) {
  background-color: rgba(255, 166, 0, $opacity);
}
@mixin secondary-color-text-rgba($opacity:1) {
  color: rgba(255, 166, 0, $opacity);
}

//Placeholders
//Usage:
//@include placeholder;
@mixin placeholder {
  // sass-lint:disable no-vendor-prefixes
  ::-webkit-input-placeholder {
	@content
  }
  :-moz-placeholder {
	@content
  }
  ::-moz-placeholder {
	@content
  }
  :-ms-input-placeholder {
	@content
  }
}
