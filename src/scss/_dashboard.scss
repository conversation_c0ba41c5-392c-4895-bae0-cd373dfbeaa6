@import 'modules/dashboard_partner_form';

.dashboard_user_partner {
  padding: 20px 0;
  margin-top: 20px;
  width: 100%;
  .ant-btn:hover,
  .ant-btn:focus,
  .ant-btn:active,
  .ant-btn.active {
    background-color: white;
  }
  display: flex;
  .dash_side_nav {
    width: 280px;
    .ant-menu-item,
    .ant-menu-submenu-title {
      padding-left: 10px !important;
    }
    .ant-menu-inline .ant-menu-sub.ant-menu-inline {
      padding-left: 25px;
    }
    .ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline {
      background-color: transparent;
    }
    .ant-menu-light.ant-menu-root.ant-menu-inline {
      border: none;
    }
    .intro_text {
      h1 {
        font-size: 20px;
        color: #6c757d;
        i {
          vertical-align: 0.2em !important;
        }
        + p {
          color: #ccc;
        }
      }
      font-weight: bold;
    }
    .nav_container {
      margin-top: 50px;
    }
    .admin_message_box {
      width: 100%;
      flex: 1;
      padding: 20px 17px 20px 17px;
      width: 80%;
      color: white;
      border-radius: 20px;
      line-height: 1.5;
      background-color: $color-primary;
      text-align: center;
      margin-top: 60px;
      position: relative;
      display: flex;
      align-items: center;
      flex-direction: column;
      a {
        margin-top: 10px;
        text-align: center;

        width: 110px;
        text-align: center;
        border-radius: 10px;
        padding: 6px 0;
        background-color: $orange;
        display: block;
        color: white;
      }
    }
    .user_profile_present {
      margin-top: 100px;
      padding: 10px 0;
      display: flex;
      .profile_img {
        height: 50px;
        width: 50px;
        overflow: hidden;
        border-radius: 50%;
        img {
          object-fit: cover;
          width: 100%;
          height: 100%;
        }
      }
      .user_info {
        cursor: pointer;
        &:hover {
          i {
            color: $color-primary;
          }
        }
        padding-left: 15px;
        margin-top: 5px;
        h1 {
          font-size: 17px;
          + p {
            margin-top: 2px;
            font-size: 14px;
            color: #ccc;
          }
        }
      }
    }
  }
  .dashboard_stats_card {
    border-radius: 15px;
    min-height: 680px;
    padding: 25px;
    &.hightlights {
      background-color: #f6f6f5;
    }
    .company-logo {
      // opacity: 0;
      background-color: white;
      height: 100px;
      width: 100px;
      margin-bottom: 30px;
      border-radius: 50%;
      //padding: 10px;
      overflow: hidden;
      border: 2px dashed $color-primary;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        object-fit: cover;
      }
    }

    .company_info_box {
      padding: 20px;
      background-color: white;
      border-radius: 10px;
      min-height: 100px;
      .form-group {
        margin-bottom: 24px;
      }
      label {
        color: $textColorLight;
        font-size: 14px;
        + div {
          margin-top: 4px;
          font-weight: bold;
          color: $textColorDark;
          font-size: 18px;
        }
      }
    }

    .venue_add_action {
      margin-top: 80px;
      li {
        border-left: 3px solid $color-primary;
        &:nth-child(2n) {
          border-left: 3px solid $color-secondary;
          i {
            color: $color-secondary;
          }
        }
        a {
          padding: 2px 0 2px 30px;
          transition: 0.3s;
          &.inactive {
            cursor: auto;
          }
          &:hover:not(.inactive) {
            padding-right: 15px;
            h3 {
              opacity: 1;
            }
          }
          margin-bottom: 35px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          div {
            color: $textColorLight;
            font-size: 13px;
          }
          h3 {
            color: $textColorDark;
            opacity: 0.8;
            font-size: 18px;
            font-weight: bold;
          }
        }
      }
    }
  }
  .dashboard_statistics {
    padding: 20px 0;
    h1 {
      font-size: 24px;
      font-weight: bold;
      color: $color-primary;
      margin-bottom: 30px;
    }
    .stats_info {
      li {
        display: flex;
        margin-bottom: 20px;
        cursor: pointer;
        .icon_presenter {
          height: 60px;
          width: 60px;
          @include primary-color-rgba(0.018);
          color: $color-primary;
          text-align: center;
          margin-right: 20px;
          @include primary-color-text-rgba(0.8);
          line-height: 55px;
          font-size: 24px;
          border-radius: 10px;
          border: 1px solid $color-primary-light;
        }
        div {
          p {
            margin-top: 8px;
            color: $textColorLight;
            font-size: 14px;
          }
          h3 {
            font-size: 20px;
            font-weight: bold;
            color: $textColorDark;
          }
        }
      }
    }
  }
  .dashboard-content {
    flex: 1;
  }
}

.activity_logs {
  .title_sub_box {
    h2 {
      font-size: 22px;
      font-weight: bold;
      color: $color-primary;
      + p {
        color: #ccc;
        margin-top: 5px;
      }
    }
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }
  .activity_list {
    li {
      border-bottom: 1px solid #eee;
      &:last-child {
        border-bottom: none;
      }
      cursor: pointer;
      &:hover {
        //background-color: #eee;
      }
      margin-bottom: 20px;
      padding-bottom: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      h4 {
        color: $textColorDark;
        opacity: 0.9;
        font-size: 17px;
        font-weight: bold;
        + p {
          font-size: 14px;
          color: #ccc;
          margin-top: 3px;
        }
      }
    }
  }
}

// .action-button-container {
//   padding-top: 15px;
//   display: flex;
//   justify-content: flex-end;
//   gap: 10px;
// }

.dashboard_add_form {
  &.ant-modal {
    max-width: calc(100vw - 0px) !important;
  }
  .ant-modal-content {
    box-shadow: none;
  }
}

.page-intro-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  .header-title {
    flex: 1;
  }
  .action-button-container {
    // flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    gap: 10px;
  }
}

h1.titled-info {
  font-weight: bold;
  font-size: 28px;
  color: $textColorDark;
  + p {
    margin-top: 5;
    font-size: 16px;
    color: $textColorLight;
    line-height: 1.5;
    margin-bottom: 10px;
  }
}

.dashboard-main-content {
  min-height: 550px;

  .ant-tabs-nav .ant-tabs-tab {
    padding: 10px 10px !important;
  }
  .tabled-content {
    margin-top: 10px;

    .dashboard-table {
      min-height: 700px;
    }
  }
}

.custom-table-row {
  .ant-table-cell {
    font-size: 16px;
    line-height: 2;
  }
}

.event-item {
  display: flex;
  align-items: center;
  gap: 12px;

  &__image-container {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__placeholder {
    width: 100%;
    height: 100%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      color: #bfbfbf;
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__title {
    font-size: 14px;
    font-weight: bold;
    color: #262626;
    // overflow-wrap: anywhere;
    overflow: hidden;
    display: -webkit-box;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    word-break: break-word;
  }

  &__categories {
    display: flex;
    flex-wrap: wrap;
    font-size: small;
    width: 250px;
    gap: 4px;
  }

  &__category-tag {
    font-size: 12px;
  }
}

.card-container {
  .card-header {
    &:first-child {
      border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
    }
    padding: 5px 15px;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  }
  .card-body {
    display: flex;
    min-height: 1px;
    padding: 1.25rem;
    justify-content: center;
    .icon-avatar {
      width: 30px;
    }
    .body-content {
      flex: 10 1;
      p {
        line-height: 1;
        strong {
          font-weight: 700;
          sup {
            padding-left: 5px;
            color: green;
            font-size: 14px;
          }
        }
      }
      small {
        font-size: 12px;
        color: #888;
      }
    }
    .body-action {
      width: 140px;
    }
  }
}

.section-box {
	background: #fff;
	border-radius: 10px;
	box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
	padding: 20px;
	margin-bottom: 20px;
	width: 100%;

  &.no-box {
    background: none;
    border-radius: 0;
    box-shadow: none;
    padding: 0;
    margin-bottom: 0;
  }

	.header {
		background: #f8f8f8;
		padding: 10px 20px;
		border-radius: 8px 8px 0 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 1.2rem;
		font-weight: 600;
		color: $color-primary;
	}

	.section-form {
		display: flex;
		flex-direction: column;
    margin-top: 10px;
    margin-left: 20px;
		gap: 12px;

    .input-container {
      display: flex;
      flex-direction: column;
      // width: 500px;
      margin-bottom: 1rem;

      label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
        font-size: 1rem;
      }

      .error-message {
        font-size: 0.875rem;
        color: #e53935;
        margin-bottom: 0.5rem;
        font-weight: 500;
      }

      .ant-picker {
        height: 36px;
        width: 200px;
      }

      .profile-image-section {
        margin-top: 16px;

        .profile-image {
          background-color: white;
          height: 110px;
          width: 110px;
          border-radius: 50%;
          overflow: hidden;
          border: 2px dashed $color-primary;
          display: flex;
          justify-content: center;
          align-items: center;
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;

          &.upload-trigger {
            cursor: pointer;

            span {
              color: $color-primary;
              font-size: 0.9rem;
              font-weight: 500;
            }

            &:hover {
              border-color: darken($color-primary, 10%);
              transform: translateY(-2px);
            }
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .image-preview-container {
          position: relative;
          display: inline-block;
        }

        .clear-icon {
          position: absolute;
          top: -10px;
          right: -10px;
          cursor: pointer;
          font-size: 22px;
          color: $color-primary;
          background-color: white;
          border-radius: 50%;
          transition: all 0.2s ease;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);

          &:hover {
            transform: scale(1.15);
            color: darken($color-primary, 15%);
          }
        }
      }
    }

    .form-actions {
      display: flex;
      gap: 16px;
    }
	}

	.section-view {
		display: flex;
		flex-direction: column;
    margin-top: 10px;
    margin-left: 5px;

		.section-title-content {
			display: flex;
			align-items: center;
			gap: 10px;
			margin-bottom: 10px;

			.icon-avatar {
				background: #f5f5f5;
				border-radius: 50%;
				width: 40px;
				height: 40px;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 18px;
				color: $color-primary;
			}
		}

    .section-main-content {
      margin-left: 5px;
      margin-top: 5px;
      color: #a49e9e;
      display: flex;
      flex-direction: column;
      gap: 6px;
    }
	}
}

// Partner Dashboard Responsive Styles
.dashboard_user_partner {
  padding: 20px 0;
  margin-top: 20px;
  width: 100%;
  
  .ant-btn:hover,
  .ant-btn:focus,
  .ant-btn:active,
  .ant-btn.active {
    background-color: white;
  }
  
  display: flex;
  
  .dash_side_nav {
    width: 280px;
    
    .ant-menu-item,
    .ant-menu-submenu-title {
      padding-left: 10px !important;
    }
    
    .ant-menu-inline .ant-menu-sub.ant-menu-inline {
      padding-left: 25px;
    }
    
    .ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline {
      background-color: transparent;
    }
    
    .ant-menu-light.ant-menu-root.ant-menu-inline {
      border: none;
    }
    
    .intro_text {
      h1 {
        font-size: 20px;
        color: #6c757d;
        i {
          vertical-align: 0.2em !important;
        }
        + p {
          color: #ccc;
        }
      }
      font-weight: bold;
    }
    
    .nav_container {
      margin-top: 50px;
    }
    
    .admin_message_box {
      width: 100%;
      flex: 1;
      padding: 20px 17px 20px 17px;
      width: 80%;
      color: white;
      border-radius: 20px;
      line-height: 1.5;
      background-color: $color-primary;
      text-align: center;
      margin-top: 60px;
      position: relative;
      display: flex;
      align-items: center;
      flex-direction: column;
      
      a {
        margin-top: 10px;
        text-align: center;
        width: 110px;
        text-align: center;
        border-radius: 10px;
        padding: 6px 0;
        background-color: $orange;
        display: block;
        color: white;
      }
    }
    
    .user_profile_present {
      margin-top: 100px;
      padding: 10px 0;
      display: flex;
      
      .profile_img {
        height: 50px;
        width: 50px;
        overflow: hidden;
        border-radius: 50%;
        
        img {
          object-fit: cover;
          width: 100%;
          height: 100%;
        }
      }
      
      .user_info {
        cursor: pointer;
        &:hover {
          i {
            color: $color-primary;
          }
        }
        padding-left: 15px;
        margin-top: 5px;
        
        h1 {
          font-size: 17px;
          + p {
            margin-top: 2px;
            font-size: 14px;
            color: #ccc;
          }
        }
      }
    }
  }
  
  .dashboard_stats_card {
    border-radius: 15px;
    min-height: 680px;
    padding: 25px;
    
    &.hightlights {
      background-color: #f6f6f5;
    }
    
    .company-logo {
      background-color: white;
      height: 100px;
      width: 100px;
      margin-bottom: 30px;
      border-radius: 50%;
      overflow: hidden;
      border: 2px dashed $color-primary;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s ease;
      
      &:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }
      
      img {
        object-fit: cover;
        width: 100%;
        height: 100%;
      }
    }

    .company_info_box {
      padding: 20px;
      background-color: white;
      border-radius: 10px;
      min-height: 100px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8e8e8;
      
      .form-group {
        margin-bottom: 24px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      
      label {
        color: $textColorLight;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
        
        + div {
          margin-top: 4px;
          font-weight: bold;
          color: $textColorDark;
          font-size: 18px;
          line-height: 1.4;
          padding: 8px 12px;
          background: #f8f9fa;
          border-radius: 6px;
          border-left: 3px solid $color-primary;
        }
      }
    }

    .venue_add_action {
      margin-top: 80px;
      
      li {
        border-left: 3px solid $color-primary;
        margin-bottom: 35px;
        
        &:nth-child(2n) {
          border-left: 3px solid $color-secondary;
          i {
            color: $color-secondary;
          }
        }
        
        a {
          padding: 2px 0 2px 30px;
          transition: 0.3s;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          &.inactive {
            cursor: auto;
            opacity: 0.6;
          }
          
          &:hover:not(.inactive) {
            padding-right: 15px;
            h3 {
              opacity: 1;
            }
          }
          
          div {
            color: $textColorLight;
            font-size: 13px;
          }
          
          h3 {
            color: $textColorDark;
            opacity: 0.8;
            font-size: 18px;
            font-weight: bold;
            margin: 0;
          }
        }
      }
    }
  }
  
  .dashboard_statistics {
    padding: 20px 0;
    
    h1 {
      font-size: 24px;
      font-weight: bold;
      color: $color-primary;
      margin-bottom: 30px;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 0;
        width: 40px;
        height: 3px;
        background: linear-gradient(90deg, $color-primary, $color-secondary);
        border-radius: 2px;
      }
    }
    
    .stats_info {
      li {
        display: flex;
        margin-bottom: 20px;
        cursor: pointer;
        padding: 16px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid #e8e8e8;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }
        
        .icon_presenter {
          height: 60px;
          width: 60px;
          @include primary-color-rgba(0.018);
          color: $color-primary;
          text-align: center;
          margin-right: 20px;
          @include primary-color-text-rgba(0.8);
          line-height: 55px;
          font-size: 24px;
          border-radius: 10px;
          border: 1px solid $color-primary-light;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        div {
          flex: 1;
          
          p {
            margin-top: 8px;
            color: $textColorLight;
            font-size: 14px;
            margin-bottom: 4px;
          }
          
          h3 {
            font-size: 20px;
            font-weight: bold;
            color: $textColorDark;
            margin: 0;
          }
        }
      }
    }
  }
  
  .dashboard-content {
    flex: 1;
  }
}

.activity_logs {
  .title_sub_box {
    h2 {
      font-size: 22px;
      font-weight: bold;
      color: $color-primary;
      margin-bottom: 8px;
      
      + p {
        color: #ccc;
        margin-top: 5px;
      }
    }
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }
  
  .activity_list {
    li {
      border-bottom: 1px solid #eee;
      padding: 16px;
      margin-bottom: 12px;
      border-radius: 8px;
      background: white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      
      &:last-child {
        border-bottom: none;
      }
      
      cursor: pointer;
      
      &:hover {
        background: #f8f9fa;
        transform: translateX(4px);
      }
      
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h4 {
        color: $textColorDark;
        opacity: 0.9;
        font-size: 17px;
        font-weight: bold;
        margin: 0;
        
        + p {
          font-size: 14px;
          color: #ccc;
          margin-top: 3px;
          margin-bottom: 0;
        }
      }
    }
  }
}

// Responsive Design for Partner Dashboard
@media all and (max-width: 1200px) {
  .dashboard_user_partner {
    .dashboard_stats_card {
      padding: 20px;
      min-height: 600px;
      
      .company-logo {
        height: 90px;
        width: 90px;
        margin-bottom: 25px;
      }
      
      .company_info_box {
        padding: 18px;
        
        .form-group {
          margin-bottom: 20px;
        }
        
        label + div {
          font-size: 16px;
          padding: 6px 10px;
        }
      }
      
      .venue_add_action {
        margin-top: 60px;
        
        li {
          margin-bottom: 25px;
          
          a {
            padding: 2px 0 2px 25px;
            
            div {
              font-size: 12px;
            }
            
            h3 {
              font-size: 16px;
            }
          }
        }
      }
    }
    
    .dashboard_statistics {
      padding: 15px 0;
      
      h1 {
        font-size: 22px;
        margin-bottom: 25px;
      }
      
      .stats_info {
        li {
          padding: 14px;
          margin-bottom: 15px;
          
          .icon_presenter {
            height: 55px;
            width: 55px;
            margin-right: 16px;
            font-size: 22px;
            line-height: 50px;
          }
          
          div {
            p {
              font-size: 13px;
            }
            
            h3 {
              font-size: 18px;
            }
          }
        }
      }
    }
    
    .activity_logs {
      .title_sub_box {
        margin-bottom: 25px;
        
        h2 {
          font-size: 20px;
        }
      }
      
      .activity_list {
        li {
          padding: 14px;
          margin-bottom: 10px;
          
          h4 {
            font-size: 16px;
            
            + p {
              font-size: 13px;
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 992px) {
  .dashboard_user_partner {
    flex-direction: column;
    
    .dash_side_nav {
      width: 100%;
      margin-bottom: 20px;
      
      .nav_container {
        margin-top: 30px;
      }
      
      .admin_message_box {
        margin-top: 30px;
        width: 100%;
        padding: 15px;
      }
      
      .user_profile_present {
        margin-top: 30px;
      }
    }
    
    .dashboard-content {
      width: 100%;
    }
    
    .row {
      flex-direction: column;
      
      .col-md-8,
      .col-md-4 {
        width: 100%;
        margin-bottom: 20px;
      }
    }
    
    .dashboard_stats_card {
      padding: 18px;
      min-height: auto;
      
      .company-logo {
        height: 80px;
        width: 80px;
        margin-bottom: 20px;
      }
      
      .company_info_box {
        padding: 16px;
        
        .form-group {
          margin-bottom: 18px;
        }
        
        label + div {
          font-size: 15px;
          padding: 6px 10px;
        }
      }
      
      .venue_add_action {
        margin-top: 40px;
        
        li {
          margin-bottom: 20px;
          
          a {
            padding: 2px 0 2px 20px;
            
            div {
              font-size: 11px;
            }
            
            h3 {
              font-size: 15px;
            }
          }
        }
      }
    }
    
    .dashboard_statistics {
      padding: 10px 0;
      
      h1 {
        font-size: 20px;
        margin-bottom: 20px;
      }
      
      .stats_info {
        li {
          padding: 12px;
          margin-bottom: 12px;
          
          .icon_presenter {
            height: 50px;
            width: 50px;
            margin-right: 14px;
            font-size: 20px;
            line-height: 45px;
          }
          
          div {
            p {
              font-size: 12px;
            }
            
            h3 {
              font-size: 16px;
            }
          }
        }
      }
    }
    
    .activity_logs {
      .title_sub_box {
        margin-bottom: 20px;
        
        h2 {
          font-size: 18px;
        }
      }
      
      .activity_list {
        li {
          padding: 12px;
          margin-bottom: 8px;
          
          h4 {
            font-size: 15px;
            
            + p {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 768px) {
  .dashboard_user_partner {
    padding: 15px 0;
    margin-top: 15px;
    
    .dashboard_stats_card {
      padding: 15px;
      border-radius: 12px;
      
      .company-logo {
        height: 70px;
        width: 70px;
        margin-bottom: 18px;
        border-width: 1px;
      }
      
      .company_info_box {
        padding: 14px;
        border-radius: 8px;
        
        .form-group {
          margin-bottom: 16px;
        }
        
        label {
          font-size: 13px;
        }
        
        label + div {
          font-size: 14px;
          padding: 5px 8px;
        }
      }
      
      .venue_add_action {
        margin-top: 30px;
        
        li {
          margin-bottom: 15px;
          
          a {
            padding: 2px 0 2px 15px;
            
            div {
              font-size: 10px;
            }
            
            h3 {
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .dashboard_statistics {
      padding: 8px 0;
      
      h1 {
        font-size: 18px;
        margin-bottom: 18px;
      }
      
      .stats_info {
        li {
          padding: 10px;
          margin-bottom: 10px;
          border-radius: 8px;
          
          .icon_presenter {
            height: 45px;
            width: 45px;
            margin-right: 12px;
            font-size: 18px;
            line-height: 40px;
            border-radius: 8px;
          }
          
          div {
            p {
              font-size: 11px;
            }
            
            h3 {
              font-size: 15px;
            }
          }
        }
      }
    }
    
    .activity_logs {
      .title_sub_box {
        margin-bottom: 18px;
        
        h2 {
          font-size: 16px;
        }
      }
      
      .activity_list {
        li {
          padding: 10px;
          margin-bottom: 6px;
          border-radius: 6px;
          
          h4 {
            font-size: 14px;
            
            + p {
              font-size: 11px;
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 576px) {
  .dashboard_user_partner {
    padding: 10px 0;
    margin-top: 10px;
    
    .dashboard_stats_card {
      padding: 12px;
      border-radius: 10px;
      
      .company-logo {
        height: 60px;
        width: 60px;
        margin-bottom: 15px;
      }
      
      .company_info_box {
        padding: 12px;
        border-radius: 6px;
        
        .form-group {
          margin-bottom: 14px;
        }
        
        label {
          font-size: 12px;
        }
        
        label + div {
          font-size: 13px;
          padding: 4px 6px;
        }
      }
      
      .venue_add_action {
        margin-top: 25px;
        
        li {
          margin-bottom: 12px;
          
          a {
            padding: 2px 0 2px 12px;
            
            div {
              font-size: 9px;
            }
            
            h3 {
              font-size: 13px;
            }
          }
        }
      }
    }
    
    .dashboard_statistics {
      padding: 6px 0;
      
      h1 {
        font-size: 16px;
        margin-bottom: 15px;
      }
      
      .stats_info {
        li {
          padding: 8px;
          margin-bottom: 8px;
          border-radius: 6px;
          
          .icon_presenter {
            height: 40px;
            width: 40px;
            margin-right: 10px;
            font-size: 16px;
            line-height: 35px;
            border-radius: 6px;
          }
          
          div {
            p {
              font-size: 10px;
            }
            
            h3 {
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .activity_logs {
      .title_sub_box {
        margin-bottom: 15px;
        
        h2 {
          font-size: 15px;
        }
      }
      
      .activity_list {
        li {
          padding: 8px;
          margin-bottom: 5px;
          border-radius: 5px;
          
          h4 {
            font-size: 13px;
            
            + p {
              font-size: 10px;
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 480px) {
  .dashboard_user_partner {
    .dashboard_stats_card {
      padding: 10px;
      
      .company-logo {
        height: 55px;
        width: 55px;
        margin-bottom: 12px;
      }
      
      .company_info_box {
        padding: 10px;
        
        .form-group {
          margin-bottom: 12px;
        }
        
        label {
          font-size: 11px;
        }
        
        label + div {
          font-size: 12px;
          padding: 3px 5px;
        }
      }
      
      .venue_add_action {
        margin-top: 20px;
        
        li {
          margin-bottom: 10px;
          
          a {
            padding: 2px 0 2px 10px;
            
            div {
              font-size: 8px;
            }
            
            h3 {
              font-size: 12px;
            }
          }
        }
      }
    }
    
    .dashboard_statistics {
      h1 {
        font-size: 15px;
        margin-bottom: 12px;
      }
      
      .stats_info {
        li {
          padding: 6px;
          margin-bottom: 6px;
          
          .icon_presenter {
            height: 35px;
            width: 35px;
            margin-right: 8px;
            font-size: 14px;
            line-height: 30px;
          }
          
          div {
            p {
              font-size: 9px;
            }
            
            h3 {
              font-size: 13px;
            }
          }
        }
      }
    }
    
    .activity_logs {
      .title_sub_box {
        margin-bottom: 12px;
        
        h2 {
          font-size: 14px;
        }
      }
      
      .activity_list {
        li {
          padding: 6px;
          margin-bottom: 4px;
          
          h4 {
            font-size: 12px;
            
            + p {
              font-size: 9px;
            }
          }
        }
      }
    }
  }
}
