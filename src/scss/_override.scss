@import 'variables';

a {
  &:hover {
    text-decoration: none;
    color: $color-primary-active;
  }
  &:focus {
    text-decoration: none;
  }
}

.nav-tabs .nav-link {
  border: none;
}

.nav-tabs {
  border-bottom: none;
}

/*
Container Resized
*/
@media (min-width: 576px) {
  .container {
    //max-width: 540px;
    max-width: 560px;
  }
}

@media (min-width: 768px) {
  .container {
    //max-width: 720px;
    max-width: 750px;
  }
}

@media (min-width: 992px) {
  .container {
    //max-width: 960px;
    max-width: 1000px;
  }
}

@media (min-width: 1200px) {
  .container {
    //max-width: 1140px;
    max-width: 1190px;
  }
}

/*
Container Resized
*/

.form-control {
  border: none;
  height: auto;
}

.form-control:focus {
  border: none;
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.input-group-text {
  background-color: white;
  border: none;
}

.modal-title {
  color: white;
}

//.app-modal {
//  .modal-content {
//	overflow: hidden;
//  }
//  .modal-header {
//	background: $color-primary;
//	border-bottom: none;
//	color: white;
//	border-radius: 0;
//	position: relative;
//	button.close {
//	  color: white;
//	}
//	div.decorate-bar {
//	  height: 5px;
//	  width: 70%;
//	  background: $color-secondary;
//	  position: absolute;
//	  bottom: 0;
//	  left: 0;
//	  border-top-right-radius: 2px;
//	  border-bottom-right-radius: 2px;
//	}
//  }
//  .modal-body {
//	padding: 0;
//	.city-search-bar {
//	  position: relative;
//	  z-index: 999;
//	  padding: 20px;
//	  background: white;
//	  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
//	  .search-box-input {
//		border: 1px solid #eee;
//		height: 50px;
//	  }
//	}
//
//	.city-list-container {
//	  //margin-top: 10px;
//	  padding: 20px;
//	  min-height: 150px;
//	  background: #f7f7f7;
//	}
//  }
//}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle,
.btn-primary {
  background: $color-primary;
  border: none;
  &:hover {
    background: $color-primary-active;
  }
  &:focus {
    outline: none;
    box-shadow: none;
  }
}

.btn-primary:disabled {
  background: #8b005a;
  cursor: not-allowed;
}

.btn-outline-primary {
  border-color: $color-primary;
  &:hover {
    background-color: $color-primary;
    color: white !important;
    border-color: $color-primary;
  }
  &.active {
    background-color: $color-primary !important;
    color: white !important;
    border-color: $color-primary !important;
  }
}

.btn-light,
.btn-light:focus {
  background: white;
  box-shadow: 0 0.03em 1px rgba(0, 0, 0, 0.3);
  border: none;
  outline: none;
  &:hover {
    background: #eee;
    color: #777;
  }
}

.text-primary {
  color: $color-primary !important;
}

.card {
  padding: 3px;
  margin-bottom: 40px;
  span {
    // font-size: 10px !important;
    &.checked {
      color: $color-primary;
    }
  }
  border-radius: 0;
  //border: none;
  .card-body {
    padding: 15px 0.5rem;
    min-height: 65px;
    .card-title {
      font-size: 15px;
      margin-bottom: 0;
      font-weight: 400;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
    p.itenary-days {
      font-size: 14px;
      font-weight: bold;
      text-align: right;
      color: #666;
    }
    a.follow-btn {
      font-size: 13px;
      color: $color-primary;
      padding: 2px 7px;
      float: right;
    }
    //a.like-events-heart {
    //  font-size: 18px;
    //  &:hover {
    //	color: $color-primary;
    //  }
    //}
    span {
      color: #666;
      font-size: 12px;
      font-weight: 300;
    }
    .card-general-info {
      i {
        font-size: 15px;
        color: $color-primary;
      }
      padding: 5px 0;
      color: #777;
      font-size: 13px;
    }
  }
  .card-footer {
    border-radius: 0;
    padding: 0.5rem 0.5rem;
    p {
      font-size: 13px;
    }
  }
}

.slick-dotted {
  ul {
    margin-top: 40px;
    display: flex;
    justify-content: center;
    li {
      display: inline-block;
      text-align: center;
      margin: 0 10px;

      button {
        color: transparent;
        background-color: transparent;
        border-radius: 40px;
        height: 14px;
        display: block;
        width: 13px;
        border: 1px solid $color-light;
        &:focus {
          outline: none;
        }
        &[aria-selected='true'] {
          background: $color-light;
        }
      }
    }
  }
}

/* Ripple magic */
a.ripple-eff,
button.ripple-eff {
  position: relative;
  overflow: hidden;
}

a.ripple-eff:after,
button.ripple-eff:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.2);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 1;
  }
  20% {
    transform: scale(25, 25);
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

a.ripple-eff:focus:not(:active)::after,
button.ripple-eff:focus:not(:active)::after {
  animation: ripple 0.5s ease-out;
}

.dropdown-toggle::after {
  border-top: none;
}

.noUi-horizontal {
  height: 10px;
}

.noUi-horizontal .noUi-handle {
  width: 10px;
  height: 10px;
  top: 0;
}

html:not([dir='rtl']) .noUi-horizontal .noUi-handle {
  right: -6px;
  top: -1px;
}

.noUi-handle:before,
.noUi-handle:after {
  background-color: transparent;
  height: 0;
}

.noUi-handle {
  border-radius: 10px;
  border: 1px solid $color-primary;
  &:focus {
    outline: none;
  }
}

.noUi-connect {
  background: $color-primary;
}

.noUi-connects {
  border-radius: 10px;
}

.noUi-base,
.noUi-connects {
  position: static;
}

input:-webkit-autofill,
input:focus,
input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 30px #f9f9f9 inset;
  background: white;

  //-webkit-text-fill-color: yellow !important;
}

input:-webkit-autofill {
  background-color: red !important;
}

//And Design
.ant-checkbox-checked .ant-checkbox-inner,
.ant-checkbox-indeterminate .ant-checkbox-inner {
  background-color: $color-primary !important;
  border-color: $color-primary !important;
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: $color-primary !important;
}

.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
  color: $color-primary;
}

.ant-steps-item-finish > .ant-steps-item-content > .ant-steps-item-title:after {
  background-color: $color-primary;
}

a {
  &:hover {
    text-decoration: none;
    color: $color-primary-active;
  }
}

.nav-tabs .nav-link {
  border: none;
}

.nav-tabs {
  border-bottom: none;
}

/*
Container Resized
*/
@media (min-width: 576px) {
  .container {
    //max-width: 540px;
    max-width: 560px;
  }
}

@media (min-width: 768px) {
  .container {
    //max-width: 720px;
    max-width: 750px;
  }
}

@media (min-width: 992px) {
  .container {
    //max-width: 960px;
    max-width: 1000px;
  }
}

@media (min-width: 1200px) {
  .container {
    //max-width: 1140px;
    max-width: 1190px;
  }
}

/*
Container Resized
*/

.form-control {
  border: none;
}

.form-control:focus {
  border: none;
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

// .input-group-text {
//   background-color: white;
//   border: none;
// }

.app-modal {
  .modal-content {
    overflow: hidden;
  }
  .modal-header {
    background: $color-primary;
    border-bottom: none;
    color: white;
    border-radius: 0;
    position: relative;
    .btn-close {
      color: white;
      background-color: white;
    }
    div.decorate-bar {
      height: 5px;
      width: 100%;
      background: $color-secondary;
      position: absolute;
      bottom: 0;
      left: 0;
      border-top-right-radius: 2px;
      border-bottom-right-radius: 2px;
    }
  }
  .modal-body {
    padding: 0;
    .city-search-bar {
      position: relative;
      z-index: 999;
      padding: 20px;
      background: white;
      box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
      .search-box-input {
        border: 1px solid #eee;
        height: 50px;
      }
    }

    .city-list-container {
      //margin-top: 10px;
      padding: 20px;
      min-height: 150px;
      background: #f7f7f7;
    }
  }
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle,
.btn-primary {
  background: $color-primary;
  border: none;
  &:hover {
    background: $color-primary-active;
  }
  &:focus {
    outline: none;
    box-shadow: none;
  }
}

.btn-outline-primary {
  border-color: $color-primary;
  &:hover {
    background-color: $color-primary;
    color: white !important;
    border-color: $color-primary;
  }
  &.active {
    background-color: $color-primary !important;
    color: white !important;
    border-color: $color-primary !important;
  }
}

.btn-light {
  background: white;
  box-shadow: 0 0.03em 1px rgba(0, 0, 0, 0.3);
  border: none;
  outline: none;
  &:hover {
    background: #eee;
    color: #777;
  }
}

// .text-primary {
//   color: $color-primary !important;
// }

.card {
  padding: 3px;
  margin-bottom: 40px;
  span {
    // font-size: 10px !important;
    &.checked {
      color: $color-primary;
    }
  }
  .card-img-top {
    aspect-ratio: 16/9;
  }
  .card-logo-cover {
    position: absolute;
    bottom: 10px;
    box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.4);
    left: 10px;
    width: 30%;
    background-color: white;
    padding: 3px;
    img {
      width: 100%;
    }
  }

  border-radius: 0;
  //border: none;
  .card-body {
    padding: 15px 0.5rem;
    min-height: 65px;
    .card-title {
      font-size: 15px;
      margin-bottom: 0;
      font-weight: 400;
      cursor: pointer;
      &:hover {
        text-decoration: underline;
      }
    }
    a.follow-btn {
      font-size: 13px;
      color: $color-primary;
      padding: 2px 7px;
      float: right;
    }
    //a.like-events-heart {
    //  font-size: 18px;
    //  &:hover {
    //	color: $color-primary;
    //  }
    //}
    span {
      color: #666;
      font-size: 12px;
      font-weight: 300;
    }
    .card-general-info {
      i {
        font-size: 15px;
        color: $color-primary;
      }
      padding: 5px 0;
      color: #777;
      font-size: 13px;
    }
  }
  .card-footer {
    border-radius: 0;
    padding: 0.5rem 0.5rem;
    p {
      font-size: 13px;
    }
  }
}

.slick-dotted {
  ul {
    margin-top: 40px;
    display: flex;
    justify-content: center;
    li {
      display: inline-block;
      text-align: center;
      margin: 0 10px;

      button {
        color: transparent;
        background-color: transparent;
        border-radius: 40px;
        height: 14px;
        display: block;
        width: 13px;
        border: 1px solid $color-light;
        &:focus {
          outline: none;
        }
        &[aria-selected='true'] {
          background: $color-light;
        }
      }
    }
  }
}

/* Ripple magic */
a.ripple-eff,
button.ripple-eff {
  position: relative;
  overflow: hidden;
}

a.ripple-eff:after,
button.ripple-eff:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.2);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 1;
  }
  20% {
    transform: scale(25, 25);
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

a.ripple-eff:focus:not(:active)::after,
button.ripple-eff:focus:not(:active)::after {
  animation: ripple 0.5s ease-out;
}

.dropdown-toggle::after {
  border-top: none;
}

.noUi-horizontal {
  height: 10px;
}

.noUi-horizontal .noUi-handle {
  width: 10px;
  height: 10px;
  top: 0;
}

html:not([dir='rtl']) .noUi-horizontal .noUi-handle {
  right: -6px;
  top: -1px;
}

.noUi-handle:before,
.noUi-handle:after {
  background-color: transparent;
  height: 0;
}

.noUi-handle {
  border-radius: 10px;
  border: 1px solid $color-primary;
  &:focus {
    outline: none;
  }
}

.noUi-connect {
  background: $color-primary;
}

.noUi-connects {
  border-radius: 10px;
}

.noUi-base,
.noUi-connects {
  position: static;
}

input:-webkit-autofill,
input:focus,
input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0 30px #f9f9f9 inset;
  background: white;

  //-webkit-text-fill-color: yellow !important;
}

input:-webkit-autofill {
  background-color: red !important;
}

//And Design
.ant-input {
  height: 40px;
}

.ant-checkbox-checked .ant-checkbox-inner,
.ant-checkbox-indeterminate .ant-checkbox-inner {
  background-color: $color-primary !important;
  border-color: $color-primary !important;
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: $color-primary !important;
}

.ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon,
.ant-steps-item-custom.ant-steps-item-process
  .ant-steps-item-icon
  > .ant-steps-icon {
  color: $color-primary;
}

.ant-steps-item-description {
  font-size: 12px;
}

.ant-steps-item-title {
  line-height: 25px;
}

.ant-btn {
  height: 40px;
}

.ant-input:hover {
  border-color: $color-primary;
}

.ant-input:focus {
  border-color: $color-primary;
  box-shadow: 0 0 0 2px rgba(168, 13, 93, 0.1);
}

.ant-btn:hover,
.ant-btn:focus {
  border-color: $color-primary;
  color: $color-primary;
}

.ant-btn.btn-primary {
  &:hover,
  &:focus {
    color: white;
  }
}

.ant-btn-primary,
.ant-btn-primary:hover,
.ant-btn-primary:focus {
  border-color: $color-primary;
  background-color: $color-primary;
  color: white;
}

.ant-btn-primary:active {
  background-color: $color-primary-active;
}

.ant-form-item-label label {
  font-weight: 600;
  color: #777;
}

.ant-btn-lg {
  padding-left: 40px;
  padding-right: 40px;
}

// .ant-slider {
//   margin: 5px 10px 0 0;
// }

// .ant-slider-mark-text {
//   font-size: 9px;
// }

// .ant-slider-track {
//   background: #999;
// }

// .ant-slider-handle {
//   border: solid 2px #999;
// }

// .ant-slider:hover .ant-slider-track {
//   background: $color-primary-active;
// }

// .ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {
//   border-color: $color-primary-active;
// }

// .ant-slider-handle.ant-tooltip-open {
//   border-color: $color-primary-active;
// }

// .ant-slider-dot-active {
//   border-color: #999;
// }

// .ant-slider:hover .ant-slider-dot-active {
//   border-color: $color-primary-active;
// }

.ant-avatar {
  background: $color-primary;
  i {
    color: white !important;
  }
}

.ant-badge-count {
  box-shadow: 0 0 0 2px $color-primary-active;
}

.ant-input-affix-wrapper:hover .ant-input:not(.ant-input-disabled) {
  border-color: $color-primary;
  border-right-width: 1px;
}

.ant-tabs-nav {
  width: 100%;
  display: flex;
}

.ant-tabs-nav-container {
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.3);
}

.ant-tabs-nav .ant-tabs-tab {
  flex: 1;
  padding: 22px 17px;
  margin: 0;
}

.ant-tabs-content {
  padding: 0 20px 20px 20px;
}

.no-padding {
  padding: 0 !important;
}

.ant-tabs-nav .ant-tabs-tab-active {
  color: $color-primary-active;
  font-weight: bold;
  font-size: 16px;
}

.ant-tabs-ink-bar {
  background-color: $color-primary;
  height: 4px;
}

.ant-tabs-nav .ant-tabs-tab:hover {
  color: $color-primary;
}

.ant-pagination-item-active,
.ant-pagination-item-active:focus,
.ant-pagination-item-active:hover,
.ant-pagination-item:focus,
.ant-pagination-item:hover {
  border-color: $color-primary;
  color: $color-primary;
}

.ant-pagination-prev:focus .ant-pagination-item-link,
.ant-pagination-next:focus .ant-pagination-item-link,
.ant-pagination-prev:hover .ant-pagination-item-link,
.ant-pagination-next:hover .ant-pagination-item-link {
  border-color: $color-primary;
  color: $color-primary;
}

.ant-fullcalendar-fullscreen .ant-fullcalendar-content {
  height: 40px;
}

.ant-fullcalendar-fullscreen
  .ant-fullcalendar-month-panel-selected-cell
  .ant-fullcalendar-month,
.ant-fullcalendar-fullscreen
  .ant-fullcalendar-selected-day
  .ant-fullcalendar-date {
  background: none;
}

.ant-fullcalendar-fullscreen .ant-fullcalendar-month,
.ant-fullcalendar-fullscreen .ant-fullcalendar-date {
  height: 80px;
}

.ant-fullcalendar-fullscreen
  .ant-fullcalendar-month-panel-selected-cell
  .ant-fullcalendar-month,
.ant-fullcalendar-fullscreen
  .ant-fullcalendar-selected-day:not(.ant-fullcalendar-disabled-cell)
  .ant-fullcalendar-date {
  @include primary-color-rgba(1);
  .ant-fullcalendar-value {
    color: white;
  }
  .ant-fullcalendar-content {
    color: white;
  }
}

.ant-fullcalendar-fullscreen
  .ant-fullcalendar-month-panel-current-cell
  .ant-fullcalendar-month,
.ant-fullcalendar-fullscreen .ant-fullcalendar-today .ant-fullcalendar-date {
  border-top-color: $color-primary;
}

.ant-fullcalendar-fullscreen
  .ant-fullcalendar-month-panel-selected-cell
  .ant-fullcalendar-value,
.ant-fullcalendar-fullscreen
  .ant-fullcalendar-selected-day
  .ant-fullcalendar-value {
  color: $color-primary;
}

.ant-fullcalendar-fullscreen .ant-fullcalendar-month:hover,
.ant-fullcalendar-fullscreen .ant-fullcalendar-date:hover {
  @include primary-color-rgba(0.1);
}

.ant-radio-button-wrapper-checked:hover,
.ant-radio-button-wrapper-checked:first-child {
  border-color: $color-primary;
  color: $color-primary;
}

.ant-select-selection:hover {
  color: $color-primary;
  border-color: $color-primary;
}

.ant-select-focused .ant-select-selection,
.ant-select-selection:focus,
.ant-select-selection:active {
  color: $color-primary;
  border-color: $color-primary;
  box-shadow: 0 0 2px rgba(168, 13, 93, 1);
}

.ant-radio-button-wrapper:hover,
.ant-radio-button-wrapper-focused {
  color: $color-primary;
}

.ant-radio-button-wrapper-checked {
  border-color: $color-primary;
  color: $color-primary;
}

.ant-radio-button-wrapper-checked::before {
  background-color: $color-primary;
}

.ant-collapse-borderless {
  background-color: transparent;
}

.ant-collapse > .ant-collapse-item > .ant-collapse-header .arrow {
  left: 0;
}

.ant-rate {
  color: $color-primary;
}

.user-rating {
  .ratings-posted {
    display: flex;
    align-items: center;
    justify-content: center;
    color: $color-primary;
    font-weight: bold;
    h3 {
      font-size: 20px;
      padding-right: 7px;
    }
  }
  padding-top: 30px;
  .anticon-star {
    font-size: 10px;
  }
  p {
    padding-top: 10px;
    display: inline;
  }
}

.ant-rate-star {
  margin-right: 5px;
}

.ant-select-auto-complete.ant-select .ant-input:focus,
.ant-select-auto-complete.ant-select .ant-input:hover {
  border-color: $color-primary;
}

.ant-select-dropdown-menu-item:hover {
  @include primary-color-rgba(0.1);
}

.ant-select-dropdown-menu-item-active {
  @include primary-color-rgba(0.1);
}

.ant-breadcrumb a:hover {
  color: $color-primary;
}

.ant-breadcrumb-link .anticon {
  vertical-align: 0 !important;
}

.anticon {
  vertical-align: 0 !important;
}

.ant-radio-wrapper:hover .ant-radio .ant-radio-inner,
.ant-radio:hover .ant-radio-inner,
.ant-radio-focused .ant-radio-inner {
  border-color: $color-primary;
}

.ant-radio-checked .ant-radio-inner {
  border-color: $color-primary;
}

.ant-radio-inner:after {
  background-color: $color-primary;
}

.ant-calendar-picker {
  width: 100%;
}

.ant-notification-notice-message {
  color: $color-primary;
}

.ant-spin-dot i {
  background-color: $color-primary !important;
}

.ant-notification-notice-icon-success {
  color: $color-primary !important;
}

.btn-light:not(:disabled):not(.disabled):active,
.btn-light:not(:disabled):not(.disabled).active,
.show > .btn-light.dropdown-toggle {
  color: #888;
}

button:focus {
  outline: none;
}

.ant-spin-text {
  color: $color-primary;
  font-weight: bold;
}

.ant-fullcalendar-cell {
  .ant-fullcalendar-date {
    background-color: rgba(168, 13, 93, 0.1);
  }
}

.ant-fullcalendar-disabled-cell {
  background-color: transparent;
}

.ant-dropdown {
  z-index: 99991;
}

.ant-dropdown-menu-item-selected,
.ant-dropdown-menu-submenu-title-selected,
.ant-dropdown-menu-item-selected > a,
.ant-dropdown-menu-submenu-title-selected > a {
  color: $color-primary;
  background-color: rgba(168, 13, 93, 0.1);
}

.ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu-title:hover {
  background-color: rgba(168, 13, 93, 0.1);
}

.slider-control-bottomcenter {
  display: none;
}

.ant-select-dropdown {
  z-index: 99991;
}

.ph-avatar.small {
  width: 45%;
  min-width: 30px;
}

.ph-item {
  background-color: transparent;
  border: none;
}

.ph-picture,
.ph-avatar {
  background-color: #eee;
}

.ph-row div {
  background-color: #eee;
}

.btn-primary.disabled,
.btn-primary:disabled {
  @include primary-color-rgba(0.4);
}

// .ant-slider-mark-text.ant-slider-mark-text-active {
//   width: 50px !important;
// }

.ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title::after {
  background-color: $color-primary !important;
}

.ant-btn.ant-btn-primary:hover,
.ant-btn.ant-btn-primary:focus,
.ant-btn.ant-btn-primary:active,
.ant-btn.ant-btn-primary.active {
  background-color: $color-primary-active;
}

.ant-btn-disabled,
.ant-btn.disabled,
.ant-btn[disabled],
.ant-btn-disabled:hover,
.ant-btn.disabled:hover,
.ant-btn[disabled]:hover,
.ant-btn-disabled:focus,
.ant-btn.disabled:focus,
.ant-btn[disabled]:focus,
.ant-btn-disabled:active,
.ant-btn.disabled:active,
.ant-btn[disabled]:active,
.ant-btn-disabled.active,
.ant-btn.disabled.active,
.ant-btn[disabled].active {
  //@include primary-color-rgba(0.4);
  background-color: #eee;
}

.ant-menu-vertical .ant-menu-item,
.ant-menu-vertical-left .ant-menu-item,
.ant-menu-vertical-right .ant-menu-item,
.ant-menu-inline .ant-menu-item,
.ant-menu-vertical .ant-menu-submenu-title,
.ant-menu-vertical-left .ant-menu-submenu-title,
.ant-menu-vertical-right .ant-menu-submenu-title,
.ant-menu-inline .ant-menu-submenu-title {
  padding-left: 0 !important;
}

.ant-menu-submenu-selected,
.ant-menu-item:hover,
.ant-menu-item-active,
.ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
.ant-menu-submenu-active,
.ant-menu-submenu-title:hover {
  color: $color-primary;
}

.ant-menu-item-group {
  .ant-menu-item {
    padding-left: 20px !important;
  }
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  @include primary-color-rgba(0.1);
  background-color: transparent;
}

.ant-menu-item-selected {
  color: $color-primary;
  //padding-left: 15px!important;
}

.ant-menu-vertical .ant-menu-item::after,
.ant-menu-vertical-left .ant-menu-item::after,
.ant-menu-vertical-right .ant-menu-item::after,
.ant-menu-inline .ant-menu-item::after {
  //border-right-color: $color-primary;
  border-right-color: transparent;
}

.ant-menu-item-group-title {
  display: none;
}

.ant-menu {
  color: #999;
}

.ant-menu-inline,
.ant-menu-vertical,
.ant-menu-vertical-left {
  border-right: none;
}

.ant-menu-submenu-active i.ant-menu {
  color: red !important;
}

.ant-btn-background-ghost.ant-btn-primary {
  border-color: $color-primary;
  color: $color-primary;
}

.ant-select-dropdown-menu-item:hover:not(
    .ant-select-dropdown-menu-item-disabled
  ),
.ant-select-dropdown-menu-item-active:not(
    .ant-select-dropdown-menu-item-disabled
  ) {
  @include primary-color-rgba(0.08);
}

.ant-select-dropdown.ant-select-dropdown--multiple
  .ant-select-dropdown-menu-item-selected
  .ant-select-selected-icon,
.ant-select-dropdown.ant-select-dropdown--multiple
  .ant-select-dropdown-menu-item-selected:hover
  .ant-select-selected-icon {
  color: $color-primary;
}

.ant-tooltip {
  z-index: 99991;
}

.ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
  color: $color-primary;
}

.ant-tabs-nav-container {
  box-shadow: none;
}

.ant-tabs-content {
  padding: 0;
}

.ant-calendar .ant-calendar-ok-btn,
.ant-calendar .ant-calendar-ok-btn:active,
.ant-calendar .ant-calendar-ok-btn.active,
.ant-calendar .ant-calendar-ok-btn:hover,
.ant-calendar .ant-calendar-ok-btn:focus {
  background-color: $color-primary;
  color: white !important;
  border-color: $color-primary;
}

.ant-calendar-today .ant-calendar-date {
  color: $color-primary;
  border-color: $color-primary;
}

.ant-calendar-selected-day .ant-calendar-date {
  @include primary-color-rgba(0.1);
  border-color: $color-primary;
  color: $color-primary;
}

.ant-calendar-date:active {
  background-color: $color-primary;
}

.ant-calendar-date:hover {
  @include primary-color-rgba(0.1);
}

.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  border-color: $color-primary;
  color: $color-primary;
  box-shadow: none;
}

.ant-radio-button-wrapper-checked:not(
    .ant-radio-button-wrapper-disabled
  ):first-child {
  border-color: $color-primary;
}

.ant-radio-button-wrapper-checked:not(
    .ant-radio-button-wrapper-disabled
  ):hover {
  color: $color-primary;
  border-color: $color-primary;
  box-shadow: none;
}

.ant-radio-button-wrapper-checked:not(
    .ant-radio-button-wrapper-disabled
  ):active {
  color: $color-primary-active;
  box-shadow: none;
  border-color: $color-primary-active;
}
.ant-radio-button-wrapper-checked:not(
    .ant-radio-button-wrapper-disabled
  )::before {
  background-color: $color-primary !important;
  opacity: 1 !important;
}
.ant-btn-background-ghost.ant-btn-primary:hover,
.ant-btn-background-ghost.ant-btn-primary:focus {
  border-color: $color-primary-active;
  // color: $color-primary-active;
  color: white;
}
.ant-calendar-time-picker-select li:hover {
  @include primary-color-rgba(0.1);
}
.ant-calendar-time-picker-select li:focus {
  color: $color-primary;
}

.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  color: $color-primary !important;
}

.btn-primary:focus,
.btn-primary.focus {
  background-color: $color-primary-active !important;
}

.ant-dropdown-menu-item-disabled:hover,
.ant-dropdown-menu-submenu-title-disabled:hover {
  background-color: white;
  cursor: not-allowed !important;
  a {
    color: #ccc;
  }
}

.ant-dropdown-menu-item-disabled {
  a {
    color: #ccc;
  }
}
.search-box-input {
  .input-group-prepend {
    background-color: white;
    display: flex;
    align-items: center;
  }
  .form-control {
    border-radius: 0;
  }
}

.modal-backdrop {
  z-index: 999 !important;
}
