/* eslint-disable no-console */
import express, { Application, Response, Request } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import bodyParser from 'body-parser';
import http from 'http';

import { removeNullFieldsMiddleware } from '@/middlewares/remove-null-fields.middleware';
import { response } from '@/utils/response.util';
import { loggingMiddleware } from '@/middlewares/request-logger.middleware';
import { useController } from '@/utils/loader.util';
import sequelize from '@/configs/database.config';

class App {
	public app: Application;
	public port: number;
	public server: http.Server;
	private allowedCORS = [
		'http://localhost:5173',
		'http://localhost:5174',
		'http://localhost:3000',
		'http://localhost:3001',
		'https://eticketnepal.com',
		'https://www.eticketnepal.com'
	];

	constructor(port: number) {
		// process.env.TZ = 'Asia/Kathmandu';
		this.app = express();
		this.port = port;
		this.server = http.createServer(this.app);
		this.initializeMiddlewares();
		this.initializeControllers();
		this.initializeDatabase();
	}

	private initializeMiddlewares(): void {
		this.app.use(cors({ credentials: true, origin: this.allowedCORS }));
		this.app.use(helmet());
		this.app.use(removeNullFieldsMiddleware);
		this.app.use(loggingMiddleware);
		this.app.use(response);
		this.app.use(bodyParser.json());
	}

	private initializeControllers(): void {
		useController(this.app);

		this.app.get('*', (req: Request, res: Response) => {
			const url = req.originalUrl.split('?')[0];

			return res.status(404).send({
				message: `Route: (${url}) not found`,
			});
		});
	}

	private initializeDatabase(): void {
		sequelize
			.authenticate()
			.then(() => {
				console.log('Database connection has been established successfully.');
			})
			.catch((err) => {
				console.error('Unable to connect to the database:', err);
			});
	}

	public listenHttp(): void {
		this.server.listen(this.port, () => {
			console.log(`Server 🚀 is running on port http://localhost:${this.port}. Server timezone ${Intl.DateTimeFormat().resolvedOptions().timeZone}`);
		});
	}
}

export default App;
